{"sourceFile": "services/scripts/public/consultar-servicios/index.php", "activeCommit": 0, "commits": [{"activePatchIndex": 10, "patches": [{"date": 1757339966906, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1757343692707, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,55 +1,141 @@\n+<?php\n+// Configuración del IUE - puede venir por GET o estar hardcodeado\n+$iue = isset($_GET['iue']) ? $_GET['iue'] : 'HU7jaq4YHEe39T3zPUrn';\n+\n+// Función para realizar la consulta a la API\n+function consultarServicioAPI($numeroServicio, $password, $iue) {\n+    $url = \"https://api.saasargentina.com/v1/servicios/\" . urlencode($numeroServicio) . \"?iue=\" . urlencode($iue) . \"&pass=\" . urlencode($password);\n+\n+    $ch = curl_init();\n+    curl_setopt($ch, CURLOPT_URL, $url);\n+    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);\n+    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);\n+    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);\n+    curl_setopt($ch, CURLOPT_TIMEOUT, 30);\n+    curl_setopt($ch, CURLOPT_USERAGENT, 'SaaS Argentina Consultar Servicios Script');\n+\n+    $response = curl_exec($ch);\n+    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);\n+    $error = curl_error($ch);\n+    curl_close($ch);\n+\n+    if ($error) {\n+        return ['error' => true, 'message' => 'Error de conexión: ' . $error];\n+    }\n+\n+    if ($httpCode !== 200) {\n+        $errorMessages = [\n+            404 => 'Servicio no encontrado',\n+            401 => 'Acceso denegado. Verifique la contraseña',\n+            403 => 'Acceso denegado. Verifique la contraseña',\n+            500 => 'Error interno del servidor'\n+        ];\n+\n+        $message = isset($errorMessages[$httpCode]) ? $errorMessages[$httpCode] : 'Error HTTP: ' . $httpCode;\n+        return ['error' => true, 'message' => $message];\n+    }\n+\n+    $data = json_decode($response, true);\n+    if (json_last_error() !== JSON_ERROR_NONE) {\n+        return ['error' => true, 'message' => 'Error al procesar la respuesta del servidor'];\n+    }\n+\n+    return ['error' => false, 'data' => $data];\n+}\n+\n+// Manejar solicitudes AJAX\n+if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'consultar') {\n+    header('Content-Type: application/json');\n+\n+    $numeroServicio = trim($_POST['numeroServicio'] ?? '');\n+    $password = trim($_POST['password'] ?? '');\n+\n+    if (empty($numeroServicio)) {\n+        echo json_encode(['error' => true, 'message' => 'El número de servicio es requerido']);\n+        exit;\n+    }\n+\n+    if (empty($password)) {\n+        echo json_encode(['error' => true, 'message' => 'La contraseña es requerida']);\n+        exit;\n+    }\n+\n+    $resultado = consultarServicioAPI($numeroServicio, $password, $iue);\n+    echo json_encode($resultado);\n+    exit;\n+}\n+?>\n <!DOCTYPE html>\n-<html>\n+<html lang=\"es\">\n <head>\n+    <meta charset=\"UTF-8\">\n+    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n     <title>Consultar Servicios</title>\n-    <link rel=\"stylesheet\" href=\"bootstrap.min.css\">\n+    <link href=\"bootstrap.min.css\" rel=\"stylesheet\">\n     <style>\n         .servicio-card {\n-            border: 1px solid #ddd;\n-            border-radius: 8px;\n-            padding: 20px;\n-            margin: 20px 0;\n-            background-color: #f9f9f9;\n+            border: 1px solid #dee2e6;\n+            border-radius: 0.5rem;\n+            padding: 1.5rem;\n+            margin: 1.5rem 0;\n+            background-color: #f8f9fa;\n+            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\n         }\n         .servicio-field {\n-            margin-bottom: 10px;\n+            margin-bottom: 0.75rem;\n         }\n         .servicio-field strong {\n             display: inline-block;\n             width: 150px;\n-            color: #333;\n+            color: #495057;\n         }\n         .estado-badge {\n-            padding: 4px 8px;\n-            border-radius: 4px;\n-            font-size: 12px;\n-            font-weight: bold;\n+            padding: 0.375rem 0.75rem;\n+            border-radius: 0.375rem;\n+            font-size: 0.875rem;\n+            font-weight: 600;\n         }\n         .estado-sin-comenzar {\n-            background-color: #ffc107;\n-            color: #212529;\n+            background-color: #fff3cd;\n+            color: #664d03;\n+            border: 1px solid #ffecb5;\n         }\n         .estado-en-proceso {\n-            background-color: #17a2b8;\n-            color: white;\n+            background-color: #cff4fc;\n+            color: #055160;\n+            border: 1px solid #b6effb;\n         }\n         .estado-finalizado {\n-            background-color: #28a745;\n-            color: white;\n+            background-color: #d1e7dd;\n+            color: #0f5132;\n+            border: 1px solid #badbcc;\n         }\n         .prioridad-alta {\n             color: #dc3545;\n-            font-weight: bold;\n+            font-weight: 600;\n         }\n         .prioridad-media {\n-            color: #ffc107;\n-            font-weight: bold;\n+            color: #fd7e14;\n+            font-weight: 600;\n         }\n         .prioridad-baja {\n-            color: #28a745;\n-            font-weight: bold;\n+            color: #198754;\n+            font-weight: 600;\n         }\n+        .loading-spinner {\n+            display: inline-block;\n+            width: 1rem;\n+            height: 1rem;\n+            border: 0.125rem solid #f3f3f3;\n+            border-top: 0.125rem solid #0d6efd;\n+            border-radius: 50%;\n+            animation: spin 1s linear infinite;\n+        }\n+        @keyframes spin {\n+            0% { transform: rotate(0deg); }\n+            100% { transform: rotate(360deg); }\n+        }\n     </style>\n </head>\n <body>\n \n"}, {"date": 1757343727413, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -137,45 +137,62 @@\n         }\n     </style>\n </head>\n <body>\n+<div class=\"container py-4\">\n+    <div class=\"row justify-content-center\">\n+        <div class=\"col-lg-8\">\n+            <h1 class=\"mb-4 text-center\">Consultar Servicios</h1>\n \n-<div class=\"container\">\n-    <h1>Consultar Servicios</h1>\n+            <div class=\"card\">\n+                <div class=\"card-body\">\n+                    <form id=\"consultaForm\">\n+                        <div class=\"row\">\n+                            <div class=\"col-md-6\">\n+                                <div class=\"mb-3\">\n+                                    <label for=\"numeroServicio\" class=\"form-label\">Número de Servicio:</label>\n+                                    <input type=\"text\" name=\"numeroServicio\" id=\"numeroServicio\" class=\"form-control\" placeholder=\"Ingrese el número de servicio\" required>\n+                                </div>\n+                            </div>\n+                            <div class=\"col-md-6\">\n+                                <div class=\"mb-3\">\n+                                    <label for=\"password\" class=\"form-label\">Contraseña:</label>\n+                                    <input type=\"password\" name=\"password\" id=\"password\" class=\"form-control\" placeholder=\"Ingrese la contraseña\" required>\n+                                </div>\n+                            </div>\n+                        </div>\n \n-    <div class=\"row\">\n-        <div class=\"col-md-6\">\n-            <div class=\"form-group\">\n-                <label for=\"numeroServicio\">Número de Servicio:</label>\n-                <input type=\"text\" name=\"numeroServicio\" id=\"numeroServicio\" class=\"form-control\" placeholder=\"Ingrese el número de servicio\">\n+                        <div class=\"d-grid\">\n+                            <button type=\"submit\" id=\"consultar\" class=\"btn btn-primary\">\n+                                <span id=\"btnText\">Consultar Servicio</span>\n+                                <span id=\"btnSpinner\" class=\"loading-spinner ms-2\" style=\"display: none;\"></span>\n+                            </button>\n+                        </div>\n+                    </form>\n+                </div>\n             </div>\n+\n+            <div id=\"resultado\"></div>\n         </div>\n-        <div class=\"col-md-6\">\n-            <div class=\"form-group\">\n-                <label for=\"password\">Contraseña:</label>\n-                <input type=\"password\" name=\"password\" id=\"password\" class=\"form-control\" placeholder=\"Ingrese la contraseña\">\n-            </div>\n-        </div>\n     </div>\n-\n-    <div class=\"form-group\">\n-        <input type=\"submit\" name=\"consultar\" id=\"consultar\" value=\"Consultar Servicio\" onclick=\"consultarServicio()\" class=\"btn btn-primary\">\n-    </div>\n-\n-    <div id=\"resultado\"></div>\n </div>\n \n-<script src=\"jquery-2.2.4.min.js\"></script>\n+<script src=\"jquery-3.7.1.min.js\"></script>\n+<script src=\"bootstrap.bundle.min.js\"></script>\n <script>\n-    // Configuración del IUE - puede venir por GET o estar hardcodeado\n-    var iue = '<?php echo isset($_GET['iue']) ? $_GET['iue'] : 'HU7jaq4YHEe39T3zPUrn'; ?>';\n-\n     $(function() {\n         $(\"#numeroServicio\").focus();\n \n+        // Manejar el envío del formulario\n+        $(\"#consultaForm\").on('submit', function(e) {\n+            e.preventDefault();\n+            consultarServicio();\n+        });\n+\n         // Permitir consultar con Enter en ambos campos\n-        $(\"#numeroServicio, #password\").keypress(function(e) {\n-            if (e.keyCode == 13) {\n+        $(\"#numeroServicio, #password\").on('keypress', function(e) {\n+            if (e.which === 13) {\n+                e.preventDefault();\n                 consultarServicio();\n             }\n         });\n     });\n@@ -183,49 +200,64 @@\n     function consultarServicio() {\n         var numeroServicio = $(\"#numeroServicio\").val().trim();\n         var password = $(\"#password\").val().trim();\n         var resultado = $(\"#resultado\");\n+        var btnText = $(\"#btnText\");\n+        var btnSpinner = $(\"#btnSpinner\");\n+        var consultarBtn = $(\"#consultar\");\n \n         // Validaciones\n         if (!numeroServicio) {\n-            alert(\"Por favor ingrese el número de servicio\");\n+            mostrarAlerta(\"Por favor ingrese el número de servicio\", \"warning\");\n             $(\"#numeroServicio\").focus();\n             return;\n         }\n \n         if (!password) {\n-            alert(\"Por favor ingrese la contraseña\");\n+            mostrarAlerta(\"Por favor ingrese la contraseña\", \"warning\");\n             $(\"#password\").focus();\n             return;\n         }\n \n         // Mostrar indicador de carga\n-        var cargando = '<div class=\"text-center\"><img src=\"https://app.saasargentina.com/estilos/estilo_1/images/cargando.gif\"><br>Consultando servicio...</div>';\n-        resultado.html(cargando);\n+        btnText.text(\"Consultando...\");\n+        btnSpinner.show();\n+        consultarBtn.prop('disabled', true);\n+        resultado.html('<div class=\"text-center mt-3\"><div class=\"loading-spinner\"></div><br>Consultando servicio...</div>');\n \n-        // Construir URL de la API\n-        var url = 'https://api.saasargentina.com/v1/servicios/' + numeroServicio + '?iue=' + iue + '&pass=' + password;\n-\n-        // Realizar consulta a la API\n-        $.getJSON(url)\n-            .done(function(data) {\n-                if (data.estado && data.resultados && data.resultados.length > 0) {\n-                    mostrarServicio(data.resultados[0]);\n-                } else {\n-                    mostrarError(\"No se encontró el servicio o los datos de acceso son incorrectos\");\n-                }\n-            })\n-            .fail(function(jqxhr, textStatus, error) {\n-                var errorMsg = \"Error al consultar el servicio\";\n-                if (jqxhr.status === 404) {\n-                    errorMsg = \"Servicio no encontrado\";\n-                } else if (jqxhr.status === 401 || jqxhr.status === 403) {\n-                    errorMsg = \"Acceso denegado. Verifique la contraseña\";\n-                } else if (jqxhr.responseJSON && jqxhr.responseJSON.error) {\n-                    errorMsg = jqxhr.responseJSON.error;\n-                }\n-                mostrarError(errorMsg);\n-            });\n+        // Realizar consulta via AJAX al PHP local\n+        $.ajax({\n+            url: '',\n+            method: 'POST',\n+            data: {\n+                action: 'consultar',\n+                numeroServicio: numeroServicio,\n+                password: password\n+            },\n+            dataType: 'json'\n+        })\n+        .done(function(response) {\n+            if (response.error) {\n+                mostrarError(response.message);\n+            } else if (response.data && response.data.estado && response.data.resultados && response.data.resultados.length > 0) {\n+                mostrarServicio(response.data.resultados[0]);\n+            } else {\n+                mostrarError(\"No se encontró el servicio o los datos de acceso son incorrectos\");\n+            }\n+        })\n+        .fail(function(jqxhr, textStatus, error) {\n+            var errorMsg = \"Error al consultar el servicio\";\n+            if (jqxhr.responseJSON && jqxhr.responseJSON.message) {\n+                errorMsg = jqxhr.responseJSON.message;\n+            }\n+            mostrarError(errorMsg);\n+        })\n+        .always(function() {\n+            // Restaurar botón\n+            btnText.text(\"Consultar Servicio\");\n+            btnSpinner.hide();\n+            consultarBtn.prop('disabled', false);\n+        });\n     }\n \n     function mostrarServicio(servicio) {\n         var html = '<div class=\"servicio-card\">';\n"}, {"date": 1757343761598, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -259,23 +259,23 @@\n         });\n     }\n \n     function mostrarServicio(servicio) {\n-        var html = '<div class=\"servicio-card\">';\n-        html += '<h3>Servicio #' + servicio.idservicio + '</h3>';\n+        var html = '<div class=\"servicio-card mt-4\">';\n+        html += '<h3 class=\"mb-3 text-primary\">Servicio #' + escapeHtml(servicio.idservicio) + '</h3>';\n \n         if (servicio.titulo) {\n-            html += '<div class=\"servicio-field\"><strong>Título:</strong> ' + servicio.titulo + '</div>';\n+            html += '<div class=\"servicio-field\"><strong>Título:</strong> ' + escapeHtml(servicio.titulo) + '</div>';\n         }\n \n-        html += '<div class=\"servicio-field\"><strong>Estado:</strong> <span class=\"estado-badge ' + getEstadoClass(servicio.estado) + '\">' + servicio.estado + '</span></div>';\n+        html += '<div class=\"servicio-field\"><strong>Estado:</strong> <span class=\"estado-badge ' + getEstadoClass(servicio.estado) + '\">' + escapeHtml(servicio.estado) + '</span></div>';\n \n         if (servicio.prioridad) {\n             html += '<div class=\"servicio-field\"><strong>Prioridad:</strong> <span class=\"' + getPrioridadClass(servicio.prioridad) + '\">' + getPrioridadTexto(servicio.prioridad) + '</span></div>';\n         }\n \n-        html += '<div class=\"servicio-field\"><strong>Tipo:</strong> ' + (servicio.tiposervicio || 'No especificado') + '</div>';\n-        html += '<div class=\"servicio-field\"><strong>Cliente:</strong> ' + (servicio.cliente || 'No especificado') + '</div>';\n+        html += '<div class=\"servicio-field\"><strong>Tipo:</strong> ' + escapeHtml(servicio.tiposervicio || 'No especificado') + '</div>';\n+        html += '<div class=\"servicio-field\"><strong>Cliente:</strong> ' + escapeHtml(servicio.cliente || 'No especificado') + '</div>';\n \n         if (servicio.fechainicio) {\n             html += '<div class=\"servicio-field\"><strong>Fecha Inicio:</strong> ' + formatearFecha(servicio.fechainicio) + '</div>';\n         }\n@@ -290,35 +290,61 @@\n \n         html += '<div class=\"servicio-field\"><strong>Fecha Solicitado:</strong> ' + formatearFecha(servicio.fechasolicitado) + '</div>';\n \n         if (servicio.tiempodedicado && servicio.tiempodedicado !== '00:00:00') {\n-            html += '<div class=\"servicio-field\"><strong>Tiempo Dedicado:</strong> ' + servicio.tiempodedicado + '</div>';\n+            html += '<div class=\"servicio-field\"><strong>Tiempo Dedicado:</strong> ' + escapeHtml(servicio.tiempodedicado) + '</div>';\n         }\n \n         if (servicio.obssolicitado) {\n-            html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br>' + servicio.obssolicitado.replace(/\\n/g, '<br>') + '</div>';\n+            html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' + escapeHtml(servicio.obssolicitado).replace(/\\n/g, '<br>') + '</div></div>';\n         }\n \n         if (servicio.obsrealizado) {\n-            html += '<div class=\"servicio-field\"><strong>Observaciones Realizadas:</strong><br>' + servicio.obsrealizado.replace(/\\n/g, '<br>') + '</div>';\n+            html += '<div class=\"servicio-field\"><strong>Observaciones Realizadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' + escapeHtml(servicio.obsrealizado).replace(/\\n/g, '<br>') + '</div></div>';\n         }\n \n         if (servicio.saldo !== null && servicio.saldo !== undefined) {\n-            html += '<div class=\"servicio-field\"><strong>Saldo:</strong> $' + parseFloat(servicio.saldo).toFixed(2) + '</div>';\n+            html += '<div class=\"servicio-field\"><strong>Saldo:</strong> <span class=\"fw-bold\">$' + parseFloat(servicio.saldo).toFixed(2) + '</span></div>';\n         }\n \n         html += '</div>';\n \n         $(\"#resultado\").html(html);\n     }\n \n     function mostrarError(mensaje) {\n-        var html = '<div class=\"alert alert-danger\" role=\"alert\">';\n-        html += '<strong>Error:</strong> ' + mensaje;\n+        var html = '<div class=\"alert alert-danger mt-4\" role=\"alert\">';\n+        html += '<i class=\"bi bi-exclamation-triangle-fill me-2\"></i>';\n+        html += '<strong>Error:</strong> ' + escapeHtml(mensaje);\n         html += '</div>';\n         $(\"#resultado\").html(html);\n     }\n \n+    function mostrarAlerta(mensaje, tipo) {\n+        var alertClass = 'alert-info';\n+        var icon = 'bi-info-circle-fill';\n+\n+        switch(tipo) {\n+            case 'warning':\n+                alertClass = 'alert-warning';\n+                icon = 'bi-exclamation-triangle-fill';\n+                break;\n+            case 'success':\n+                alertClass = 'alert-success';\n+                icon = 'bi-check-circle-fill';\n+                break;\n+            case 'danger':\n+                alertClass = 'alert-danger';\n+                icon = 'bi-x-circle-fill';\n+                break;\n+        }\n+\n+        var html = '<div class=\"alert ' + alertClass + ' mt-4\" role=\"alert\">';\n+        html += '<i class=\"' + icon + ' me-2\"></i>' + escapeHtml(mensaje);\n+        html += '</div>';\n+        $(\"#resultado\").html(html);\n+    }\n+\n     function getEstadoClass(estado) {\n         if (!estado) return '';\n         var estadoLower = estado.toLowerCase();\n         if (estadoLower.includes('sin comenzar') || estadoLower.includes('pendiente')) {\n"}, {"date": 1757343785074, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -348,36 +348,73 @@\n         if (!estado) return '';\n         var estadoLower = estado.toLowerCase();\n         if (estadoLower.includes('sin comenzar') || estadoLower.includes('pendiente')) {\n             return 'estado-sin-comenzar';\n-        } else if (estadoLower.includes('proceso') || estadoLower.includes('progreso')) {\n+        } else if (estadoLower.includes('proceso') || estadoLower.includes('progreso') || estadoLower.includes('en curso')) {\n             return 'estado-en-proceso';\n-        } else if (estadoLower.includes('finalizado') || estadoLower.includes('completado') || estadoLower.includes('terminado')) {\n+        } else if (estadoLower.includes('finalizado') || estadoLower.includes('completado') || estadoLower.includes('terminado') || estadoLower.includes('cerrado')) {\n             return 'estado-finalizado';\n         }\n-        return '';\n+        return 'estado-sin-comenzar'; // Por defecto\n     }\n \n     function getPrioridadClass(prioridad) {\n         if (prioridad == '1') return 'prioridad-alta';\n         if (prioridad == '2') return 'prioridad-media';\n         if (prioridad == '3') return 'prioridad-baja';\n-        return '';\n+        return 'prioridad-media'; // Por defecto\n     }\n \n     function getPrioridadTexto(prioridad) {\n         if (prioridad == '1') return 'Alta';\n         if (prioridad == '2') return 'Media';\n         if (prioridad == '3') return 'Baja';\n-        return prioridad;\n+        return 'Media';\n     }\n \n     function formatearFecha(fecha) {\n         if (!fecha) return '';\n+\n+        // Intentar parsear la fecha\n         var date = new Date(fecha);\n+        if (isNaN(date.getTime())) {\n+            // Si no se puede parsear, intentar con formato específico\n+            var parts = fecha.split(' ');\n+            if (parts.length === 2) {\n+                var dateParts = parts[0].split('-');\n+                var timeParts = parts[1].split(':');\n+                if (dateParts.length === 3 && timeParts.length === 3) {\n+                    date = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], timeParts[0], timeParts[1], timeParts[2]);\n+                }\n+            }\n+        }\n+\n         if (isNaN(date.getTime())) return fecha;\n-        return date.toLocaleDateString('es-AR') + ' ' + date.toLocaleTimeString('es-AR');\n+\n+        // Formatear fecha en español\n+        var options = {\n+            year: 'numeric',\n+            month: '2-digit',\n+            day: '2-digit',\n+            hour: '2-digit',\n+            minute: '2-digit',\n+            hour12: false\n+        };\n+\n+        return date.toLocaleDateString('es-AR', options);\n     }\n+\n+    function escapeHtml(text) {\n+        if (!text) return '';\n+        var map = {\n+            '&': '&amp;',\n+            '<': '&lt;',\n+            '>': '&gt;',\n+            '\"': '&quot;',\n+            \"'\": '&#039;'\n+        };\n+        return text.toString().replace(/[&<>\"']/g, function(m) { return map[m]; });\n+    }\n </script>\n \n </body>\n </html>\n\\ No newline at end of file\n"}, {"date": 1757343954715, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,420 @@\n+<?php\n+// Configuración del IUE - puede venir por GET o estar hardcodeado\n+$iue = isset($_GET['iue']) ? $_GET['iue'] : 'HU7jaq4YHEe39T3zPUrn';\n+\n+// Función para realizar la consulta a la API\n+function consultarServicioAPI($numeroServicio, $password, $iue) {\n+    $url = \"https://api-beta.saasargentina.com/v1/servicios/\" . urlencode($numeroServicio) . \"?iue=\" . urlencode($iue) . \"&pass=\" . urlencode($password);\n+\n+    $ch = curl_init();\n+    curl_setopt($ch, CURLOPT_URL, $url);\n+    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);\n+    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);\n+    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);\n+    curl_setopt($ch, CURLOPT_TIMEOUT, 30);\n+    curl_setopt($ch, CURLOPT_USERAGENT, 'SaaS Argentina Consultar Servicios Script');\n+\n+    $response = curl_exec($ch);\n+    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);\n+    $error = curl_error($ch);\n+    curl_close($ch);\n+\n+    if ($error) {\n+        return ['error' => true, 'message' => 'Error de conexión: ' . $error];\n+    }\n+\n+    if ($httpCode !== 200) {\n+        $errorMessages = [\n+            404 => 'Servicio no encontrado',\n+            401 => 'Acceso denegado. Verifique la contraseña',\n+            403 => 'Acceso denegado. Verifique la contraseña',\n+            500 => 'Error interno del servidor'\n+        ];\n+\n+        $message = isset($errorMessages[$httpCode]) ? $errorMessages[$httpCode] : 'Error HTTP: ' . $httpCode;\n+        return ['error' => true, 'message' => $message];\n+    }\n+\n+    $data = json_decode($response, true);\n+    if (json_last_error() !== JSON_ERROR_NONE) {\n+        return ['error' => true, 'message' => 'Error al procesar la respuesta del servidor'];\n+    }\n+\n+    return ['error' => false, 'data' => $data];\n+}\n+\n+// Manejar solicitudes AJAX\n+if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'consultar') {\n+    header('Content-Type: application/json');\n+\n+    $numeroServicio = trim($_POST['numeroServicio'] ?? '');\n+    $password = trim($_POST['password'] ?? '');\n+\n+    if (empty($numeroServicio)) {\n+        echo json_encode(['error' => true, 'message' => 'El número de servicio es requerido']);\n+        exit;\n+    }\n+\n+    if (empty($password)) {\n+        echo json_encode(['error' => true, 'message' => 'La contraseña es requerida']);\n+        exit;\n+    }\n+\n+    $resultado = consultarServicioAPI($numeroServicio, $password, $iue);\n+    echo json_encode($resultado);\n+    exit;\n+}\n+?>\n+<!DOCTYPE html>\n+<html lang=\"es\">\n+<head>\n+    <meta charset=\"UTF-8\">\n+    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n+    <title>Consultar Servicios</title>\n+    <link href=\"bootstrap.min.css\" rel=\"stylesheet\">\n+    <style>\n+        .servicio-card {\n+            border: 1px solid #dee2e6;\n+            border-radius: 0.5rem;\n+            padding: 1.5rem;\n+            margin: 1.5rem 0;\n+            background-color: #f8f9fa;\n+            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\n+        }\n+        .servicio-field {\n+            margin-bottom: 0.75rem;\n+        }\n+        .servicio-field strong {\n+            display: inline-block;\n+            width: 150px;\n+            color: #495057;\n+        }\n+        .estado-badge {\n+            padding: 0.375rem 0.75rem;\n+            border-radius: 0.375rem;\n+            font-size: 0.875rem;\n+            font-weight: 600;\n+        }\n+        .estado-sin-comenzar {\n+            background-color: #fff3cd;\n+            color: #664d03;\n+            border: 1px solid #ffecb5;\n+        }\n+        .estado-en-proceso {\n+            background-color: #cff4fc;\n+            color: #055160;\n+            border: 1px solid #b6effb;\n+        }\n+        .estado-finalizado {\n+            background-color: #d1e7dd;\n+            color: #0f5132;\n+            border: 1px solid #badbcc;\n+        }\n+        .prioridad-alta {\n+            color: #dc3545;\n+            font-weight: 600;\n+        }\n+        .prioridad-media {\n+            color: #fd7e14;\n+            font-weight: 600;\n+        }\n+        .prioridad-baja {\n+            color: #198754;\n+            font-weight: 600;\n+        }\n+        .loading-spinner {\n+            display: inline-block;\n+            width: 1rem;\n+            height: 1rem;\n+            border: 0.125rem solid #f3f3f3;\n+            border-top: 0.125rem solid #0d6efd;\n+            border-radius: 50%;\n+            animation: spin 1s linear infinite;\n+        }\n+        @keyframes spin {\n+            0% { transform: rotate(0deg); }\n+            100% { transform: rotate(360deg); }\n+        }\n+    </style>\n+</head>\n+<body>\n+<div class=\"container py-4\">\n+    <div class=\"row justify-content-center\">\n+        <div class=\"col-lg-8\">\n+            <h1 class=\"mb-4 text-center\">Consultar Servicios</h1>\n+\n+            <div class=\"card\">\n+                <div class=\"card-body\">\n+                    <form id=\"consultaForm\">\n+                        <div class=\"row\">\n+                            <div class=\"col-md-6\">\n+                                <div class=\"mb-3\">\n+                                    <label for=\"numeroServicio\" class=\"form-label\">Número de Servicio:</label>\n+                                    <input type=\"text\" name=\"numeroServicio\" id=\"numeroServicio\" class=\"form-control\" placeholder=\"Ingrese el número de servicio\" required>\n+                                </div>\n+                            </div>\n+                            <div class=\"col-md-6\">\n+                                <div class=\"mb-3\">\n+                                    <label for=\"password\" class=\"form-label\">Contraseña:</label>\n+                                    <input type=\"password\" name=\"password\" id=\"password\" class=\"form-control\" placeholder=\"Ingrese la contraseña\" required>\n+                                </div>\n+                            </div>\n+                        </div>\n+\n+                        <div class=\"d-grid\">\n+                            <button type=\"submit\" id=\"consultar\" class=\"btn btn-primary\">\n+                                <span id=\"btnText\">Consultar Servicio</span>\n+                                <span id=\"btnSpinner\" class=\"loading-spinner ms-2\" style=\"display: none;\"></span>\n+                            </button>\n+                        </div>\n+                    </form>\n+                </div>\n+            </div>\n+\n+            <div id=\"resultado\"></div>\n+        </div>\n+    </div>\n+</div>\n+\n+<script src=\"jquery-3.7.1.min.js\"></script>\n+<script src=\"bootstrap.bundle.min.js\"></script>\n+<script>\n+    $(function() {\n+        $(\"#numeroServicio\").focus();\n+\n+        // Manejar el envío del formulario\n+        $(\"#consultaForm\").on('submit', function(e) {\n+            e.preventDefault();\n+            consultarServicio();\n+        });\n+\n+        // Permitir consultar con Enter en ambos campos\n+        $(\"#numeroServicio, #password\").on('keypress', function(e) {\n+            if (e.which === 13) {\n+                e.preventDefault();\n+                consultarServicio();\n+            }\n+        });\n+    });\n+\n+    function consultarServicio() {\n+        var numeroServicio = $(\"#numeroServicio\").val().trim();\n+        var password = $(\"#password\").val().trim();\n+        var resultado = $(\"#resultado\");\n+        var btnText = $(\"#btnText\");\n+        var btnSpinner = $(\"#btnSpinner\");\n+        var consultarBtn = $(\"#consultar\");\n+\n+        // Validaciones\n+        if (!numeroServicio) {\n+            mostrarAlerta(\"Por favor ingrese el número de servicio\", \"warning\");\n+            $(\"#numeroServicio\").focus();\n+            return;\n+        }\n+\n+        if (!password) {\n+            mostrarAlerta(\"Por favor ingrese la contraseña\", \"warning\");\n+            $(\"#password\").focus();\n+            return;\n+        }\n+\n+        // Mostrar indicador de carga\n+        btnText.text(\"Consultando...\");\n+        btnSpinner.show();\n+        consultarBtn.prop('disabled', true);\n+        resultado.html('<div class=\"text-center mt-3\"><div class=\"loading-spinner\"></div><br>Consultando servicio...</div>');\n+\n+        // Realizar consulta via AJAX al PHP local\n+        $.ajax({\n+            url: '',\n+            method: 'POST',\n+            data: {\n+                action: 'consultar',\n+                numeroServicio: numeroServicio,\n+                password: password\n+            },\n+            dataType: 'json'\n+        })\n+        .done(function(response) {\n+            if (response.error) {\n+                mostrarError(response.message);\n+            } else if (response.data && response.data.estado && response.data.resultados && response.data.resultados.length > 0) {\n+                mostrarServicio(response.data.resultados[0]);\n+            } else {\n+                mostrarError(\"No se encontró el servicio o los datos de acceso son incorrectos\");\n+            }\n+        })\n+        .fail(function(jqxhr, textStatus, error) {\n+            var errorMsg = \"Error al consultar el servicio\";\n+            if (jqxhr.responseJSON && jqxhr.responseJSON.message) {\n+                errorMsg = jqxhr.responseJSON.message;\n+            }\n+            mostrarError(errorMsg);\n+        })\n+        .always(function() {\n+            // Restaurar botón\n+            btnText.text(\"Consultar Servicio\");\n+            btnSpinner.hide();\n+            consultarBtn.prop('disabled', false);\n+        });\n+    }\n+\n+    function mostrarServicio(servicio) {\n+        var html = '<div class=\"servicio-card mt-4\">';\n+        html += '<h3 class=\"mb-3 text-primary\">Servicio #' + escapeHtml(servicio.idservicio) + '</h3>';\n+\n+        if (servicio.titulo) {\n+            html += '<div class=\"servicio-field\"><strong>Título:</strong> ' + escapeHtml(servicio.titulo) + '</div>';\n+        }\n+\n+        html += '<div class=\"servicio-field\"><strong>Estado:</strong> <span class=\"estado-badge ' + getEstadoClass(servicio.estado) + '\">' + escapeHtml(servicio.estado) + '</span></div>';\n+\n+        if (servicio.prioridad) {\n+            html += '<div class=\"servicio-field\"><strong>Prioridad:</strong> <span class=\"' + getPrioridadClass(servicio.prioridad) + '\">' + getPrioridadTexto(servicio.prioridad) + '</span></div>';\n+        }\n+\n+        html += '<div class=\"servicio-field\"><strong>Tipo:</strong> ' + escapeHtml(servicio.tiposervicio || 'No especificado') + '</div>';\n+        html += '<div class=\"servicio-field\"><strong>Cliente:</strong> ' + escapeHtml(servicio.cliente || 'No especificado') + '</div>';\n+\n+        if (servicio.fechainicio) {\n+            html += '<div class=\"servicio-field\"><strong>Fecha Inicio:</strong> ' + formatearFecha(servicio.fechainicio) + '</div>';\n+        }\n+\n+        if (servicio.fechafin) {\n+            html += '<div class=\"servicio-field\"><strong>Fecha Fin:</strong> ' + formatearFecha(servicio.fechafin) + '</div>';\n+        }\n+\n+        if (servicio.fechalimite) {\n+            html += '<div class=\"servicio-field\"><strong>Fecha Límite:</strong> ' + formatearFecha(servicio.fechalimite) + '</div>';\n+        }\n+\n+        html += '<div class=\"servicio-field\"><strong>Fecha Solicitado:</strong> ' + formatearFecha(servicio.fechasolicitado) + '</div>';\n+\n+        if (servicio.tiempodedicado && servicio.tiempodedicado !== '00:00:00') {\n+            html += '<div class=\"servicio-field\"><strong>Tiempo Dedicado:</strong> ' + escapeHtml(servicio.tiempodedicado) + '</div>';\n+        }\n+\n+        if (servicio.obssolicitado) {\n+            html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' + escapeHtml(servicio.obssolicitado).replace(/\\n/g, '<br>') + '</div></div>';\n+        }\n+\n+        if (servicio.obsrealizado) {\n+            html += '<div class=\"servicio-field\"><strong>Observaciones Realizadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' + escapeHtml(servicio.obsrealizado).replace(/\\n/g, '<br>') + '</div></div>';\n+        }\n+\n+        if (servicio.saldo !== null && servicio.saldo !== undefined) {\n+            html += '<div class=\"servicio-field\"><strong>Saldo:</strong> <span class=\"fw-bold\">$' + parseFloat(servicio.saldo).toFixed(2) + '</span></div>';\n+        }\n+\n+        html += '</div>';\n+\n+        $(\"#resultado\").html(html);\n+    }\n+\n+    function mostrarError(mensaje) {\n+        var html = '<div class=\"alert alert-danger mt-4\" role=\"alert\">';\n+        html += '<i class=\"bi bi-exclamation-triangle-fill me-2\"></i>';\n+        html += '<strong>Error:</strong> ' + escapeHtml(mensaje);\n+        html += '</div>';\n+        $(\"#resultado\").html(html);\n+    }\n+\n+    function mostrarAlerta(mensaje, tipo) {\n+        var alertClass = 'alert-info';\n+        var icon = 'bi-info-circle-fill';\n+\n+        switch(tipo) {\n+            case 'warning':\n+                alertClass = 'alert-warning';\n+                icon = 'bi-exclamation-triangle-fill';\n+                break;\n+            case 'success':\n+                alertClass = 'alert-success';\n+                icon = 'bi-check-circle-fill';\n+                break;\n+            case 'danger':\n+                alertClass = 'alert-danger';\n+                icon = 'bi-x-circle-fill';\n+                break;\n+        }\n+\n+        var html = '<div class=\"alert ' + alertClass + ' mt-4\" role=\"alert\">';\n+        html += '<i class=\"' + icon + ' me-2\"></i>' + escapeHtml(mensaje);\n+        html += '</div>';\n+        $(\"#resultado\").html(html);\n+    }\n+\n+    function getEstadoClass(estado) {\n+        if (!estado) return '';\n+        var estadoLower = estado.toLowerCase();\n+        if (estadoLower.includes('sin comenzar') || estadoLower.includes('pendiente')) {\n+            return 'estado-sin-comenzar';\n+        } else if (estadoLower.includes('proceso') || estadoLower.includes('progreso') || estadoLower.includes('en curso')) {\n+            return 'estado-en-proceso';\n+        } else if (estadoLower.includes('finalizado') || estadoLower.includes('completado') || estadoLower.includes('terminado') || estadoLower.includes('cerrado')) {\n+            return 'estado-finalizado';\n+        }\n+        return 'estado-sin-comenzar'; // Por defecto\n+    }\n+\n+    function getPrioridadClass(prioridad) {\n+        if (prioridad == '1') return 'prioridad-alta';\n+        if (prioridad == '2') return 'prioridad-media';\n+        if (prioridad == '3') return 'prioridad-baja';\n+        return 'prioridad-media'; // Por defecto\n+    }\n+\n+    function getPrioridadTexto(prioridad) {\n+        if (prioridad == '1') return 'Alta';\n+        if (prioridad == '2') return 'Media';\n+        if (prioridad == '3') return 'Baja';\n+        return 'Media';\n+    }\n+\n+    function formatearFecha(fecha) {\n+        if (!fecha) return '';\n+\n+        // Intentar parsear la fecha\n+        var date = new Date(fecha);\n+        if (isNaN(date.getTime())) {\n+            // Si no se puede parsear, intentar con formato específico\n+            var parts = fecha.split(' ');\n+            if (parts.length === 2) {\n+                var dateParts = parts[0].split('-');\n+                var timeParts = parts[1].split(':');\n+                if (dateParts.length === 3 && timeParts.length === 3) {\n+                    date = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], timeParts[0], timeParts[1], timeParts[2]);\n+                }\n+            }\n+        }\n+\n+        if (isNaN(date.getTime())) return fecha;\n+\n+        // Formatear fecha en español\n+        var options = {\n+            year: 'numeric',\n+            month: '2-digit',\n+            day: '2-digit',\n+            hour: '2-digit',\n+            minute: '2-digit',\n+            hour12: false\n+        };\n+\n+        return date.toLocaleDateString('es-AR', options);\n+    }\n+\n+    function escapeHtml(text) {\n+        if (!text) return '';\n+        var map = {\n+            '&': '&amp;',\n+            '<': '&lt;',\n+            '>': '&gt;',\n+            '\"': '&quot;',\n+            \"'\": '&#039;'\n+        };\n+        return text.toString().replace(/[&<>\"']/g, function(m) { return map[m]; });\n+    }\n+</script>\n+\n+</body>\n+</html>\n\\ No newline at end of file\n"}, {"date": 1757344711638, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -294,9 +294,9 @@\n             html += '<div class=\"servicio-field\"><strong>Tiempo Dedicado:</strong> ' + escapeHtml(servicio.tiempodedicado) + '</div>';\n         }\n \n         if (servicio.obssolicitado) {\n-            html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' + escapeHtml(servicio.obssolicitado).replace(/\\n/g, '<br>') + '</div></div>';\n+            html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' .charAt´+(servicio.obssolicitado).replace(/\\n/g, '<br>') + '</div></div>';\n         }\n \n         if (servicio.obsrealizado) {\n             html += '<div class=\"servicio-field\"><strong>Observaciones Realizadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' + escapeHtml(servicio.obsrealizado).replace(/\\n/g, '<br>') + '</div></div>';\n"}, {"date": 1757344740402, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -294,428 +294,8 @@\n             html += '<div class=\"servicio-field\"><strong>Tiempo Dedicado:</strong> ' + escapeHtml(servicio.tiempodedicado) + '</div>';\n         }\n \n         if (servicio.obssolicitado) {\n-            html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' .charAt´+(servicio.obssolicitado).replace(/\\n/g, '<br>') + '</div></div>';\n-        }\n-\n-        if (servicio.obsrealizado) {\n-            html += '<div class=\"servicio-field\"><strong>Observaciones Realizadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' + escapeHtml(servicio.obsrealizado).replace(/\\n/g, '<br>') + '</div></div>';\n-        }\n-\n-        if (servicio.saldo !== null && servicio.saldo !== undefined) {\n-            html += '<div class=\"servicio-field\"><strong>Saldo:</strong> <span class=\"fw-bold\">$' + parseFloat(servicio.saldo).toFixed(2) + '</span></div>';\n-        }\n-\n-        html += '</div>';\n-\n-        $(\"#resultado\").html(html);\n-    }\n-\n-    function mostrarError(mensaje) {\n-        var html = '<div class=\"alert alert-danger mt-4\" role=\"alert\">';\n-        html += '<i class=\"bi bi-exclamation-triangle-fill me-2\"></i>';\n-        html += '<strong>Error:</strong> ' + escapeHtml(mensaje);\n-        html += '</div>';\n-        $(\"#resultado\").html(html);\n-    }\n-\n-    function mostrarAlerta(mensaje, tipo) {\n-        var alertClass = 'alert-info';\n-        var icon = 'bi-info-circle-fill';\n-\n-        switch(tipo) {\n-            case 'warning':\n-                alertClass = 'alert-warning';\n-                icon = 'bi-exclamation-triangle-fill';\n-                break;\n-            case 'success':\n-                alertClass = 'alert-success';\n-                icon = 'bi-check-circle-fill';\n-                break;\n-            case 'danger':\n-                alertClass = 'alert-danger';\n-                icon = 'bi-x-circle-fill';\n-                break;\n-        }\n-\n-        var html = '<div class=\"alert ' + alertClass + ' mt-4\" role=\"alert\">';\n-        html += '<i class=\"' + icon + ' me-2\"></i>' + escapeHtml(mensaje);\n-        html += '</div>';\n-        $(\"#resultado\").html(html);\n-    }\n-\n-    function getEstadoClass(estado) {\n-        if (!estado) return '';\n-        var estadoLower = estado.toLowerCase();\n-        if (estadoLower.includes('sin comenzar') || estadoLower.includes('pendiente')) {\n-            return 'estado-sin-comenzar';\n-        } else if (estadoLower.includes('proceso') || estadoLower.includes('progreso') || estadoLower.includes('en curso')) {\n-            return 'estado-en-proceso';\n-        } else if (estadoLower.includes('finalizado') || estadoLower.includes('completado') || estadoLower.includes('terminado') || estadoLower.includes('cerrado')) {\n-            return 'estado-finalizado';\n-        }\n-        return 'estado-sin-comenzar'; // Por defecto\n-    }\n-\n-    function getPrioridadClass(prioridad) {\n-        if (prioridad == '1') return 'prioridad-alta';\n-        if (prioridad == '2') return 'prioridad-media';\n-        if (prioridad == '3') return 'prioridad-baja';\n-        return 'prioridad-media'; // Por defecto\n-    }\n-\n-    function getPrioridadTexto(prioridad) {\n-        if (prioridad == '1') return 'Alta';\n-        if (prioridad == '2') return 'Media';\n-        if (prioridad == '3') return 'Baja';\n-        return 'Media';\n-    }\n-\n-    function formatearFecha(fecha) {\n-        if (!fecha) return '';\n-\n-        // Intentar parsear la fecha\n-        var date = new Date(fecha);\n-        if (isNaN(date.getTime())) {\n-            // Si no se puede parsear, intentar con formato específico\n-            var parts = fecha.split(' ');\n-            if (parts.length === 2) {\n-                var dateParts = parts[0].split('-');\n-                var timeParts = parts[1].split(':');\n-                if (dateParts.length === 3 && timeParts.length === 3) {\n-                    date = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], timeParts[0], timeParts[1], timeParts[2]);\n-                }\n-            }\n-        }\n-\n-        if (isNaN(date.getTime())) return fecha;\n-\n-        // Formatear fecha en español\n-        var options = {\n-            year: 'numeric',\n-            month: '2-digit',\n-            day: '2-digit',\n-            hour: '2-digit',\n-            minute: '2-digit',\n-            hour12: false\n-        };\n-\n-        return date.toLocaleDateString('es-AR', options);\n-    }\n-\n-    function escapeHtml(text) {\n-        if (!text) return '';\n-        var map = {\n-            '&': '&amp;',\n-            '<': '&lt;',\n-            '>': '&gt;',\n-            '\"': '&quot;',\n-            \"'\": '&#039;'\n-        };\n-        return text.toString().replace(/[&<>\"']/g, function(m) { return map[m]; });\n-    }\n-</script>\n-\n-</body>\n-</html>\n-<?php\n-// Configuración del IUE - puede venir por GET o estar hardcodeado\n-$iue = isset($_GET['iue']) ? $_GET['iue'] : 'HU7jaq4YHEe39T3zPUrn';\n-\n-// Función para realizar la consulta a la API\n-function consultarServicioAPI($numeroServicio, $password, $iue) {\n-    $url = \"https://api.saasargentina.com/v1/servicios/\" . urlencode($numeroServicio) . \"?iue=\" . urlencode($iue) . \"&pass=\" . urlencode($password);\n-\n-    $ch = curl_init();\n-    curl_setopt($ch, CURLOPT_URL, $url);\n-    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);\n-    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);\n-    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);\n-    curl_setopt($ch, CURLOPT_TIMEOUT, 30);\n-    curl_setopt($ch, CURLOPT_USERAGENT, 'SaaS Argentina Consultar Servicios Script');\n-\n-    $response = curl_exec($ch);\n-    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);\n-    $error = curl_error($ch);\n-    curl_close($ch);\n-\n-    if ($error) {\n-        return ['error' => true, 'message' => 'Error de conexión: ' . $error];\n-    }\n-\n-    if ($httpCode !== 200) {\n-        $errorMessages = [\n-            404 => 'Servicio no encontrado',\n-            401 => 'Acceso denegado. Verifique la contraseña',\n-            403 => 'Acceso denegado. Verifique la contraseña',\n-            500 => 'Error interno del servidor'\n-        ];\n-\n-        $message = isset($errorMessages[$httpCode]) ? $errorMessages[$httpCode] : 'Error HTTP: ' . $httpCode;\n-        return ['error' => true, 'message' => $message];\n-    }\n-\n-    $data = json_decode($response, true);\n-    if (json_last_error() !== JSON_ERROR_NONE) {\n-        return ['error' => true, 'message' => 'Error al procesar la respuesta del servidor'];\n-    }\n-\n-    return ['error' => false, 'data' => $data];\n-}\n-\n-// Manejar solicitudes AJAX\n-if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'consultar') {\n-    header('Content-Type: application/json');\n-\n-    $numeroServicio = trim($_POST['numeroServicio'] ?? '');\n-    $password = trim($_POST['password'] ?? '');\n-\n-    if (empty($numeroServicio)) {\n-        echo json_encode(['error' => true, 'message' => 'El número de servicio es requerido']);\n-        exit;\n-    }\n-\n-    if (empty($password)) {\n-        echo json_encode(['error' => true, 'message' => 'La contraseña es requerida']);\n-        exit;\n-    }\n-\n-    $resultado = consultarServicioAPI($numeroServicio, $password, $iue);\n-    echo json_encode($resultado);\n-    exit;\n-}\n-?>\n-<!DOCTYPE html>\n-<html lang=\"es\">\n-<head>\n-    <meta charset=\"UTF-8\">\n-    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n-    <title>Consultar Servicios</title>\n-    <link href=\"bootstrap.min.css\" rel=\"stylesheet\">\n-    <style>\n-        .servicio-card {\n-            border: 1px solid #dee2e6;\n-            border-radius: 0.5rem;\n-            padding: 1.5rem;\n-            margin: 1.5rem 0;\n-            background-color: #f8f9fa;\n-            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\n-        }\n-        .servicio-field {\n-            margin-bottom: 0.75rem;\n-        }\n-        .servicio-field strong {\n-            display: inline-block;\n-            width: 150px;\n-            color: #495057;\n-        }\n-        .estado-badge {\n-            padding: 0.375rem 0.75rem;\n-            border-radius: 0.375rem;\n-            font-size: 0.875rem;\n-            font-weight: 600;\n-        }\n-        .estado-sin-comenzar {\n-            background-color: #fff3cd;\n-            color: #664d03;\n-            border: 1px solid #ffecb5;\n-        }\n-        .estado-en-proceso {\n-            background-color: #cff4fc;\n-            color: #055160;\n-            border: 1px solid #b6effb;\n-        }\n-        .estado-finalizado {\n-            background-color: #d1e7dd;\n-            color: #0f5132;\n-            border: 1px solid #badbcc;\n-        }\n-        .prioridad-alta {\n-            color: #dc3545;\n-            font-weight: 600;\n-        }\n-        .prioridad-media {\n-            color: #fd7e14;\n-            font-weight: 600;\n-        }\n-        .prioridad-baja {\n-            color: #198754;\n-            font-weight: 600;\n-        }\n-        .loading-spinner {\n-            display: inline-block;\n-            width: 1rem;\n-            height: 1rem;\n-            border: 0.125rem solid #f3f3f3;\n-            border-top: 0.125rem solid #0d6efd;\n-            border-radius: 50%;\n-            animation: spin 1s linear infinite;\n-        }\n-        @keyframes spin {\n-            0% { transform: rotate(0deg); }\n-            100% { transform: rotate(360deg); }\n-        }\n-    </style>\n-</head>\n-<body>\n-<div class=\"container py-4\">\n-    <div class=\"row justify-content-center\">\n-        <div class=\"col-lg-8\">\n-            <h1 class=\"mb-4 text-center\">Consultar Servicios</h1>\n-\n-            <div class=\"card\">\n-                <div class=\"card-body\">\n-                    <form id=\"consultaForm\">\n-                        <div class=\"row\">\n-                            <div class=\"col-md-6\">\n-                                <div class=\"mb-3\">\n-                                    <label for=\"numeroServicio\" class=\"form-label\">Número de Servicio:</label>\n-                                    <input type=\"text\" name=\"numeroServicio\" id=\"numeroServicio\" class=\"form-control\" placeholder=\"Ingrese el número de servicio\" required>\n-                                </div>\n-                            </div>\n-                            <div class=\"col-md-6\">\n-                                <div class=\"mb-3\">\n-                                    <label for=\"password\" class=\"form-label\">Contraseña:</label>\n-                                    <input type=\"password\" name=\"password\" id=\"password\" class=\"form-control\" placeholder=\"Ingrese la contraseña\" required>\n-                                </div>\n-                            </div>\n-                        </div>\n-\n-                        <div class=\"d-grid\">\n-                            <button type=\"submit\" id=\"consultar\" class=\"btn btn-primary\">\n-                                <span id=\"btnText\">Consultar Servicio</span>\n-                                <span id=\"btnSpinner\" class=\"loading-spinner ms-2\" style=\"display: none;\"></span>\n-                            </button>\n-                        </div>\n-                    </form>\n-                </div>\n-            </div>\n-\n-            <div id=\"resultado\"></div>\n-        </div>\n-    </div>\n-</div>\n-\n-<script src=\"jquery-3.7.1.min.js\"></script>\n-<script src=\"bootstrap.bundle.min.js\"></script>\n-<script>\n-    $(function() {\n-        $(\"#numeroServicio\").focus();\n-\n-        // Manejar el envío del formulario\n-        $(\"#consultaForm\").on('submit', function(e) {\n-            e.preventDefault();\n-            consultarServicio();\n-        });\n-\n-        // Permitir consultar con Enter en ambos campos\n-        $(\"#numeroServicio, #password\").on('keypress', function(e) {\n-            if (e.which === 13) {\n-                e.preventDefault();\n-                consultarServicio();\n-            }\n-        });\n-    });\n-\n-    function consultarServicio() {\n-        var numeroServicio = $(\"#numeroServicio\").val().trim();\n-        var password = $(\"#password\").val().trim();\n-        var resultado = $(\"#resultado\");\n-        var btnText = $(\"#btnText\");\n-        var btnSpinner = $(\"#btnSpinner\");\n-        var consultarBtn = $(\"#consultar\");\n-\n-        // Validaciones\n-        if (!numeroServicio) {\n-            mostrarAlerta(\"Por favor ingrese el número de servicio\", \"warning\");\n-            $(\"#numeroServicio\").focus();\n-            return;\n-        }\n-\n-        if (!password) {\n-            mostrarAlerta(\"Por favor ingrese la contraseña\", \"warning\");\n-            $(\"#password\").focus();\n-            return;\n-        }\n-\n-        // Mostrar indicador de carga\n-        btnText.text(\"Consultando...\");\n-        btnSpinner.show();\n-        consultarBtn.prop('disabled', true);\n-        resultado.html('<div class=\"text-center mt-3\"><div class=\"loading-spinner\"></div><br>Consultando servicio...</div>');\n-\n-        // Realizar consulta via AJAX al PHP local\n-        $.ajax({\n-            url: '',\n-            method: 'POST',\n-            data: {\n-                action: 'consultar',\n-                numeroServicio: numeroServicio,\n-                password: password\n-            },\n-            dataType: 'json'\n-        })\n-        .done(function(response) {\n-            if (response.error) {\n-                mostrarError(response.message);\n-            } else if (response.data && response.data.estado && response.data.resultados && response.data.resultados.length > 0) {\n-                mostrarServicio(response.data.resultados[0]);\n-            } else {\n-                mostrarError(\"No se encontró el servicio o los datos de acceso son incorrectos\");\n-            }\n-        })\n-        .fail(function(jqxhr, textStatus, error) {\n-            var errorMsg = \"Error al consultar el servicio\";\n-            if (jqxhr.responseJSON && jqxhr.responseJSON.message) {\n-                errorMsg = jqxhr.responseJSON.message;\n-            }\n-            mostrarError(errorMsg);\n-        })\n-        .always(function() {\n-            // Restaurar botón\n-            btnText.text(\"Consultar Servicio\");\n-            btnSpinner.hide();\n-            consultarBtn.prop('disabled', false);\n-        });\n-    }\n-\n-    function mostrarServicio(servicio) {\n-        var html = '<div class=\"servicio-card mt-4\">';\n-        html += '<h3 class=\"mb-3 text-primary\">Servicio #' + escapeHtml(servicio.idservicio) + '</h3>';\n-\n-        if (servicio.titulo) {\n-            html += '<div class=\"servicio-field\"><strong>Título:</strong> ' + escapeHtml(servicio.titulo) + '</div>';\n-        }\n-\n-        html += '<div class=\"servicio-field\"><strong>Estado:</strong> <span class=\"estado-badge ' + getEstadoClass(servicio.estado) + '\">' + escapeHtml(servicio.estado) + '</span></div>';\n-\n-        if (servicio.prioridad) {\n-            html += '<div class=\"servicio-field\"><strong>Prioridad:</strong> <span class=\"' + getPrioridadClass(servicio.prioridad) + '\">' + getPrioridadTexto(servicio.prioridad) + '</span></div>';\n-        }\n-\n-        html += '<div class=\"servicio-field\"><strong>Tipo:</strong> ' + escapeHtml(servicio.tiposervicio || 'No especificado') + '</div>';\n-        html += '<div class=\"servicio-field\"><strong>Cliente:</strong> ' + escapeHtml(servicio.cliente || 'No especificado') + '</div>';\n-\n-        if (servicio.fechainicio) {\n-            html += '<div class=\"servicio-field\"><strong>Fecha Inicio:</strong> ' + formatearFecha(servicio.fechainicio) + '</div>';\n-        }\n-\n-        if (servicio.fechafin) {\n-            html += '<div class=\"servicio-field\"><strong>Fecha Fin:</strong> ' + formatearFecha(servicio.fechafin) + '</div>';\n-        }\n-\n-        if (servicio.fechalimite) {\n-            html += '<div class=\"servicio-field\"><strong>Fecha Límite:</strong> ' + formatearFecha(servicio.fechalimite) + '</div>';\n-        }\n-\n-        html += '<div class=\"servicio-field\"><strong>Fecha Solicitado:</strong> ' + formatearFecha(servicio.fechasolicitado) + '</div>';\n-\n-        if (servicio.tiempodedicado && servicio.tiempodedicado !== '00:00:00') {\n-            html += '<div class=\"servicio-field\"><strong>Tiempo Dedicado:</strong> ' + escapeHtml(servicio.tiempodedicado) + '</div>';\n-        }\n-\n-        if (servicio.obssolicitado) {\n             html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' + escapeHtml(servicio.obssolicitado).replace(/\\n/g, '<br>') + '</div></div>';\n         }\n \n         if (servicio.obsrealizado) {\n"}, {"date": 1757344836702, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -294,13 +294,13 @@\n             html += '<div class=\"servicio-field\"><strong>Tiempo Dedicado:</strong> ' + escapeHtml(servicio.tiempodedicado) + '</div>';\n         }\n \n         if (servicio.obssolicitado) {\n-            html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' + escapeHtml(servicio.obssolicitado).replace(/\\n/g, '<br>') + '</div></div>';\n+            html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br><div class=\"mt-2 p-3 bg-light rounded border observaciones-content\">' + formatearObservaciones(servicio.obssolicitado) + '</div></div>';\n         }\n \n         if (servicio.obsrealizado) {\n-            html += '<div class=\"servicio-field\"><strong>Observaciones Realizadas:</strong><br><div class=\"mt-2 p-2 bg-light rounded\">' + escapeHtml(servicio.obsrealizado).replace(/\\n/g, '<br>') + '</div></div>';\n+            html += '<div class=\"servicio-field\"><strong>Observaciones Realizadas:</strong><br><div class=\"mt-2 p-3 bg-light rounded border observaciones-content\">' + formatearObservaciones(servicio.obsrealizado) + '</div></div>';\n         }\n \n         if (servicio.saldo !== null && servicio.saldo !== undefined) {\n             html += '<div class=\"servicio-field\"><strong>Saldo:</strong> <span class=\"fw-bold\">$' + parseFloat(servicio.saldo).toFixed(2) + '</span></div>';\n"}, {"date": 1757344852314, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -134,8 +134,41 @@\n         @keyframes spin {\n             0% { transform: rotate(0deg); }\n             100% { transform: rotate(360deg); }\n         }\n+        .observaciones-content {\n+            line-height: 1.6;\n+            font-size: 0.95rem;\n+        }\n+        .observaciones-content p {\n+            margin-bottom: 0.75rem;\n+        }\n+        .observaciones-content p:last-child {\n+            margin-bottom: 0;\n+        }\n+        .observaciones-content ul, .observaciones-content ol {\n+            margin-bottom: 0.75rem;\n+            padding-left: 1.5rem;\n+        }\n+        .observaciones-content li {\n+            margin-bottom: 0.25rem;\n+        }\n+        .observaciones-content strong, .observaciones-content b {\n+            color: #495057;\n+        }\n+        .observaciones-content em, .observaciones-content i {\n+            color: #6c757d;\n+        }\n+        .observaciones-content h1, .observaciones-content h2, .observaciones-content h3,\n+        .observaciones-content h4, .observaciones-content h5, .observaciones-content h6 {\n+            margin-top: 1rem;\n+            margin-bottom: 0.5rem;\n+            color: #495057;\n+        }\n+        .observaciones-content h1 { font-size: 1.25rem; }\n+        .observaciones-content h2 { font-size: 1.15rem; }\n+        .observaciones-content h3 { font-size: 1.1rem; }\n+        .observaciones-content h4, .observaciones-content h5, .observaciones-content h6 { font-size: 1rem; }\n     </style>\n </head>\n <body>\n <div class=\"container py-4\">\n"}, {"date": 1757344873227, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -435,8 +435,56 @@\n \n         return date.toLocaleDateString('es-AR', options);\n     }\n \n+    function formatearObservaciones(texto) {\n+        if (!texto) return '';\n+\n+        // Detectar si el texto contiene HTML válido\n+        var htmlRegex = /<[^>]+>/;\n+        var hasHTML = htmlRegex.test(texto);\n+\n+        if (hasHTML) {\n+            // Si contiene HTML, sanitizar solo elementos peligrosos pero mantener el formato\n+            var sanitized = texto\n+                // Remover scripts y elementos peligrosos\n+                .replace(/<script[^>]*>.*?<\\/script>/gi, '')\n+                .replace(/<iframe[^>]*>.*?<\\/iframe>/gi, '')\n+                .replace(/<object[^>]*>.*?<\\/object>/gi, '')\n+                .replace(/<embed[^>]*>/gi, '')\n+                .replace(/<link[^>]*>/gi, '')\n+                .replace(/<meta[^>]*>/gi, '')\n+                .replace(/javascript:/gi, '')\n+                .replace(/on\\w+\\s*=/gi, '');\n+\n+            // Limpiar HTML mal formado y agregar párrafos si es necesario\n+            sanitized = sanitized.trim();\n+\n+            // Si no tiene etiquetas de párrafo, convertir saltos de línea\n+            if (!/<p[^>]*>/.test(sanitized) && !/<div[^>]*>/.test(sanitized) && !/<br[^>]*>/.test(sanitized)) {\n+                sanitized = sanitized.replace(/\\n\\s*\\n/g, '</p><p>').replace(/\\n/g, '<br>');\n+                if (!sanitized.startsWith('<p>')) {\n+                    sanitized = '<p>' + sanitized + '</p>';\n+                }\n+            }\n+\n+            return sanitized;\n+        } else {\n+            // Si no contiene HTML, escapar y convertir saltos de línea\n+            var escaped = escapeHtml(texto);\n+\n+            // Convertir dobles saltos de línea en párrafos\n+            var formatted = escaped.replace(/\\n\\s*\\n/g, '</p><p>').replace(/\\n/g, '<br>');\n+\n+            // Envolver en párrafos si no está vacío\n+            if (formatted.trim()) {\n+                formatted = '<p>' + formatted + '</p>';\n+            }\n+\n+            return formatted;\n+        }\n+    }\n+\n     function escapeHtml(text) {\n         if (!text) return '';\n         var map = {\n             '&': '&amp;',\n"}], "date": 1757339966906, "name": "Commit-0", "content": "<!DOCTYPE html>\n<html>\n<head>\n    <title>Consultar Servicios</title>\n    <link rel=\"stylesheet\" href=\"bootstrap.min.css\">\n    <style>\n        .servicio-card {\n            border: 1px solid #ddd;\n            border-radius: 8px;\n            padding: 20px;\n            margin: 20px 0;\n            background-color: #f9f9f9;\n        }\n        .servicio-field {\n            margin-bottom: 10px;\n        }\n        .servicio-field strong {\n            display: inline-block;\n            width: 150px;\n            color: #333;\n        }\n        .estado-badge {\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-size: 12px;\n            font-weight: bold;\n        }\n        .estado-sin-comenzar {\n            background-color: #ffc107;\n            color: #212529;\n        }\n        .estado-en-proceso {\n            background-color: #17a2b8;\n            color: white;\n        }\n        .estado-finalizado {\n            background-color: #28a745;\n            color: white;\n        }\n        .prioridad-alta {\n            color: #dc3545;\n            font-weight: bold;\n        }\n        .prioridad-media {\n            color: #ffc107;\n            font-weight: bold;\n        }\n        .prioridad-baja {\n            color: #28a745;\n            font-weight: bold;\n        }\n    </style>\n</head>\n<body>\n\n<div class=\"container\">\n    <h1>Consultar Servicios</h1>\n\n    <div class=\"row\">\n        <div class=\"col-md-6\">\n            <div class=\"form-group\">\n                <label for=\"numeroServicio\">Número de Servicio:</label>\n                <input type=\"text\" name=\"numeroServicio\" id=\"numeroServicio\" class=\"form-control\" placeholder=\"Ingrese el número de servicio\">\n            </div>\n        </div>\n        <div class=\"col-md-6\">\n            <div class=\"form-group\">\n                <label for=\"password\">Contraseña:</label>\n                <input type=\"password\" name=\"password\" id=\"password\" class=\"form-control\" placeholder=\"Ingrese la contraseña\">\n            </div>\n        </div>\n    </div>\n\n    <div class=\"form-group\">\n        <input type=\"submit\" name=\"consultar\" id=\"consultar\" value=\"Consultar Servicio\" onclick=\"consultarServicio()\" class=\"btn btn-primary\">\n    </div>\n\n    <div id=\"resultado\"></div>\n</div>\n\n<script src=\"jquery-2.2.4.min.js\"></script>\n<script>\n    // Configuración del IUE - puede venir por GET o estar hardcodeado\n    var iue = '<?php echo isset($_GET['iue']) ? $_GET['iue'] : 'HU7jaq4YHEe39T3zPUrn'; ?>';\n\n    $(function() {\n        $(\"#numeroServicio\").focus();\n\n        // Permitir consultar con Enter en ambos campos\n        $(\"#numeroServicio, #password\").keypress(function(e) {\n            if (e.keyCode == 13) {\n                consultarServicio();\n            }\n        });\n    });\n\n    function consultarServicio() {\n        var numeroServicio = $(\"#numeroServicio\").val().trim();\n        var password = $(\"#password\").val().trim();\n        var resultado = $(\"#resultado\");\n\n        // Validaciones\n        if (!numeroServicio) {\n            alert(\"Por favor ingrese el número de servicio\");\n            $(\"#numeroServicio\").focus();\n            return;\n        }\n\n        if (!password) {\n            alert(\"Por favor ingrese la contraseña\");\n            $(\"#password\").focus();\n            return;\n        }\n\n        // Mostrar indicador de carga\n        var cargando = '<div class=\"text-center\"><img src=\"https://app.saasargentina.com/estilos/estilo_1/images/cargando.gif\"><br>Consultando servicio...</div>';\n        resultado.html(cargando);\n\n        // Construir URL de la API\n        var url = 'https://api.saasargentina.com/v1/servicios/' + numeroServicio + '?iue=' + iue + '&pass=' + password;\n\n        // Realizar consulta a la API\n        $.getJSON(url)\n            .done(function(data) {\n                if (data.estado && data.resultados && data.resultados.length > 0) {\n                    mostrarServicio(data.resultados[0]);\n                } else {\n                    mostrarError(\"No se encontró el servicio o los datos de acceso son incorrectos\");\n                }\n            })\n            .fail(function(jqxhr, textStatus, error) {\n                var errorMsg = \"Error al consultar el servicio\";\n                if (jqxhr.status === 404) {\n                    errorMsg = \"Servicio no encontrado\";\n                } else if (jqxhr.status === 401 || jqxhr.status === 403) {\n                    errorMsg = \"Acceso denegado. Verifique la contraseña\";\n                } else if (jqxhr.responseJSON && jqxhr.responseJSON.error) {\n                    errorMsg = jqxhr.responseJSON.error;\n                }\n                mostrarError(errorMsg);\n            });\n    }\n\n    function mostrarServicio(servicio) {\n        var html = '<div class=\"servicio-card\">';\n        html += '<h3>Servicio #' + servicio.idservicio + '</h3>';\n\n        if (servicio.titulo) {\n            html += '<div class=\"servicio-field\"><strong>Título:</strong> ' + servicio.titulo + '</div>';\n        }\n\n        html += '<div class=\"servicio-field\"><strong>Estado:</strong> <span class=\"estado-badge ' + getEstadoClass(servicio.estado) + '\">' + servicio.estado + '</span></div>';\n\n        if (servicio.prioridad) {\n            html += '<div class=\"servicio-field\"><strong>Prioridad:</strong> <span class=\"' + getPrioridadClass(servicio.prioridad) + '\">' + getPrioridadTexto(servicio.prioridad) + '</span></div>';\n        }\n\n        html += '<div class=\"servicio-field\"><strong>Tipo:</strong> ' + (servicio.tiposervicio || 'No especificado') + '</div>';\n        html += '<div class=\"servicio-field\"><strong>Cliente:</strong> ' + (servicio.cliente || 'No especificado') + '</div>';\n\n        if (servicio.fechainicio) {\n            html += '<div class=\"servicio-field\"><strong>Fecha Inicio:</strong> ' + formatearFecha(servicio.fechainicio) + '</div>';\n        }\n\n        if (servicio.fechafin) {\n            html += '<div class=\"servicio-field\"><strong>Fecha Fin:</strong> ' + formatearFecha(servicio.fechafin) + '</div>';\n        }\n\n        if (servicio.fechalimite) {\n            html += '<div class=\"servicio-field\"><strong>Fecha Límite:</strong> ' + formatearFecha(servicio.fechalimite) + '</div>';\n        }\n\n        html += '<div class=\"servicio-field\"><strong>Fecha Solicitado:</strong> ' + formatearFecha(servicio.fechasolicitado) + '</div>';\n\n        if (servicio.tiempodedicado && servicio.tiempodedicado !== '00:00:00') {\n            html += '<div class=\"servicio-field\"><strong>Tiempo Dedicado:</strong> ' + servicio.tiempodedicado + '</div>';\n        }\n\n        if (servicio.obssolicitado) {\n            html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br>' + servicio.obssolicitado.replace(/\\n/g, '<br>') + '</div>';\n        }\n\n        if (servicio.obsrealizado) {\n            html += '<div class=\"servicio-field\"><strong>Observaciones Realizadas:</strong><br>' + servicio.obsrealizado.replace(/\\n/g, '<br>') + '</div>';\n        }\n\n        if (servicio.saldo !== null && servicio.saldo !== undefined) {\n            html += '<div class=\"servicio-field\"><strong>Saldo:</strong> $' + parseFloat(servicio.saldo).toFixed(2) + '</div>';\n        }\n\n        html += '</div>';\n\n        $(\"#resultado\").html(html);\n    }\n\n    function mostrarError(mensaje) {\n        var html = '<div class=\"alert alert-danger\" role=\"alert\">';\n        html += '<strong>Error:</strong> ' + mensaje;\n        html += '</div>';\n        $(\"#resultado\").html(html);\n    }\n\n    function getEstadoClass(estado) {\n        if (!estado) return '';\n        var estadoLower = estado.toLowerCase();\n        if (estadoLower.includes('sin comenzar') || estadoLower.includes('pendiente')) {\n            return 'estado-sin-comenzar';\n        } else if (estadoLower.includes('proceso') || estadoLower.includes('progreso')) {\n            return 'estado-en-proceso';\n        } else if (estadoLower.includes('finalizado') || estadoLower.includes('completado') || estadoLower.includes('terminado')) {\n            return 'estado-finalizado';\n        }\n        return '';\n    }\n\n    function getPrioridadClass(prioridad) {\n        if (prioridad == '1') return 'prioridad-alta';\n        if (prioridad == '2') return 'prioridad-media';\n        if (prioridad == '3') return 'prioridad-baja';\n        return '';\n    }\n\n    function getPrioridadTexto(prioridad) {\n        if (prioridad == '1') return 'Alta';\n        if (prioridad == '2') return 'Media';\n        if (prioridad == '3') return 'Baja';\n        return prioridad;\n    }\n\n    function formatearFecha(fecha) {\n        if (!fecha) return '';\n        var date = new Date(fecha);\n        if (isNaN(date.getTime())) return fecha;\n        return date.toLocaleDateString('es-AR') + ' ' + date.toLocaleTimeString('es-AR');\n    }\n</script>\n\n</body>\n</html>"}]}