{"sourceFile": "services/scripts/public/consultar-servicios/index.php", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1757339966906, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1757339966906, "name": "Commit-0", "content": "<!DOCTYPE html>\n<html>\n<head>\n    <title>Consultar Servicios</title>\n    <link rel=\"stylesheet\" href=\"bootstrap.min.css\">\n    <style>\n        .servicio-card {\n            border: 1px solid #ddd;\n            border-radius: 8px;\n            padding: 20px;\n            margin: 20px 0;\n            background-color: #f9f9f9;\n        }\n        .servicio-field {\n            margin-bottom: 10px;\n        }\n        .servicio-field strong {\n            display: inline-block;\n            width: 150px;\n            color: #333;\n        }\n        .estado-badge {\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-size: 12px;\n            font-weight: bold;\n        }\n        .estado-sin-comenzar {\n            background-color: #ffc107;\n            color: #212529;\n        }\n        .estado-en-proceso {\n            background-color: #17a2b8;\n            color: white;\n        }\n        .estado-finalizado {\n            background-color: #28a745;\n            color: white;\n        }\n        .prioridad-alta {\n            color: #dc3545;\n            font-weight: bold;\n        }\n        .prioridad-media {\n            color: #ffc107;\n            font-weight: bold;\n        }\n        .prioridad-baja {\n            color: #28a745;\n            font-weight: bold;\n        }\n    </style>\n</head>\n<body>\n\n<div class=\"container\">\n    <h1>Consultar Servicios</h1>\n\n    <div class=\"row\">\n        <div class=\"col-md-6\">\n            <div class=\"form-group\">\n                <label for=\"numeroServicio\">Número de Servicio:</label>\n                <input type=\"text\" name=\"numeroServicio\" id=\"numeroServicio\" class=\"form-control\" placeholder=\"Ingrese el número de servicio\">\n            </div>\n        </div>\n        <div class=\"col-md-6\">\n            <div class=\"form-group\">\n                <label for=\"password\">Contraseña:</label>\n                <input type=\"password\" name=\"password\" id=\"password\" class=\"form-control\" placeholder=\"Ingrese la contraseña\">\n            </div>\n        </div>\n    </div>\n\n    <div class=\"form-group\">\n        <input type=\"submit\" name=\"consultar\" id=\"consultar\" value=\"Consultar Servicio\" onclick=\"consultarServicio()\" class=\"btn btn-primary\">\n    </div>\n\n    <div id=\"resultado\"></div>\n</div>\n\n<script src=\"jquery-2.2.4.min.js\"></script>\n<script>\n    // Configuración del IUE - puede venir por GET o estar hardcodeado\n    var iue = '<?php echo isset($_GET['iue']) ? $_GET['iue'] : 'HU7jaq4YHEe39T3zPUrn'; ?>';\n\n    $(function() {\n        $(\"#numeroServicio\").focus();\n\n        // Permitir consultar con Enter en ambos campos\n        $(\"#numeroServicio, #password\").keypress(function(e) {\n            if (e.keyCode == 13) {\n                consultarServicio();\n            }\n        });\n    });\n\n    function consultarServicio() {\n        var numeroServicio = $(\"#numeroServicio\").val().trim();\n        var password = $(\"#password\").val().trim();\n        var resultado = $(\"#resultado\");\n\n        // Validaciones\n        if (!numeroServicio) {\n            alert(\"Por favor ingrese el número de servicio\");\n            $(\"#numeroServicio\").focus();\n            return;\n        }\n\n        if (!password) {\n            alert(\"Por favor ingrese la contraseña\");\n            $(\"#password\").focus();\n            return;\n        }\n\n        // Mostrar indicador de carga\n        var cargando = '<div class=\"text-center\"><img src=\"https://app.saasargentina.com/estilos/estilo_1/images/cargando.gif\"><br>Consultando servicio...</div>';\n        resultado.html(cargando);\n\n        // Construir URL de la API\n        var url = 'https://api.saasargentina.com/v1/servicios/' + numeroServicio + '?iue=' + iue + '&pass=' + password;\n\n        // Realizar consulta a la API\n        $.getJSON(url)\n            .done(function(data) {\n                if (data.estado && data.resultados && data.resultados.length > 0) {\n                    mostrarServicio(data.resultados[0]);\n                } else {\n                    mostrarError(\"No se encontró el servicio o los datos de acceso son incorrectos\");\n                }\n            })\n            .fail(function(jqxhr, textStatus, error) {\n                var errorMsg = \"Error al consultar el servicio\";\n                if (jqxhr.status === 404) {\n                    errorMsg = \"Servicio no encontrado\";\n                } else if (jqxhr.status === 401 || jqxhr.status === 403) {\n                    errorMsg = \"Acceso denegado. Verifique la contraseña\";\n                } else if (jqxhr.responseJSON && jqxhr.responseJSON.error) {\n                    errorMsg = jqxhr.responseJSON.error;\n                }\n                mostrarError(errorMsg);\n            });\n    }\n\n    function mostrarServicio(servicio) {\n        var html = '<div class=\"servicio-card\">';\n        html += '<h3>Servicio #' + servicio.idservicio + '</h3>';\n\n        if (servicio.titulo) {\n            html += '<div class=\"servicio-field\"><strong>Título:</strong> ' + servicio.titulo + '</div>';\n        }\n\n        html += '<div class=\"servicio-field\"><strong>Estado:</strong> <span class=\"estado-badge ' + getEstadoClass(servicio.estado) + '\">' + servicio.estado + '</span></div>';\n\n        if (servicio.prioridad) {\n            html += '<div class=\"servicio-field\"><strong>Prioridad:</strong> <span class=\"' + getPrioridadClass(servicio.prioridad) + '\">' + getPrioridadTexto(servicio.prioridad) + '</span></div>';\n        }\n\n        html += '<div class=\"servicio-field\"><strong>Tipo:</strong> ' + (servicio.tiposervicio || 'No especificado') + '</div>';\n        html += '<div class=\"servicio-field\"><strong>Cliente:</strong> ' + (servicio.cliente || 'No especificado') + '</div>';\n\n        if (servicio.fechainicio) {\n            html += '<div class=\"servicio-field\"><strong>Fecha Inicio:</strong> ' + formatearFecha(servicio.fechainicio) + '</div>';\n        }\n\n        if (servicio.fechafin) {\n            html += '<div class=\"servicio-field\"><strong>Fecha Fin:</strong> ' + formatearFecha(servicio.fechafin) + '</div>';\n        }\n\n        if (servicio.fechalimite) {\n            html += '<div class=\"servicio-field\"><strong>Fecha Límite:</strong> ' + formatearFecha(servicio.fechalimite) + '</div>';\n        }\n\n        html += '<div class=\"servicio-field\"><strong>Fecha Solicitado:</strong> ' + formatearFecha(servicio.fechasolicitado) + '</div>';\n\n        if (servicio.tiempodedicado && servicio.tiempodedicado !== '00:00:00') {\n            html += '<div class=\"servicio-field\"><strong>Tiempo Dedicado:</strong> ' + servicio.tiempodedicado + '</div>';\n        }\n\n        if (servicio.obssolicitado) {\n            html += '<div class=\"servicio-field\"><strong>Observaciones Solicitadas:</strong><br>' + servicio.obssolicitado.replace(/\\n/g, '<br>') + '</div>';\n        }\n\n        if (servicio.obsrealizado) {\n            html += '<div class=\"servicio-field\"><strong>Observaciones Realizadas:</strong><br>' + servicio.obsrealizado.replace(/\\n/g, '<br>') + '</div>';\n        }\n\n        if (servicio.saldo !== null && servicio.saldo !== undefined) {\n            html += '<div class=\"servicio-field\"><strong>Saldo:</strong> $' + parseFloat(servicio.saldo).toFixed(2) + '</div>';\n        }\n\n        html += '</div>';\n\n        $(\"#resultado\").html(html);\n    }\n\n    function mostrarError(mensaje) {\n        var html = '<div class=\"alert alert-danger\" role=\"alert\">';\n        html += '<strong>Error:</strong> ' + mensaje;\n        html += '</div>';\n        $(\"#resultado\").html(html);\n    }\n\n    function getEstadoClass(estado) {\n        if (!estado) return '';\n        var estadoLower = estado.toLowerCase();\n        if (estadoLower.includes('sin comenzar') || estadoLower.includes('pendiente')) {\n            return 'estado-sin-comenzar';\n        } else if (estadoLower.includes('proceso') || estadoLower.includes('progreso')) {\n            return 'estado-en-proceso';\n        } else if (estadoLower.includes('finalizado') || estadoLower.includes('completado') || estadoLower.includes('terminado')) {\n            return 'estado-finalizado';\n        }\n        return '';\n    }\n\n    function getPrioridadClass(prioridad) {\n        if (prioridad == '1') return 'prioridad-alta';\n        if (prioridad == '2') return 'prioridad-media';\n        if (prioridad == '3') return 'prioridad-baja';\n        return '';\n    }\n\n    function getPrioridadTexto(prioridad) {\n        if (prioridad == '1') return 'Alta';\n        if (prioridad == '2') return 'Media';\n        if (prioridad == '3') return 'Baja';\n        return prioridad;\n    }\n\n    function formatearFecha(fecha) {\n        if (!fecha) return '';\n        var date = new Date(fecha);\n        if (isNaN(date.getTime())) return fecha;\n        return date.toLocaleDateString('es-AR') + ' ' + date.toLocaleTimeString('es-AR');\n    }\n</script>\n\n</body>\n</html>"}]}