{"sourceFile": "services/scripts/public/buscar-productos/index.php", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1749280039534, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1749280643902, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,8 +11,12 @@\n     <h1>Buscador de productos</h1>\n     <div class=\"form-group\">\n         <input type=\"text\" name=\"buscado\" id=\"buscado\" onkeypress=\"enter_buscar(event)\" class=\"form-control\" aria-describedby=\"buscar\"><br>\n         <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">\n+        <div class=\"form-check form-check-inline ml-2\">\n+            <input type=\"checkbox\" class=\"form-check-input\" id=\"terminalPrecios\" checked>\n+            <label class=\"form-check-label\" for=\"terminalPrecios\" title=\"Active esta opción si utiliza esta funcionalidad para una terminal de precios pública que no dispone de teclado o mouse\">Terminal de precios</label>\n+        </div>\n     </div>\n     <br>\n \n     <table class=\"table table-striped\">\n"}, {"date": 1749280675108, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,10 +10,10 @@\n \n     <h1>Buscador de productos</h1>\n     <div class=\"form-group\">\n         <input type=\"text\" name=\"buscado\" id=\"buscado\" onkeypress=\"enter_buscar(event)\" class=\"form-control\" aria-describedby=\"buscar\"><br>\n-        <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">\n         <div class=\"form-check form-check-inline ml-2\">\n+            <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">\n             <input type=\"checkbox\" class=\"form-check-input\" id=\"terminalPrecios\" checked>\n             <label class=\"form-check-label\" for=\"terminalPrecios\" title=\"Active esta opción si utiliza esta funcionalidad para una terminal de precios pública que no dispone de teclado o mouse\">Terminal de precios</label>\n         </div>\n     </div>\n"}, {"date": 1749280687908, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,9 +11,9 @@\n     <h1>Buscador de productos</h1>\n     <div class=\"form-group\">\n         <input type=\"text\" name=\"buscado\" id=\"buscado\" onkeypress=\"enter_buscar(event)\" class=\"form-control\" aria-describedby=\"buscar\"><br>\n         <div class=\"form-check form-check-inline ml-2\">\n-            <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">\n+            <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">&nbsp;\n             <input type=\"checkbox\" class=\"form-check-input\" id=\"terminalPrecios\" checked>\n             <label class=\"form-check-label\" for=\"terminalPrecios\" title=\"Active esta opción si utiliza esta funcionalidad para una terminal de precios pública que no dispone de teclado o mouse\">Terminal de precios</label>\n         </div>\n     </div>\n"}, {"date": 1755198825773, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -28,12 +28,29 @@\n <script>\n     var iues = '<?php echo $_GET['iue']; ?>'.split(',');\n     var empresas = '<?php echo $_GET['empresa']; ?>'.split(',');\n     var cantidad = '<?php echo is_numeric($_GET['cantidad']) ? $_GET['cantidad'] : 10; ?>';\n+    \n+    // Procesar el parámetro campos\n+    var camposDisponibles = ['empresa', 'codigo', 'nombre', 'precio', 'preciofinal', 'stock'];\n+    var camposPredeterminados = 'codigo,nombre,precio,preciofinal,stock';\n+    var camposParam = '<?php echo isset($_GET['campos']) ? $_GET['campos'] : ''; ?>';\n+    var campos = [];\n+    \n+    if (camposParam) {\n\\ No newline at end of file\n+        var camposSolicitados = camposParam.split(',');\n+        campos = camposSolicitados.filter(function(campo) {\n+            return camposDisponibles.includes(campo.trim());\n+        });\n+    }\n+    \n+    if (campos.length === 0) {\n+        campos = camposPredeterminados.split(',');\n+    }\n \n     $(function() {\n         $(\"#buscado\").focus();\n     });\n </script>\n \n </body>\n-</html>\n+</html>\n"}, {"date": 1757345179573, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,56 +1,156 @@\n-<!DOCTYPE html>\n-<html>\n-<head>\n-    <title>Buscador de productos</title>\n-    <link rel=\"stylesheet\" href=\"bootstrap.min.css\">\n-</head>\n-<body>\n+<?php\n+// Configuración de parámetros - mantener compatibilidad total\n+$iues = isset($_GET['iue']) ? explode(',', $_GET['iue']) : [];\n+$empresas = isset($_GET['empresa']) ? explode(',', $_GET['empresa']) : [];\n+$cantidad = isset($_GET['cantidad']) && is_numeric($_GET['cantidad']) ? (int)$_GET['cantidad'] : 10;\n \n-<div class=\"container\">\n+// Procesar campos disponibles\n+$camposDisponibles = ['empresa', 'codigo', 'nombre', 'precio', 'preciofinal', 'stock'];\n+$camposPredeterminados = ['codigo', 'nombre', 'precio', 'preciofinal', 'stock'];\n+$camposParam = isset($_GET['campos']) ? $_GET['campos'] : '';\n \n-    <h1>Buscador de productos</h1>\n-    <div class=\"form-group\">\n-        <input type=\"text\" name=\"buscado\" id=\"buscado\" onkeypress=\"enter_buscar(event)\" class=\"form-control\" aria-describedby=\"buscar\"><br>\n-        <div class=\"form-check form-check-inline ml-2\">\n-            <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">&nbsp;\n-            <input type=\"checkbox\" class=\"form-check-input\" id=\"terminalPrecios\" checked>\n-            <label class=\"form-check-label\" for=\"terminalPrecios\" title=\"Active esta opción si utiliza esta funcionalidad para una terminal de precios pública que no dispone de teclado o mouse\">Terminal de precios</label>\n-        </div>\n-    </div>\n-    <br>\n+$campos = [];\n+if ($camposParam) {\n+    $camposSolicitados = explode(',', $camposParam);\n+    foreach ($camposSolicitados as $campo) {\n+        $campo = trim($campo);\n+        if (in_array($campo, $camposDisponibles)) {\n+            $campos[] = $campo;\n+        }\n+    }\n+}\n \n-    <table class=\"table table-striped\">\n-    </table>\n-</div>\n+if (empty($campos)) {\n+    $campos = $camposPredeterminados;\n+}\n \n-<script src=\"jquery-2.2.4.min.js\"></script>\n-<script src=\"buscador.js\"></script>\n-<script>\n-    var iues = '<?php echo $_GET['iue']; ?>'.split(',');\n-    var empresas = '<?php echo $_GET['empresa']; ?>'.split(',');\n-    var cantidad = '<?php echo is_numeric($_GET['cantidad']) ? $_GET['cantidad'] : 10; ?>';\n+// Función para buscar productos en la API\n+function buscarProductosAPI($busqueda, $iues, $empresas, $cantidad) {\n+    $resultados = [];\n \n-    // Procesar el parámetro campos\n-    var camposDisponibles = ['empresa', 'codigo', 'nombre', 'precio', 'preciofinal', 'stock'];\n-    var camposPredeterminados = 'codigo,nombre,precio,preciofinal,stock';\n-    var camposParam = '<?php echo isset($_GET['campos']) ? $_GET['campos'] : ''; ?>';\n-    var campos = [];\n+    foreach ($iues as $index => $iue) {\n+        $empresa = isset($empresas[$index]) ? $empresas[$index] : 'Empresa ' . ($index + 1);\n \n-    if (camposParam) {\n-        var camposSolicitados = camposParam.split(',');\n-        campos = camposSolicitados.filter(function(campo) {\n-            return camposDisponibles.includes(campo.trim());\n-        });\n+        $url = \"https://api-beta.saasargentina.com/v1/productos/\" .\n+               \"?iue=\" . urlencode($iue) .\n+               \"&busqueda=\" . urlencode($busqueda) .\n+               \"&cantidad=\" . urlencode($cantidad);\n+\n+        $ch = curl_init();\n+        curl_setopt($ch, CURLOPT_URL, $url);\n+        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);\n+        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);\n+        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);\n+        curl_setopt($ch, CURLOPT_TIMEOUT, 30);\n+        curl_setopt($ch, CURLOPT_USERAGENT, 'SaaS Argentina Buscador Productos Script');\n+\n+        $response = curl_exec($ch);\n+        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);\n+        $error = curl_error($ch);\n+        curl_close($ch);\n+\n+        if ($error) {\n+            $resultados[] = [\n+                'empresa' => $empresa,\n+                'error' => true,\n+                'message' => 'Error de conexión: ' . $error,\n+                'productos' => []\n+            ];\n+            continue;\n+        }\n+\n+        if ($httpCode !== 200) {\n+            $resultados[] = [\n+                'empresa' => $empresa,\n+                'error' => true,\n+                'message' => 'Error HTTP: ' . $httpCode,\n+                'productos' => []\n+            ];\n+            continue;\n+        }\n+\n+        $data = json_decode($response, true);\n+        if (json_last_error() !== JSON_ERROR_NONE) {\n+            $resultados[] = [\n+                'empresa' => $empresa,\n+                'error' => true,\n+                'message' => 'Error al procesar respuesta',\n+                'productos' => []\n+            ];\n+            continue;\n+        }\n+\n+        $resultados[] = [\n+            'empresa' => $empresa,\n+            'error' => false,\n+            'message' => '',\n+            'productos' => isset($data['resultados']) ? $data['resultados'] : []\n+        ];\n     }\n \n-    if (campos.length === 0) {\n-        campos = camposPredeterminados.split(',');\n+    return $resultados;\n+}\n+\n+// Manejar solicitudes AJAX\n+if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'buscar') {\n+    header('Content-Type: application/json');\n+\n+    $busqueda = trim($_POST['busqueda'] ?? '');\n+\n+    if (empty($busqueda)) {\n+        echo json_encode(['error' => true, 'message' => 'La búsqueda es requerida']);\n+        exit;\n     }\n \n-    $(function() {\n-        $(\"#buscado\").focus();\n-    });\n-</script>\n+    if (empty($iues)) {\n+        echo json_encode(['error' => true, 'message' => 'No se han configurado empresas para buscar']);\n+        exit;\n+    }\n \n-</body>\n-</html>\n+    $resultados = buscarProductosAPI($busqueda, $iues, $empresas, $cantidad);\n+    echo json_encode(['error' => false, 'resultados' => $resultados, 'campos' => $campos]);\n+    exit;\n+}\n+?>\n+<!DOCTYPE html>\n+<html lang=\"es\">\n+<head>\n+    <meta charset=\"UTF-8\">\n+    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n+    <title>Buscador de productos</title>\n+    <link href=\"bootstrap.min.css\" rel=\"stylesheet\">\n+    <style>\n+        .loading-spinner {\n+            display: inline-block;\n+            width: 1rem;\n+            height: 1rem;\n+            border: 0.125rem solid #f3f3f3;\n+            border-top: 0.125rem solid #0d6efd;\n+            border-radius: 50%;\n+            animation: spin 1s linear infinite;\n+        }\n+        @keyframes spin {\n+            0% { transform: rotate(0deg); }\n+            100% { transform: rotate(360deg); }\n+        }\n+        .table-container {\n+            margin-top: 1.5rem;\n+        }\n+        .empresa-section {\n+            margin-bottom: 1rem;\n+        }\n+        .empresa-title {\n+            background-color: #f8f9fa;\n+            padding: 0.5rem 1rem;\n+            border-left: 4px solid #0d6efd;\n+            margin-bottom: 0.5rem;\n+            font-weight: 600;\n+        }\n+        .no-results {\n+            text-align: center;\n+            color: #6c757d;\n+            font-style: italic;\n+            padding: 1rem;\n+        }\n+    </style>\n+</head>\n"}, {"date": 1757345206204, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,285 @@\n+<?php\n+// Configuración de parámetros - mantener compatibilidad total\n+$iues = isset($_GET['iue']) ? explode(',', $_GET['iue']) : [];\n+$empresas = isset($_GET['empresa']) ? explode(',', $_GET['empresa']) : [];\n+$cantidad = isset($_GET['cantidad']) && is_numeric($_GET['cantidad']) ? (int)$_GET['cantidad'] : 10;\n+\n+// Procesar campos disponibles\n+$camposDisponibles = ['empresa', 'codigo', 'nombre', 'precio', 'preciofinal', 'stock'];\n+$camposPredeterminados = ['codigo', 'nombre', 'precio', 'preciofinal', 'stock'];\n+$camposParam = isset($_GET['campos']) ? $_GET['campos'] : '';\n+\n+$campos = [];\n+if ($camposParam) {\n+    $camposSolicitados = explode(',', $camposParam);\n+    foreach ($camposSolicitados as $campo) {\n+        $campo = trim($campo);\n+        if (in_array($campo, $camposDisponibles)) {\n+            $campos[] = $campo;\n+        }\n+    }\n+}\n+\n+if (empty($campos)) {\n+    $campos = $camposPredeterminados;\n+}\n+\n+// Función para buscar productos en la API\n+function buscarProductosAPI($busqueda, $iues, $empresas, $cantidad) {\n+    $resultados = [];\n+\n+    foreach ($iues as $index => $iue) {\n+        $empresa = isset($empresas[$index]) ? $empresas[$index] : 'Empresa ' . ($index + 1);\n+\n+        $url = \"https://api-beta.saasargentina.com/v1/productos/\" .\n+               \"?iue=\" . urlencode($iue) .\n+               \"&busqueda=\" . urlencode($busqueda) .\n+               \"&cantidad=\" . urlencode($cantidad);\n+\n+        $ch = curl_init();\n+        curl_setopt($ch, CURLOPT_URL, $url);\n+        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);\n+        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);\n+        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);\n+        curl_setopt($ch, CURLOPT_TIMEOUT, 30);\n+        curl_setopt($ch, CURLOPT_USERAGENT, 'SaaS Argentina Buscador Productos Script');\n+\n+        $response = curl_exec($ch);\n+        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);\n+        $error = curl_error($ch);\n+        curl_close($ch);\n+\n+        if ($error) {\n+            $resultados[] = [\n+                'empresa' => $empresa,\n+                'error' => true,\n+                'message' => 'Error de conexión: ' . $error,\n+                'productos' => []\n+            ];\n+            continue;\n+        }\n+\n+        if ($httpCode !== 200) {\n+            $resultados[] = [\n+                'empresa' => $empresa,\n+                'error' => true,\n+                'message' => 'Error HTTP: ' . $httpCode,\n+                'productos' => []\n+            ];\n+            continue;\n+        }\n+\n+        $data = json_decode($response, true);\n+        if (json_last_error() !== JSON_ERROR_NONE) {\n+            $resultados[] = [\n+                'empresa' => $empresa,\n+                'error' => true,\n+                'message' => 'Error al procesar respuesta',\n+                'productos' => []\n+            ];\n+            continue;\n+        }\n+\n+        $resultados[] = [\n+            'empresa' => $empresa,\n+            'error' => false,\n+            'message' => '',\n+            'productos' => isset($data['resultados']) ? $data['resultados'] : []\n+        ];\n+    }\n+\n+    return $resultados;\n+}\n+\n+// Manejar solicitudes AJAX\n+if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'buscar') {\n+    header('Content-Type: application/json');\n+\n+    $busqueda = trim($_POST['busqueda'] ?? '');\n+\n+    if (empty($busqueda)) {\n+        echo json_encode(['error' => true, 'message' => 'La búsqueda es requerida']);\n+        exit;\n+    }\n+\n+    if (empty($iues)) {\n+        echo json_encode(['error' => true, 'message' => 'No se han configurado empresas para buscar']);\n+        exit;\n+    }\n+\n+    $resultados = buscarProductosAPI($busqueda, $iues, $empresas, $cantidad);\n+    echo json_encode(['error' => false, 'resultados' => $resultados, 'campos' => $campos]);\n+    exit;\n+}\n+?>\n+<!DOCTYPE html>\n+<html lang=\"es\">\n+<head>\n+    <meta charset=\"UTF-8\">\n+    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n+    <title>Buscador de productos</title>\n+    <link href=\"bootstrap.min.css\" rel=\"stylesheet\">\n+    <style>\n+        .loading-spinner {\n+            display: inline-block;\n+            width: 1rem;\n+            height: 1rem;\n+            border: 0.125rem solid #f3f3f3;\n+            border-top: 0.125rem solid #0d6efd;\n+            border-radius: 50%;\n+            animation: spin 1s linear infinite;\n+        }\n+        @keyframes spin {\n+            0% { transform: rotate(0deg); }\n+            100% { transform: rotate(360deg); }\n+        }\n+        .table-container {\n+            margin-top: 1.5rem;\n+        }\n+        .empresa-section {\n+            margin-bottom: 1rem;\n+        }\n+        .empresa-title {\n+            background-color: #f8f9fa;\n+            padding: 0.5rem 1rem;\n+            border-left: 4px solid #0d6efd;\n+            margin-bottom: 0.5rem;\n+            font-weight: 600;\n+        }\n+        .no-results {\n+            text-align: center;\n+            color: #6c757d;\n+            font-style: italic;\n+            padding: 1rem;\n+        }\n+    </style>\n+</head>\n+<body>\n+<div class=\"container py-4\">\n+    <div class=\"row justify-content-center\">\n+        <div class=\"col-lg-10\">\n+            <h1 class=\"mb-4 text-center\">Buscador de productos</h1>\n+\n+            <div class=\"card\">\n+                <div class=\"card-body\">\n+                    <form id=\"busquedaForm\">\n+                        <div class=\"row\">\n+                            <div class=\"col-md-8\">\n+                                <div class=\"mb-3\">\n+                                    <label for=\"buscado\" class=\"form-label\">Buscar productos:</label>\n+                                    <input type=\"text\" name=\"buscado\" id=\"buscado\" class=\"form-control\" placeholder=\"Ingrese el nombre o código del producto\" required>\n+                                </div>\n+                            </div>\n+                            <div class=\"col-md-4\">\n+                                <div class=\"mb-3\">\n+                                    <label class=\"form-label\">&nbsp;</label>\n+                                    <div class=\"d-grid\">\n+                                        <button type=\"submit\" id=\"buscar\" class=\"btn btn-primary\">\n+                                            <span id=\"btnText\">Buscar</span>\n+                                            <span id=\"btnSpinner\" class=\"loading-spinner ms-2\" style=\"display: none;\"></span>\n+                                        </button>\n+                                    </div>\n+                                </div>\n+                            </div>\n+                        </div>\n+\n+                        <div class=\"form-check\">\n+                            <input type=\"checkbox\" class=\"form-check-input\" id=\"terminalPrecios\" checked>\n+                            <label class=\"form-check-label\" for=\"terminalPrecios\" title=\"Active esta opción si utiliza esta funcionalidad para una terminal de precios pública que no dispone de teclado o mouse\">\n+                                Terminal de precios\n+                            </label>\n+                        </div>\n+                    </form>\n+                </div>\n+            </div>\n+\n+            <div id=\"resultado\" class=\"table-container\"></div>\n+        </div>\n+    </div>\n+</div>\n+\n+<script src=\"jquery-3.7.1.min.js\"></script>\n+<script src=\"bootstrap.bundle.min.js\"></script>\n+<script>\n+    // Configuración desde PHP - mantener compatibilidad\n+    var configData = {\n+        iues: <?php echo json_encode($iues); ?>,\n+        empresas: <?php echo json_encode($empresas); ?>,\n+        cantidad: <?php echo json_encode($cantidad); ?>,\n+        campos: <?php echo json_encode($campos); ?>\n+    };\n+\n+    $(function() {\n+        $(\"#buscado\").focus();\n+\n+        // Manejar el envío del formulario\n+        $(\"#busquedaForm\").on('submit', function(e) {\n+            e.preventDefault();\n+            buscar();\n+        });\n+\n+        // Permitir buscar con Enter\n+        $(\"#buscado\").on('keypress', function(e) {\n+            if (e.which === 13) {\n+                e.preventDefault();\n+                buscar();\n+            }\n+        });\n+    });\n+\n+    function buscar() {\n+        var buscado = $(\"#buscado\").val().trim();\n+        var resultado = $(\"#resultado\");\n+        var btnText = $(\"#btnText\");\n+        var btnSpinner = $(\"#btnSpinner\");\n+        var buscarBtn = $(\"#buscar\");\n+\n+        // Validación\n+        if (!buscado) {\n+            mostrarAlerta(\"Por favor ingrese un término de búsqueda\", \"warning\");\n+            $(\"#buscado\").focus();\n+            return;\n+        }\n+\n+        // Mostrar indicador de carga\n+        btnText.text(\"Buscando...\");\n+        btnSpinner.show();\n+        buscarBtn.prop('disabled', true);\n+        resultado.html('<div class=\"text-center mt-3\"><div class=\"loading-spinner\"></div><br>Buscando productos...</div>');\n+\n+        // Realizar búsqueda via AJAX al PHP local\n+        $.ajax({\n+            url: '',\n+            method: 'POST',\n+            data: {\n+                action: 'buscar',\n+                busqueda: buscado\n+            },\n+            dataType: 'json'\n+        })\n+        .done(function(response) {\n+            if (response.error) {\n+                mostrarError(response.message);\n+            } else {\n+                mostrarResultados(response.resultados, response.campos);\n+            }\n+        })\n+        .fail(function(jqxhr, textStatus, error) {\n+            var errorMsg = \"Error al realizar la búsqueda\";\n+            if (jqxhr.responseJSON && jqxhr.responseJSON.message) {\n+                errorMsg = jqxhr.responseJSON.message;\n+            }\n+            mostrarError(errorMsg);\n+        })\n+        .always(function() {\n+            // Restaurar botón\n+            btnText.text(\"Buscar\");\n+            btnSpinner.hide();\n+            buscarBtn.prop('disabled', false);\n+\n+            // Limpiar campo si es terminal de precios\n+            if ($(\"#terminalPrecios\").is(\":checked\")) {\n+                $(\"#buscado\").val(\"\").focus();\n+            }\n+        });\n+    }\n"}], "date": 1749280039534, "name": "Commit-0", "content": "<!DOCTYPE html>\n<html>\n<head>\n    <title>Buscador de productos</title>\n    <link rel=\"stylesheet\" href=\"bootstrap.min.css\">\n</head>\n<body>\n\n<div class=\"container\">\n\n    <h1>Buscador de productos</h1>\n    <div class=\"form-group\">\n        <input type=\"text\" name=\"buscado\" id=\"buscado\" onkeypress=\"enter_buscar(event)\" class=\"form-control\" aria-describedby=\"buscar\"><br>\n        <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">\n    </div>\n    <br>\n\n    <table class=\"table table-striped\">\n    </table>\n</div>\n\n<script src=\"jquery-2.2.4.min.js\"></script>\n<script src=\"buscador.js\"></script>\n<script>\n    var iues = '<?php echo $_GET['iue']; ?>'.split(',');\n    var empresas = '<?php echo $_GET['empresa']; ?>'.split(',');\n    var cantidad = '<?php echo is_numeric($_GET['cantidad']) ? $_GET['cantidad'] : 10; ?>';\n\n    $(function() {\n        $(\"#buscado\").focus();\n    });\n</script>\n\n</body>\n</html>"}]}