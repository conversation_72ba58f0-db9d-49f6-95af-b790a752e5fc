{"sourceFile": "services/api/public/v1/index.php", "activeCommit": 0, "commits": [{"activePatchIndex": 27, "patches": [{"date": 1739385900028, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1739386324007, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -184,9 +184,9 @@\n                     'iue' => $iue,\n                     'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n                     'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 10, \"min_range\" => 1, \"max_range\" => 100)))),\n                 );\n-                $client->verCombos($datos);\n+                $client->listarCombos($datos);\n \n             } else {\n                 $datos = array(\n                     'iue' => $iue,\n"}, {"date": 1739388763670, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -182,9 +182,9 @@\n \n                 $datos = array(\n                     'iue' => $iue,\n                     'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n-                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 10, \"min_range\" => 1, \"max_range\" => 100)))),\n+                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 100, \"min_range\" => 1, \"max_range\" => 1000)))),\n                 );\n                 $client->listarCombos($datos);\n \n             } else {\n"}, {"date": 1752267146297, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -30,8 +30,11 @@\n $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n if (!$iue && isset($_GET['IUE']))\n     $iue = filter_input(INPUT_GET, 'IUE', FILTER_SANITIZE_URL);\n \n+if ($iue == 'HU7jaq4YHEe39T3zPUrn')\n+    exit('IUE Bloqueado por exceso de consumo');\n+\n $recursos = array();\n $temp_array = array_filter(explode('/', $url));\n \n for ($i = 0, $cantidad = count($temp_array); $i < $cantidad; $i++) {\n@@ -793,8 +796,24 @@\n                         'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n                         'idtiposervicio' => filter_input(INPUT_GET, 'idtiposervicio', FILTER_SANITIZE_NUMBER_INT),\n                         'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_NUMBER_INT),\n                         'estado' => filter_input(INPUT_GET, 'estado', FILTER_SANITIZE_NUMBER_INT),\n+\n+                        'fechasolicitado' => filter_input(INPUT_GET, 'fechasolicitado', FILTER_SANITIZE_STRING),\n+                        'fechasolicitado_desde' => filter_input(INPUT_GET, 'fechasolicitado_desde', FILTER_SANITIZE_STRING),\n+                        'fechasolicitado_hasta' => filter_input(INPUT_GET, 'fechasolicitado_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechainicio' => filter_input(INPUT_GET, 'fechainicio', FILTER_SANITIZE_STRING),\n+                        'fechainicio_desde' => filter_input(INPUT_GET, 'fechainicio_desde', FILTER_SANITIZE_STRING),\n+                        'fechainicio_hasta' => filter_input(INPUT_GET, 'fechainicio_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechafin' => filter_input(INPUT_GET, 'fechafin', FILTER_SANITIZE_STRING),\n+                        'fechafin_desde' => filter_input(INPUT_GET, 'fechafin_desde', FILTER_SANITIZE_STRING),\n+                        'fechafin_hasta' => filter_input(INPUT_GET, 'fechafin_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechalimite' => filter_input(INPUT_GET, 'fechalimite', FILTER_SANITIZE_STRING),\n+                        'fechalimite_desde' => filter_input(INPUT_GET, 'fechalimite_desde', FILTER_SANITIZE_STRING),\n+                        'fechalimite_hasta' => filter_input(INPUT_GET, 'fechalimite_hasta', FILTER_SANITIZE_STRING),\n                     );\n                     // Por si no funcionan las opciones del filter_input\n                     if (!$datos['cantidad']) {\n                         $datos['cantidad'] = '10';\n"}, {"date": 1752279056866, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -30,9 +30,9 @@\n $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n if (!$iue && isset($_GET['IUE']))\n     $iue = filter_input(INPUT_GET, 'IUE', FILTER_SANITIZE_URL);\n \n-if ($iue == 'HU7jaq4YHEe39T3zPUrn')\n+if ($iue == 'HU7jaq4YHEe39T3zPUrn' && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo')\n     exit('IUE Bloqueado por exceso de consumo');\n \n $recursos = array();\n $temp_array = array_filter(explode('/', $url));\n"}, {"date": 1752328596321, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -30,9 +30,9 @@\n $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n if (!$iue && isset($_GET['IUE']))\n     $iue = filter_input(INPUT_GET, 'IUE', FILTER_SANITIZE_URL);\n \n-if ($iue == 'HU7jaq4YHEe39T3zPUrn' && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo')\n+if ($iue == 'HU7jaq4YHEe39T3zPUrn' && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' && strlen(filter_input(INPUT_GET, 'appname', FILTER_SANITIZE_URL)) > 3)\n     exit('IUE Bloqueado por exceso de consumo');\n \n $recursos = array();\n $temp_array = array_filter(explode('/', $url));\n"}, {"date": 1752329274729, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -30,9 +30,9 @@\n $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n if (!$iue && isset($_GET['IUE']))\n     $iue = filter_input(INPUT_GET, 'IUE', FILTER_SANITIZE_URL);\n \n-if ($iue == 'HU7jaq4YHEe39T3zPUrn' && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' && strlen(filter_input(INPUT_GET, 'appname', FILTER_SANITIZE_URL)) > 3)\n+if ($iue == 'HU7jaq4YHEe39T3zPUrn' && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' && strlen(filter_input(INPUT_GET, 'appname', FILTER_SANITIZE_URL)) < 3)\n     exit('IUE Bloqueado por exceso de consumo');\n \n $recursos = array();\n $temp_array = array_filter(explode('/', $url));\n"}, {"date": 1752842865575, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -30,11 +30,8 @@\n $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n if (!$iue && isset($_GET['IUE']))\n     $iue = filter_input(INPUT_GET, 'IUE', FILTER_SANITIZE_URL);\n \n-if ($iue == 'HU7jaq4YHEe39T3zPUrn' && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' && strlen(filter_input(INPUT_GET, 'appname', FILTER_SANITIZE_URL)) < 3)\n-    exit('IUE Bloqueado por exceso de consumo');\n-\n $recursos = array();\n $temp_array = array_filter(explode('/', $url));\n \n for ($i = 0, $cantidad = count($temp_array); $i < $cantidad; $i++) {\n"}, {"date": 1752844230758, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -75,8 +75,26 @@\n } else {\n     touch($consultando_api);\n }\n \n+// Limito un llamado por segúndo como máximo\n+$timestamp_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.timestamp_api';\n+if (file_exists($timestamp_api)\n+    && time() - filemtime($timestamp_api) < 1) {\n+    exit(json_encode(array('error' => 'No se pueden ejecutar consultas más de una vez por segundo')));\n+} else {\n+    touch($timestamp_api);\n+}\n+\n+// Parche para limitar llamados de Gaming City\n+if ($iue == 'HU7jaq4YHEe39T3zPUrn'\n+    && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo'\n+    && time() - filemtime($timestamp_api) < 10) {\n+    touch($consultando_api);\n+    exit(json_encode(array('error' => 'IUE bloqueado por exceso de consulta con cantidad completo')));\n+}\n+\n+\n // PARCHE: Mando los datos a un log para saber que pasa\n file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\") . ' - ' . $_SERVER['SERVER_NAME'], FILE_APPEND);\n file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'REQUEST: ' . $metodo . \" \" . $url, FILE_APPEND);\n file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'BODY: ' . $body, FILE_APPEND);\n@@ -178,16 +196,9 @@\n     case 'combos':\n     case 'combosbycodigo':\n         if ($metodo == 'GET') {\n             if (!$recursos[1]) {\n-\n-                $datos = array(\n-                    'iue' => $iue,\n-                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n-                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 100, \"min_range\" => 1, \"max_range\" => 1000)))),\n-                );\n-                $client->listarCombos($datos);\n-\n+                $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n             } else {\n                 $datos = array(\n                     'iue' => $iue,\n                     'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n@@ -793,24 +804,8 @@\n                         'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n                         'idtiposervicio' => filter_input(INPUT_GET, 'idtiposervicio', FILTER_SANITIZE_NUMBER_INT),\n                         'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_NUMBER_INT),\n                         'estado' => filter_input(INPUT_GET, 'estado', FILTER_SANITIZE_NUMBER_INT),\n-\n-                        'fechasolicitado' => filter_input(INPUT_GET, 'fechasolicitado', FILTER_SANITIZE_STRING),\n-                        'fechasolicitado_desde' => filter_input(INPUT_GET, 'fechasolicitado_desde', FILTER_SANITIZE_STRING),\n-                        'fechasolicitado_hasta' => filter_input(INPUT_GET, 'fechasolicitado_hasta', FILTER_SANITIZE_STRING),\n-\n-                        'fechainicio' => filter_input(INPUT_GET, 'fechainicio', FILTER_SANITIZE_STRING),\n-                        'fechainicio_desde' => filter_input(INPUT_GET, 'fechainicio_desde', FILTER_SANITIZE_STRING),\n-                        'fechainicio_hasta' => filter_input(INPUT_GET, 'fechainicio_hasta', FILTER_SANITIZE_STRING),\n-\n-                        'fechafin' => filter_input(INPUT_GET, 'fechafin', FILTER_SANITIZE_STRING),\n-                        'fechafin_desde' => filter_input(INPUT_GET, 'fechafin_desde', FILTER_SANITIZE_STRING),\n-                        'fechafin_hasta' => filter_input(INPUT_GET, 'fechafin_hasta', FILTER_SANITIZE_STRING),\n-\n-                        'fechalimite' => filter_input(INPUT_GET, 'fechalimite', FILTER_SANITIZE_STRING),\n-                        'fechalimite_desde' => filter_input(INPUT_GET, 'fechalimite_desde', FILTER_SANITIZE_STRING),\n-                        'fechalimite_hasta' => filter_input(INPUT_GET, 'fechalimite_hasta', FILTER_SANITIZE_STRING),\n                     );\n                     // Por si no funcionan las opciones del filter_input\n                     if (!$datos['cantidad']) {\n                         $datos['cantidad'] = '10';\n"}, {"date": 1752844539589, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -75,26 +75,35 @@\n } else {\n     touch($consultando_api);\n }\n \n-// Limito un llamado por segúndo como máximo\n+// Limitar llamadas a la API por segundo por recurso\n $timestamp_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.timestamp_api';\n-if (file_exists($timestamp_api)\n-    && time() - filemtime($timestamp_api) < 1) {\n-    exit(json_encode(array('error' => 'No se pueden ejecutar consultas más de una vez por segundo')));\n+\n+// Verificar si existe el archivo timestamp y si ha pasado menos de 1 segundo\n+if (file_exists($timestamp_api) && time() - filemtime($timestamp_api) < 1) {\n+    http_response_code(429); // Too Many Requests\n+    exit(json_encode(array(\n+        'error' => 'Rate limit exceeded - Maximum 1 request per second allowed',\n+        'retry_after' => 1\n+    )));\n } else {\n     touch($timestamp_api);\n }\n \n-// Parche para limitar llamados de Gaming City\n-if ($iue == 'HU7jaq4YHEe39T3zPUrn'\n-    && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo'\n-    && time() - filemtime($timestamp_api) < 10) {\n-    touch($consultando_api);\n-    exit(json_encode(array('error' => 'IUE bloqueado por exceso de consulta con cantidad completo')));\n+// Limitar llamadas específicas para Gaming City\n+if ($iue === 'HU7jaq4YHEe39T3zPUrn') {\n+    $cantidad = filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING);\n+    if ($cantidad === 'completo' && time() - filemtime($timestamp_api) < 10) {\n+        http_response_code(429); // Too Many Requests\n+        touch($consultando_api);\n+        exit(json_encode(array(\n+            'error' => 'Rate limit exceeded for complete data requests',\n+            'retry_after' => 10\n+        )));\n+    }\n }\n \n-\n // PARCHE: Mando los datos a un log para saber que pasa\n file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\") . ' - ' . $_SERVER['SERVER_NAME'], FILE_APPEND);\n file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'REQUEST: ' . $metodo . \" \" . $url, FILE_APPEND);\n file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'BODY: ' . $body, FILE_APPEND);\n"}, {"date": 1752947131218, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -70,11 +70,8 @@\n         . ' - No se pueden ejecutar consultas en paralelo al mismo recurso: '\n         . $iue.'_'.$recursos[0], FILE_APPEND);\n     http_response_code(429);\n     exit(json_encode(array('error' => 'No se pueden ejecutar consultas en paralelo al mismo recurso')));\n-\n-} else {\n-    touch($consultando_api);\n }\n \n // Limitar llamadas a la API por segundo por recurso\n $timestamp_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.timestamp_api';\n@@ -85,10 +82,8 @@\n     exit(json_encode(array(\n         'error' => 'Rate limit exceeded - Maximum 1 request per second allowed',\n         'retry_after' => 1\n     )));\n-} else {\n-    touch($timestamp_api);\n }\n \n // Limitar llamadas específicas para Gaming City\n if ($iue === 'HU7jaq4YHEe39T3zPUrn') {\n@@ -102,8 +97,11 @@\n         )));\n     }\n }\n \n+touch($consultando_api);\n+touch($timestamp_api);\n+\n // PARCHE: Mando los datos a un log para saber que pasa\n file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\") . ' - ' . $_SERVER['SERVER_NAME'], FILE_APPEND);\n file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'REQUEST: ' . $metodo . \" \" . $url, FILE_APPEND);\n file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'BODY: ' . $body, FILE_APPEND);\n"}, {"date": 1752956135148, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -87,9 +87,9 @@\n \n // Limitar llamadas específicas para Gaming City\n if ($iue === 'HU7jaq4YHEe39T3zPUrn') {\n     $cantidad = filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING);\n-    if ($cantidad === 'completo' && time() - filemtime($timestamp_api) < 10) {\n+    if ($cantidad === 'completo' && time() - filemtime($timestamp_api) < 1) {\n         http_response_code(429); // Too Many Requests\n         touch($consultando_api);\n         exit(json_encode(array(\n             'error' => 'Rate limit exceeded for complete data requests',\n@@ -203,9 +203,16 @@\n     case 'combos':\n     case 'combosbycodigo':\n         if ($metodo == 'GET') {\n             if (!$recursos[1]) {\n-                $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n+\n+                $datos = array(\n+                    'iue' => $iue,\n+                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n+                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 100, \"min_range\" => 1, \"max_range\" => 1000)))),\n+                );\n+                $client->listarCombos($datos);\n+\n             } else {\n                 $datos = array(\n                     'iue' => $iue,\n                     'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n@@ -811,8 +818,24 @@\n                         'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n                         'idtiposervicio' => filter_input(INPUT_GET, 'idtiposervicio', FILTER_SANITIZE_NUMBER_INT),\n                         'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_NUMBER_INT),\n                         'estado' => filter_input(INPUT_GET, 'estado', FILTER_SANITIZE_NUMBER_INT),\n+\n+                        'fechasolicitado' => filter_input(INPUT_GET, 'fechasolicitado', FILTER_SANITIZE_STRING),\n+                        'fechasolicitado_desde' => filter_input(INPUT_GET, 'fechasolicitado_desde', FILTER_SANITIZE_STRING),\n+                        'fechasolicitado_hasta' => filter_input(INPUT_GET, 'fechasolicitado_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechainicio' => filter_input(INPUT_GET, 'fechainicio', FILTER_SANITIZE_STRING),\n+                        'fechainicio_desde' => filter_input(INPUT_GET, 'fechainicio_desde', FILTER_SANITIZE_STRING),\n+                        'fechainicio_hasta' => filter_input(INPUT_GET, 'fechainicio_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechafin' => filter_input(INPUT_GET, 'fechafin', FILTER_SANITIZE_STRING),\n+                        'fechafin_desde' => filter_input(INPUT_GET, 'fechafin_desde', FILTER_SANITIZE_STRING),\n+                        'fechafin_hasta' => filter_input(INPUT_GET, 'fechafin_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechalimite' => filter_input(INPUT_GET, 'fechalimite', FILTER_SANITIZE_STRING),\n+                        'fechalimite_desde' => filter_input(INPUT_GET, 'fechalimite_desde', FILTER_SANITIZE_STRING),\n+                        'fechalimite_hasta' => filter_input(INPUT_GET, 'fechalimite_hasta', FILTER_SANITIZE_STRING),\n                     );\n                     // Por si no funcionan las opciones del filter_input\n                     if (!$datos['cantidad']) {\n                         $datos['cantidad'] = '10';\n"}, {"date": 1752956603461, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -77,27 +77,20 @@\n $timestamp_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.timestamp_api';\n \n // Verificar si existe el archivo timestamp y si ha pasado menos de 1 segundo\n if (file_exists($timestamp_api) && time() - filemtime($timestamp_api) < 1) {\n+    if ($iue === 'HU7jaq4YHEe39T3zPUrn') {\n+        $cantidad = filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING);\n+        if ($cantidad === 'completo')\n+            touch($consultando_api);\n+    }\n     http_response_code(429); // Too Many Requests\n     exit(json_encode(array(\n         'error' => 'Rate limit exceeded - Maximum 1 request per second allowed',\n         'retry_after' => 1\n     )));\n }\n \n-// Limitar llamadas específicas para Gaming City\n-if ($iue === 'HU7jaq4YHEe39T3zPUrn') {\n-    $cantidad = filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING);\n-    if ($cantidad === 'completo' && time() - filemtime($timestamp_api) < 1) {\n-        http_response_code(429); // Too Many Requests\n-        touch($consultando_api);\n-        exit(json_encode(array(\n-            'error' => 'Rate limit exceeded for complete data requests',\n-            'retry_after' => 10\n-        )));\n-    }\n-}\n \n touch($consultando_api);\n touch($timestamp_api);\n \n"}, {"date": 1752956704977, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -77,11 +77,10 @@\n $timestamp_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.timestamp_api';\n \n // Verificar si existe el archivo timestamp y si ha pasado menos de 1 segundo\n if (file_exists($timestamp_api) && time() - filemtime($timestamp_api) < 1) {\n-    if ($iue === 'HU7jaq4YHEe39T3zPUrn') {\n-        $cantidad = filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING);\n-        if ($cantidad === 'completo')\n+    if ($iue === 'HU7jaq4YHEe39T3zPUrn'\n+        && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING) === 'completo') {\n             touch($consultando_api);\n     }\n     http_response_code(429); // Too Many Requests\n     exit(json_encode(array(\n"}, {"date": 1752956742963, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -77,8 +77,9 @@\n $timestamp_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.timestamp_api';\n \n // Verificar si existe el archivo timestamp y si ha pasado menos de 1 segundo\n if (file_exists($timestamp_api) && time() - filemtime($timestamp_api) < 1) {\n+    // Si es de Gaming City pidiendo el listado completo bloqueo\n     if ($iue === 'HU7jaq4YHEe39T3zPUrn'\n         && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING) === 'completo') {\n             touch($consultando_api);\n     }\n"}, {"date": 1752956817603, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -196,16 +196,9 @@\n     case 'combos':\n     case 'combosbycodigo':\n         if ($metodo == 'GET') {\n             if (!$recursos[1]) {\n-\n-                $datos = array(\n-                    'iue' => $iue,\n-                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n-                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 100, \"min_range\" => 1, \"max_range\" => 1000)))),\n-                );\n-                $client->listarCombos($datos);\n-\n+                $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n             } else {\n                 $datos = array(\n                     'iue' => $iue,\n                     'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n@@ -811,24 +804,8 @@\n                         'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n                         'idtiposervicio' => filter_input(INPUT_GET, 'idtiposervicio', FILTER_SANITIZE_NUMBER_INT),\n                         'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_NUMBER_INT),\n                         'estado' => filter_input(INPUT_GET, 'estado', FILTER_SANITIZE_NUMBER_INT),\n-\n-                        'fechasolicitado' => filter_input(INPUT_GET, 'fechasolicitado', FILTER_SANITIZE_STRING),\n-                        'fechasolicitado_desde' => filter_input(INPUT_GET, 'fechasolicitado_desde', FILTER_SANITIZE_STRING),\n-                        'fechasolicitado_hasta' => filter_input(INPUT_GET, 'fechasolicitado_hasta', FILTER_SANITIZE_STRING),\n-\n-                        'fechainicio' => filter_input(INPUT_GET, 'fechainicio', FILTER_SANITIZE_STRING),\n-                        'fechainicio_desde' => filter_input(INPUT_GET, 'fechainicio_desde', FILTER_SANITIZE_STRING),\n-                        'fechainicio_hasta' => filter_input(INPUT_GET, 'fechainicio_hasta', FILTER_SANITIZE_STRING),\n-\n-                        'fechafin' => filter_input(INPUT_GET, 'fechafin', FILTER_SANITIZE_STRING),\n-                        'fechafin_desde' => filter_input(INPUT_GET, 'fechafin_desde', FILTER_SANITIZE_STRING),\n-                        'fechafin_hasta' => filter_input(INPUT_GET, 'fechafin_hasta', FILTER_SANITIZE_STRING),\n-\n-                        'fechalimite' => filter_input(INPUT_GET, 'fechalimite', FILTER_SANITIZE_STRING),\n-                        'fechalimite_desde' => filter_input(INPUT_GET, 'fechalimite_desde', FILTER_SANITIZE_STRING),\n-                        'fechalimite_hasta' => filter_input(INPUT_GET, 'fechalimite_hasta', FILTER_SANITIZE_STRING),\n                     );\n                     // Por si no funcionan las opciones del filter_input\n                     if (!$datos['cantidad']) {\n                         $datos['cantidad'] = '10';\n"}, {"date": 1753100922292, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -80,9 +80,11 @@\n if (file_exists($timestamp_api) && time() - filemtime($timestamp_api) < 1) {\n     // Si es de Gaming City pidiendo el listado completo bloqueo\n     if ($iue === 'HU7jaq4YHEe39T3zPUrn'\n         && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING) === 'completo') {\n-            touch($consultando_api);\n+            file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n+                . ' - Recurso Gaming City bloqueado: '\n+                . $iue.'_'.$recursos[0] . ' - '.$url, FILE_APPEND);\n     }\n     http_response_code(429); // Too Many Requests\n     exit(json_encode(array(\n         'error' => 'Rate limit exceeded - Maximum 1 request per second allowed',\n"}, {"date": 1753126540168, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -81,10 +81,9 @@\n     // Si es de Gaming City pidiendo el listado completo bloqueo\n     if ($iue === 'HU7jaq4YHEe39T3zPUrn'\n         && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING) === 'completo') {\n             file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n-                . ' - Recurso Gaming City bloqueado: '\n-                . $iue.'_'.$recursos[0] . ' - '.$url, FILE_APPEND);\n+                . ' - Recurso Gaming City bloqueado: '.$url, FILE_APPEND);\n     }\n     http_response_code(429); // Too Many Requests\n     exit(json_encode(array(\n         'error' => 'Rate limit exceeded - Maximum 1 request per second allowed',\n"}, {"date": 1753136873779, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -82,8 +82,9 @@\n     if ($iue === 'HU7jaq4YHEe39T3zPUrn'\n         && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING) === 'completo') {\n             file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n                 . ' - Recurso Gaming City bloqueado: '.$url, FILE_APPEND);\n+            touch($consultando_api);\n     }\n     http_response_code(429); // Too Many Requests\n     exit(json_encode(array(\n         'error' => 'Rate limit exceeded - Maximum 1 request per second allowed',\n@@ -197,9 +198,16 @@\n     case 'combos':\n     case 'combosbycodigo':\n         if ($metodo == 'GET') {\n             if (!$recursos[1]) {\n-                $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n+\n+                $datos = array(\n+                    'iue' => $iue,\n+                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n+                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 100, \"min_range\" => 1, \"max_range\" => 1000)))),\n+                );\n+                $client->listarCombos($datos);\n+\n             } else {\n                 $datos = array(\n                     'iue' => $iue,\n                     'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n@@ -805,8 +813,24 @@\n                         'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n                         'idtiposervicio' => filter_input(INPUT_GET, 'idtiposervicio', FILTER_SANITIZE_NUMBER_INT),\n                         'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_NUMBER_INT),\n                         'estado' => filter_input(INPUT_GET, 'estado', FILTER_SANITIZE_NUMBER_INT),\n+\n+                        'fechasolicitado' => filter_input(INPUT_GET, 'fechasolicitado', FILTER_SANITIZE_STRING),\n+                        'fechasolicitado_desde' => filter_input(INPUT_GET, 'fechasolicitado_desde', FILTER_SANITIZE_STRING),\n+                        'fechasolicitado_hasta' => filter_input(INPUT_GET, 'fechasolicitado_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechainicio' => filter_input(INPUT_GET, 'fechainicio', FILTER_SANITIZE_STRING),\n+                        'fechainicio_desde' => filter_input(INPUT_GET, 'fechainicio_desde', FILTER_SANITIZE_STRING),\n+                        'fechainicio_hasta' => filter_input(INPUT_GET, 'fechainicio_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechafin' => filter_input(INPUT_GET, 'fechafin', FILTER_SANITIZE_STRING),\n+                        'fechafin_desde' => filter_input(INPUT_GET, 'fechafin_desde', FILTER_SANITIZE_STRING),\n+                        'fechafin_hasta' => filter_input(INPUT_GET, 'fechafin_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechalimite' => filter_input(INPUT_GET, 'fechalimite', FILTER_SANITIZE_STRING),\n+                        'fechalimite_desde' => filter_input(INPUT_GET, 'fechalimite_desde', FILTER_SANITIZE_STRING),\n+                        'fechalimite_hasta' => filter_input(INPUT_GET, 'fechalimite_hasta', FILTER_SANITIZE_STRING),\n                     );\n                     // Por si no funcionan las opciones del filter_input\n                     if (!$datos['cantidad']) {\n                         $datos['cantidad'] = '10';\n"}, {"date": 1753213359618, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -77,18 +77,21 @@\n $timestamp_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.timestamp_api';\n \n // Verificar si existe el archivo timestamp y si ha pasado menos de 1 segundo\n if (file_exists($timestamp_api) && time() - filemtime($timestamp_api) < 1) {\n-    // Si es de Gaming City pidiendo el listado completo bloqueo\n-    if ($iue === 'HU7jaq4YHEe39T3zPUrn'\n-        && filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING) === 'completo') {\n-            file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n-                . ' - Recurso Gaming City bloqueado: '.$url, FILE_APPEND);\n-            touch($consultando_api);\n+    // Si es con listado completo bloqueo\n+    if (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING) === 'completo') {\n+        file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n+            . ' - Recurso completo bloqueado: '.$url, FILE_APPEND);\n+        touch($consultando_api);\n+        exit(json_encode(array(\n+            'error' => 'Rate limit exceeded - Bloqueado por varias peticiones con cantidad completo en un mismo segundo',\n+            'retry_after' => 1\n+        )));\n     }\n     http_response_code(429); // Too Many Requests\n     exit(json_encode(array(\n-        'error' => 'Rate limit exceeded - Maximum 1 request per second allowed',\n+        'error' => 'Rate limit exceeded - Varias peticiones en un mismo segundo',\n         'retry_after' => 1\n     )));\n }\n \n"}, {"date": 1753213421254, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -263,8 +263,10 @@\n         } else {\n             $datos = array(\n                 'iue' => $iue,\n                 'idrubro' => filter_input(INPUT_GET, 'idrubro', FILTER_SANITIZE_URL),    //Agrego sub-rubros\n+                'incluir_subrubros' => filter_input(INPUT_GET, 'incluir_subrubros', FILTER_SANITIZE_URL),\n+                'incluir_vacios' => filter_input(INPUT_GET, 'incluir_vacios', FILTER_SANITIZE_URL),\n             );\n             $client->listarRubros($datos);\n         }\n         break;\n"}, {"date": 1753215557010, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -264,9 +264,8 @@\n             $datos = array(\n                 'iue' => $iue,\n                 'idrubro' => filter_input(INPUT_GET, 'idrubro', FILTER_SANITIZE_URL),    //Agrego sub-rubros\n                 'incluir_subrubros' => filter_input(INPUT_GET, 'incluir_subrubros', FILTER_SANITIZE_URL),\n-                'incluir_vacios' => filter_input(INPUT_GET, 'incluir_vacios', FILTER_SANITIZE_URL),\n             );\n             $client->listarRubros($datos);\n         }\n         break;\n"}, {"date": 1753813528613, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -87,13 +87,16 @@\n             'error' => 'Rate limit exceeded - Bloqueado por varias peticiones con cantidad completo en un mismo segundo',\n             'retry_after' => 1\n         )));\n     }\n+    // Por ahora no bloqueo las otras consultas\n+    /*\n     http_response_code(429); // Too Many Requests\n     exit(json_encode(array(\n         'error' => 'Rate limit exceeded - Varias peticiones en un mismo segundo',\n         'retry_after' => 1\n     )));\n+    */\n }\n \n \n touch($consultando_api);\n"}, {"date": 1753884846185, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -80,9 +80,9 @@\n if (file_exists($timestamp_api) && time() - filemtime($timestamp_api) < 1) {\n     // Si es con listado completo bloqueo\n     if (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING) === 'completo') {\n         file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n-            . ' - Recurso completo bloqueado: '.$url, FILE_APPEND);\n+            . ' - Recurso completo bloqueado: '.json_encode($_GET), FILE_APPEND);\n         touch($consultando_api);\n         exit(json_encode(array(\n             'error' => 'Rate limit exceeded - Bloqueado por varias peticiones con cantidad completo en un mismo segundo',\n             'retry_after' => 1\n"}, {"date": 1753884859207, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -80,9 +80,9 @@\n if (file_exists($timestamp_api) && time() - filemtime($timestamp_api) < 1) {\n     // Si es con listado completo bloqueo\n     if (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING) === 'completo') {\n         file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n-            . ' - Recurso completo bloqueado: '.json_encode($_GET), FILE_APPEND);\n+            . ' - Recurso completo bloqueado con estos parámetros: '.json_encode($_GET), FILE_APPEND);\n         touch($consultando_api);\n         exit(json_encode(array(\n             'error' => 'Rate limit exceeded - Bloqueado por varias peticiones con cantidad completo en un mismo segundo',\n             'retry_after' => 1\n"}, {"date": 1755050827678, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -164,8 +164,10 @@\n                     'iue' => $iue,\n                     'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                     'mostrarcosto' => filter_input(INPUT_GET, 'mostrarcosto', FILTER_SANITIZE_URL),\n                     'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_URL),\n+                    'mostrarimagenes' => filter_input(INPUT_GET, 'mostrarimagenes', FILTER_SANITIZE_URL),\n+                    'mostrarproveedor' => filter_input(INPUT_GET, 'mostrarproveedor', FILTER_SANITIZE_URL),\n                     'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n                     'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n                 );\n                 $client->verProducto($datos);\n"}, {"date": 1756570364501, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -64,9 +64,10 @@\n // LIMITACIÓN PARA BLOQUEAR CONSUTLAS EN PARALELAS AL MISMO RECURSO'_'.$recursos[0].\n $consultando_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.consultando_api';\n $recursos_consultas_paralelas = ['rubros'];\n if (file_exists($consultando_api)\n-    && !in_array($recursos[0], $recursos_consultas_paralelas)) {\n+    && !in_array($recursos[0], $recursos_consultas_paralelas)\n+    && (!isset($_GET['utm_source']) || filter_input(INPUT_GET, 'utm_source') !== \"sv-woo-sync-saas-1.7\")) {\n     file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n         . ' - No se pueden ejecutar consultas en paralelo al mismo recurso: '\n         . $iue.'_'.$recursos[0], FILE_APPEND);\n     http_response_code(429);\n@@ -144,8 +145,9 @@\n                     'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n                     'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n                     'tipo' => (filter_input(INPUT_GET, 'tipo', FILTER_SANITIZE_STRING) ? filter_input(INPUT_GET, 'tipo', FILTER_SANITIZE_STRING) : 'completo'),\n                     'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_NUMBER_INT),\n+                    'mostrarproveedor' => filter_input(INPUT_GET, 'mostrarproveedor', FILTER_SANITIZE_NUMBER_INT),\n                 );\n                 // Por si no funcionan las opciones del filter_input\n                 if (!$datos['cantidad']) {\n                     $datos['cantidad'] = '10';\n"}, {"date": 1757343456773, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,887 @@\n+<?php\n+require __DIR__.'/../../../acc/acc.php';\n+require __DIR__.'/../../vendor/autoload.php';\n+require __DIR__.'/../../funciones_api.php';\n+\n+// Esto está deprecado, hay que pasar al autoload\n+function __autoload($classname) {\n+    $filename = $classname . '.php';\n+    if (is_readable($filename)) {\n+        require_once $filename;\n+    }\n+}\n+\n+spl_autoload_register('__autoload');\n+\n+// PARCHE: Mando un montón de headers para que funcione el cross-domain, pero hay que limpiar y dejar solo los que tienen que ir teniendo en cuenta la seguridad\n+if (preg_match(\"/MSIE/i\", $_SERVER['HTTP_USER_AGENT'])) {\n+    header('P3P: CP=\"IDC DSP COR ADM DEVi TAIi PSA PSD IVAi IVDi CONi HIS OUR IND CNT\"');\n+}\n+header('Content-type: application/json');\n+header('Access-Control-Allow-Origin: *');\n+header('Access-Control-Allow-Credentials: true');\n+header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');\n+//header('Access-Control-Allow-Headers', 'x-requested-with, X-CSRFToken');\n+header('Access-Control-Max-Age: 1000');\n+\n+$metodo = $_SERVER['REQUEST_METHOD'];\n+$body = file_get_contents('php://input');\n+$url = filter_input(INPUT_GET, 'url', FILTER_SANITIZE_URL);\n+$iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+if (!$iue && isset($_GET['IUE']))\n+    $iue = filter_input(INPUT_GET, 'IUE', FILTER_SANITIZE_URL);\n+\n+$recursos = array();\n+$temp_array = array_filter(explode('/', $url));\n+\n+for ($i = 0, $cantidad = count($temp_array); $i < $cantidad; $i++) {\n+    if (!in_array($temp_array[$i], ['api', '$1', 'v0.1', 'v0.2', 'ml', 'v1'])) {\n+        if (strpos($temp_array[$i], '=') === false) {\n+            $recursos[] = $temp_array[$i];\n+        } else {\n+            $temp_array = explode('=', $temp_array[$i]);\n+            $recursos[$temp_array[0]] = $temp_array[1];\n+        }\n+    }\n+}\n+\n+$client = new SaasClient();\n+$client->setCallback(filter_input(INPUT_GET, 'callback', FILTER_SANITIZE_URL));\n+\n+// PARCHE: otro parche horrible porque no puedo hacer que me tome la misma session_id() así que lo fuerzo enviándolo\n+if ($_GET['S']) {\n+    session_id($_GET['S']);\n+} elseif ($_POST['S']) {\n+    session_id($_POST['S']);\n+} elseif ($metodo == \"PUT\" || $metodo == \"DELETE\") {\n+    parse_str($body, $parametros);\n+    if ($parametros['S']) {\n+        session_id($parametros['S']);\n+    }\n+}\n+session_start();\n+\n+// LIMITACIÓN PARA BLOQUEAR CONSUTLAS EN PARALELAS AL MISMO RECURSO'_'.$recursos[0].\n+$consultando_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.consultando_api';\n+$recursos_consultas_paralelas = ['rubros'];\n+if (file_exists($consultando_api)\n+    && !in_array($recursos[0], $recursos_consultas_paralelas)\n+    && (!isset($_GET['utm_source']) || filter_input(INPUT_GET, 'utm_source') !== \"sv-woo-sync-saas-1.7\")) {\n+    file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n+        . ' - No se pueden ejecutar consultas en paralelo al mismo recurso: '\n+        . $iue.'_'.$recursos[0], FILE_APPEND);\n+    http_response_code(429);\n+    exit(json_encode(array('error' => 'No se pueden ejecutar consultas en paralelo al mismo recurso')));\n+}\n+\n+// Limitar llamadas a la API por segundo por recurso\n+$timestamp_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.timestamp_api';\n+\n+// Verificar si existe el archivo timestamp y si ha pasado menos de 1 segundo\n+if (file_exists($timestamp_api) && time() - filemtime($timestamp_api) < 1) {\n+    // Si es con listado completo bloqueo\n+    if (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_STRING) === 'completo') {\n+        file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n+            . ' - Recurso completo bloqueado con estos parámetros: '.json_encode($_GET), FILE_APPEND);\n+        touch($consultando_api);\n+        exit(json_encode(array(\n+            'error' => 'Rate limit exceeded - Bloqueado por varias peticiones con cantidad completo en un mismo segundo',\n+            'retry_after' => 1\n+        )));\n+    }\n+    // Por ahora no bloqueo las otras consultas\n+    /*\n+    http_response_code(429); // Too Many Requests\n+    exit(json_encode(array(\n+        'error' => 'Rate limit exceeded - Varias peticiones en un mismo segundo',\n+        'retry_after' => 1\n+    )));\n+    */\n+}\n+\n+\n+touch($consultando_api);\n+touch($timestamp_api);\n+\n+// PARCHE: Mando los datos a un log para saber que pasa\n+file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\") . ' - ' . $_SERVER['SERVER_NAME'], FILE_APPEND);\n+file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'REQUEST: ' . $metodo . \" \" . $url, FILE_APPEND);\n+file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'BODY: ' . $body, FILE_APPEND);\n+file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'GET: ' . json_encode($_GET), FILE_APPEND);\n+file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'POST: ' . json_encode($_POST), FILE_APPEND);\n+file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'PARAMETROS: ' . json_encode($parametros), FILE_APPEND);\n+file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'SESSION_ID: ' . session_id(), FILE_APPEND);\n+\n+\n+switch ($recursos[0]) {\n+    default:\n+        $client->rechazarRecurso(400, 'Recurso inválido');\n+        break;\n+\n+    case \"notificaciones\":\n+        switch ($recursos[1]) {\n+            default:\n+                $client->rechazarRecurso(400, 'Tipo de notificación inválidas');\n+                break;\n+        }\n+        break;\n+\n+    case 'productos':\n+        if ($metodo == 'GET') {\n+            if (!$recursos[1]) {\n+                $datos = array(\n+                    'iue' => $iue,\n+                    'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_STRING),\n+                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n+                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 10, \"min_range\" => 1, \"max_range\" => 100)))),\n+                    'modificados' => filter_input(INPUT_GET, 'modificados', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 0, \"min_range\" => 1, \"max_range\" => 1000))),\n+                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n+                    'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n+                    'idrubro' => filter_input(INPUT_GET, 'idrubro', FILTER_SANITIZE_NUMBER_INT),\n+                    'ML_item_id' => filter_input(INPUT_GET, 'ML_item_id', FILTER_SANITIZE_NUMBER_INT),\n+                    'mostrarcosto' => filter_input(INPUT_GET, 'mostrarcosto', FILTER_SANITIZE_NUMBER_INT),\n+                    'mostrarsinstock' => filter_input(INPUT_GET, 'mostrarsinstock', FILTER_SANITIZE_NUMBER_INT),\n+                    'mostrarimagenes' => filter_input(INPUT_GET, 'mostrarimagenes', FILTER_SANITIZE_NUMBER_INT),\n+                    'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n+                    'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n+                    'tipo' => (filter_input(INPUT_GET, 'tipo', FILTER_SANITIZE_STRING) ? filter_input(INPUT_GET, 'tipo', FILTER_SANITIZE_STRING) : 'completo'),\n+                    'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_NUMBER_INT),\n+                    'mostrarproveedor' => filter_input(INPUT_GET, 'mostrarproveedor', FILTER_SANITIZE_NUMBER_INT),\n+                );\n+                // Por si no funcionan las opciones del filter_input\n+                if (!$datos['cantidad']) {\n+                    $datos['cantidad'] = '10';\n+                } else if (intval($datos['cantidad']) > 100) {\n+                    $datos['cantidad'] = '100';\n+                }\n+                if (intval($datos['desde']) < 1) {\n+                    $datos['desde'] = 0;\n+                }\n+\n+                $client->listarProductos($datos);\n+            } else {\n+                $datos = array(\n+                    'producto' => $recursos[1],\n+                    'iue' => $iue,\n+                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n+                    'mostrarcosto' => filter_input(INPUT_GET, 'mostrarcosto', FILTER_SANITIZE_URL),\n+                    'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_URL),\n+                    'mostrarimagenes' => filter_input(INPUT_GET, 'mostrarimagenes', FILTER_SANITIZE_URL),\n+                    'mostrarproveedor' => filter_input(INPUT_GET, 'mostrarproveedor', FILTER_SANITIZE_URL),\n+                    'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n+                    'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n+                );\n+                $client->verProducto($datos);\n+            }\n+        } elseif ($metodo == 'POST') {\n+            if (!$recursos[1]) {\n+                $datos = file_get_contents('php://input');\n+\n+                $client->agregarProducto($datos);\n+            } else {\n+                switch ($recursos[1]) {\n+                    case \"sincronizar\":\n+                        $datos = file_get_contents('php://input');\n+                        $client->modificarProductos($datos);\n+                        break;\n+\n+                    case \"eliminar\":\n+                        if ($recursos[2]) {\n+                            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);\n+                            $idProducto = $recursos[2];\n+                            $client->eliminarProducto($iue, $idProducto);\n+                        } else {\n+                            // TODO: Falta rechazar el llamado acá\n+                        }\n+                        break;\n+                }\n+            }\n+        } elseif ($metodo == 'PUT') {\n+            // Acá va el servicio para modificar productos\n+            $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n+        } elseif ($metodo == 'DELETE') {\n+            // Acá va el servicio para eliminar productos\n+            $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n+        }\n+        break;\n+\n+    case 'combos':\n+    case 'combosbycodigo':\n+        if ($metodo == 'GET') {\n+            if (!$recursos[1]) {\n+\n+                $datos = array(\n+                    'iue' => $iue,\n+                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n+                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 100, \"min_range\" => 1, \"max_range\" => 1000)))),\n+                );\n+                $client->listarCombos($datos);\n+\n+            } else {\n+                $datos = array(\n+                    'iue' => $iue,\n+                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n+                    'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n+                    'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),             );\n+                if ($recursos[0] == 'combos') {\n+                    $datos['idproducto'] = $recursos[1];\n+                } elseif ($recursos[0] == 'combosbycodigo') {\n+                    $datos['codigo'] = $recursos[1];\n+                } else {\n+                    $client->rechazarRecurso(400, 'Error en id');\n+                    break;\n+                }\n+                $client->verCombo($datos);\n+            }\n+\n+        } elseif ($metodo == 'DELETE') {\n+            $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+            if ($recursos[0] == 'combos') {\n+                $datos['idproducto'] = $recursos[1];\n+            } elseif ($recursos[0] == 'combosbycodigo') {\n+                $datos['codigo'] = $recursos[1];\n+            }\n+            $client->eliminarCombo($datos);\n+        } else {\n+            if ($metodo == 'POST' || $metodo == 'PUT') {\n+                $inputJSON = json_decode(file_get_contents('php://input'), true);\n+                $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+                $datos['body'] = $inputJSON;\n+\n+                if ($recursos[0] == 'combos') {\n+                    $datos['idproducto'] = $recursos[1];\n+                } elseif ($recursos[0] == 'combosbycodigo') {\n+                    $datos['codigo'] = $recursos[1];\n+                } else {\n+                    $client->rechazarRecurso(400, 'Error en id');\n+                    break;\n+                }\n+\n+                $client->editarCombo($datos, $metodo);\n+            } else {\n+                $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n+            }\n+        }\n+        break;\n+\n+    case 'rubros':\n+        if ($metodo != 'GET') {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n+        } else {\n+            $datos = array(\n+                'iue' => $iue,\n+                'idrubro' => filter_input(INPUT_GET, 'idrubro', FILTER_SANITIZE_URL),    //Agrego sub-rubros\n+                'incluir_subrubros' => filter_input(INPUT_GET, 'incluir_subrubros', FILTER_SANITIZE_URL),\n+            );\n+            $client->listarRubros($datos);\n+        }\n+        break;\n+\n+    case 'ventas':\n+        if ($metodo == 'POST') {\n+            if ($recursos[1]) {\n+                $idventa = $recursos[1];\n+                $datos = array(\n+                    'venta' => $idventa,\n+                    'external_reference' => filter_input(INPUT_POST, 'external_reference', FILTER_SANITIZE_URL),\n+                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),\n+                    'datosextras' => filter_input(INPUT_POST, 'datosextras', FILTER_SANITIZE_URL),\n+                );\n+                $client->cargarExternalReference($datos);\n+            } else {\n+\n+                $datos = array(\n+                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),\n+                    'idtipoventa' => filter_input(INPUT_POST, 'idtipoventa', FILTER_SANITIZE_URL),\n+                    'idcliente' => filter_input(INPUT_POST, 'idcliente', FILTER_SANITIZE_URL),\n+                    'condicionventa' => filter_input(INPUT_POST, 'condicionventa', FILTER_SANITIZE_URL),\n+                    'fecha' => filter_input(INPUT_POST, 'fecha', FILTER_SANITIZE_URL),\n+                    'vencimiento1' => filter_input(INPUT_POST, 'vencimiento1', FILTER_SANITIZE_URL),\n+                    'vencimiento2' => filter_input(INPUT_POST, 'vencimiento2', FILTER_SANITIZE_URL),\n+                    'descuento' => filter_input(INPUT_POST, 'descuento', FILTER_SANITIZE_URL),\n+                    'observacion' => filter_input(INPUT_POST, 'observacion', FILTER_SANITIZE_URL),\n+                    'MP_external_reference' => filter_input(INPUT_POST, 'MP_external_reference', FILTER_SANITIZE_URL),\n+                    'productos' => array(),\n+                );\n+\n+                $i = 1;\n+                while (isset($_POST[$i . '_idproducto']) || isset($_POST[$i . '_nombre'])) {\n+                    $datos['productos'][] = array(\n+                        $i . '_idproducto' => filter_input(INPUT_POST, $i . '_idproducto'),\n+                        $i . '_codigo' => filter_input(INPUT_POST, $i . '_codigo'),\n+                        $i . '_cantidad' => filter_input(INPUT_POST, $i . '_cantidad'),\n+                        $i . '_idunidad' => filter_input(INPUT_POST, $i . '_idunidad'),\n+                        $i . '_idiva' => filter_input(INPUT_POST, $i . '_idiva'),\n+                        $i . '_nombre' => filter_input(INPUT_POST, $i . '_nombre'),\n+                        $i . '_costo' => filter_input(INPUT_POST, $i . '_costo'),\n+                        $i . '_precio' => filter_input(INPUT_POST, $i . '_precio'),\n+                        $i . '_descuento' => filter_input(INPUT_POST, $i . '_descuento'),\n+                        $i . '_observacion' => filter_input(INPUT_POST, $i . '_observacion'),\n+                    );\n+                    $i++;\n+                }\n+            }\n+        } else if ($metodo == 'GET') {\n+            if (!is_numeric($recursos[1])) {\n+                $datos = array(\n+                    'iue' => $iue,\n+                    'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_URL),\n+                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL),\n+                    'cantidad' => filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL),\n+                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n+                    'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n+                    'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_URL),\n+                );\n+                if (!$datos['cantidad']) {\n+                    $datos['cantidad'] = '10';\n+                }\n+\n+                $client->listarVentas($datos);\n+            } else {\n+                $idventa = $recursos[1];\n+                $datos = array(\n+                    'venta' => $idventa,\n+                    'iue' => $iue,\n+                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n+                );\n+                $client->verVenta($datos);\n+            }\n+        } else {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta GET, PUT y POST como métodos');\n+        }\n+        break;\n+\n+    case 'pedidos':\n+        if ($metodo == 'GET' && !$recursos[1]) {\n+            $datos = array(\n+                'iue' => $iue,\n+                'busqueda' => '',\n+                'desde' => '',\n+                'cantidad' => '',\n+                'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n+                'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n+                'idrubro' => '',\n+                'mostrarcosto' => filter_input(INPUT_GET, 'mostrarcosto', FILTER_SANITIZE_URL),\n+            );\n+            if (!$datos['cantidad']) {\n+                $datos['cantidad'] = '10';\n+            }\n+\n+            if (!$datos['desde']) {\n+                $datos['desde'] = '0';\n+            }\n+\n+            $client->verPedido($datos);\n+        } elseif ($metodo == 'POST') {\n+            // TODO: Validar que tanto el idproducto, como la cantidad sean enteros, además hay que verificar que el idproducto exista\n+            $idproducto = $recursos[1];\n+            $cantidad = $recursos[2];\n+            $client->agregarProductoAlCarrito($idproducto, $cantidad);\n+        } elseif ($metodo == 'PUT') {\n+            $idproducto == $recursos[1];\n+            // TODO: Falta implementar este recurso\n+            // Actualiza un producto del pedido, por ejemplo la cantidad. Si no existe en el pedido ver si rechazamos la solicitud o lo agregamos\n+        } elseif ($metodo == 'DELETE') {\n+            $idproducto = $recursos[1];\n+            $client->eliminarProductoDelCarrito($idproducto);\n+        }\n+        break;\n+\n+    case 'cerrar':\n+        if ($metodo != 'POST') {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n+        } else {\n+            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);\n+            $external_reference = filter_input(INPUT_POST, 'external_reference', FILTER_SANITIZE_URL);\n+            $datos = array(\n+                'formapago' => filter_input(INPUT_POST, 'formapago', FILTER_SANITIZE_URL),\n+                'observacion' => filter_input(INPUT_POST, 'observacion', FILTER_SANITIZE_STRING),\n+                'envio' => filter_input(INPUT_POST, 'envio', FILTER_SANITIZE_STRING),\n+                'descripcionenvio' => filter_input(INPUT_POST, 'descripcionenvio', FILTER_SANITIZE_STRING),\n+                'importeenvio' => filter_input(INPUT_POST, 'importeenvio', FILTER_SANITIZE_STRING),\n+                'idextra' => filter_input(INPUT_POST, 'idextra', FILTER_SANITIZE_STRING),\n+                'direccion' => filter_input(INPUT_POST, 'direccion', FILTER_SANITIZE_STRING),\n+                'ivaenvio' => filter_input(INPUT_POST, 'ivaenvio', FILTER_SANITIZE_STRING)\n+            );\n+            if (!$iue)\n+                $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+            $client->cerrarPedido($iue, $external_reference, $datos);\n+        }\n+        break;\n+\n+    case 'pedido':\n+    case 'cerrarpedidoconitems':\n+        if ($metodo != 'POST') {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n+        } else {\n+            $inputJSON = json_decode(file_get_contents('php://input'), true);\n+            $iue = $inputJSON['iue'];\n+            $datos = [];\n+            $datos['items'] = $inputJSON['items'];\n+            $datos['descuento'] = $inputJSON['descuento'];\n+            $datos['total'] = $inputJSON['total'];\n+            $datos['idlista'] = $inputJSON['idlista'];\n+            $datos['iddeposito'] = $inputJSON['iddeposito'];\n+            $datos['idtipoventa'] = $inputJSON['idtipoventa'];\n+            $datos['estado'] = $inputJSON['estado'];\n+            $datos['cliente'] = $inputJSON['cliente'];\n+            $datos['extras'] = $inputJSON['datosextra'];\n+            $datos['observacion'] = $inputJSON['observacion'];\n+            $datos['external_reference'] = $inputJSON['external_reference'];\n+\n+            if ($iue) {\n+                $client->cerrarPedidoConItems($iue, $datos);\n+            }\n+        }\n+        break;\n+\n+        case 'anularventa':\n+            if ($metodo != 'POST') {\n+                $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n+            } else {\n+                $inputJSON = json_decode(file_get_contents('php://input'), true);\n+                $iue = $inputJSON['iue'];\n+                $idventa = $inputJSON['idventa'];\n+\n+                if ($iue) {\n+                    $client->anularventa($iue, $idventa);\n+                }\n+            }\n+            break;\n+\n+    case 'cerrarconget':\n+        if ($metodo != 'GET') {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n+        } else {\n+            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+            $external_reference = filter_input(INPUT_GET, 'external_reference', FILTER_SANITIZE_URL);\n+            $client->cerrarPedido($iue, $external_reference, false);\n+        }\n+        break;\n+    case 'cerrarConDatosExtra':\n+        if ($metodo != 'POST') {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n+        } else {\n+            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);\n+            $external_reference = filter_input(INPUT_POST, 'external_reference', FILTER_SANITIZE_URL);\n+            $extras = json_decode(filter_input(INPUT_POST, 'extras', FILTER_SANITIZE_URL));\n+            $client->cerrarPedidoConDatosExtra($iue, $external_reference, $extras);\n+        }\n+        break;\n+\n+    case 'usuarios':\n+        if ($metodo == 'GET') {\n+            if (!$recursos[1]) {\n+                $client->rechazarRecurso(400, 'Recurso invalido');\n+            } elseif ($recursos[1] != $_SESSION['usuario']) {\n+                $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');\n+            } else {\n+                file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n GET 1 \" . $recursos[1], FILE_APPEND);\n+                $datos = array(\n+                    'iue' => $iue,\n+                    'idcliente' => $recursos[1],\n+                );\n+                $client->verUsuario($datos);\n+            }\n+\n+            // Para crear a un usuario debería venir por PUT, pero no me funcionó, así que lo hago por POST por ahora\n+        } elseif ($metodo == 'PUT') {\n+            $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');\n+        } elseif ($metodo == 'POST') {\n+            if (!$recursos[1] && !$_SESSION['usuario']) {\n+                $datos = array(\n+                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),\n+                    'idcliente' => $recursos[1],\n+                    'estado' => 0,\n+                    'nombre' => filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_STRING),\n+                    'mail' => filter_input(INPUT_POST, 'mail', FILTER_VALIDATE_EMAIL),\n+                    'pass' => filter_input(INPUT_POST, 'pass', FILTER_SANITIZE_STRING),\n+                    'telefonos' => filter_input(INPUT_POST, 'telefonos', FILTER_SANITIZE_STRING),\n+                    'domicilio' => filter_input(INPUT_POST, 'domicilio', FILTER_SANITIZE_STRING),\n+                    'idlocalidad' => filter_input(INPUT_POST, 'idlocalidad', FILTER_SANITIZE_NUMBER_INT),\n+                    'dni' => filter_input(INPUT_POST, 'dni', FILTER_VALIDATE_FLOAT),\n+                    'idtipoiva' => filter_input(INPUT_POST, 'idtipoiva', FILTER_SANITIZE_NUMBER_INT),\n+                    'razonSocial' => filter_input(INPUT_POST, 'razonSocial', FILTER_SANITIZE_STRING),\n+                    'cuit' => filter_input(INPUT_POST, 'cuit', FILTER_VALIDATE_FLOAT),\n+                );\n+                if (!$datos['iue'])\n+                    $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+                $client->crearUsuario($datos);\n+            } elseif ($recursos[1] && $recursos[1] == $_SESSION['usuario']) {\n+                $datos = array(\n+                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),\n+                    'idcliente' => $recursos[1],\n+                    'nombre' => isset($_POST['nombre'])\n+                        ? filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_STRING)\n+                        : false,\n+                    'mail' => isset($_POST['mail'])\n+                        ? filter_input(INPUT_POST, 'mail', FILTER_VALIDATE_EMAIL)\n+                        : false,\n+                    'pass' => isset($_POST['pass'])\n+                        ? filter_input(INPUT_POST, 'pass', FILTER_SANITIZE_STRING)\n+                        : false,\n+                    'telefonos' => isset($_POST['telefonos'])\n+                        ? filter_input(INPUT_POST, 'telefonos', FILTER_SANITIZE_STRING)\n+                        : false,\n+                    'domicilio' => isset($_POST['domicilio'])\n+                        ? filter_input(INPUT_POST, 'domicilio', FILTER_SANITIZE_STRING)\n+                        : false,\n+                    'idlocalidad' => isset($_POST['idlocalidad'])\n+                        ? filter_input(INPUT_POST, 'idlocalidad', FILTER_SANITIZE_NUMBER_INT)\n+                        : false,\n+                    'dni' => isset($_POST['dni'])\n+                        ? filter_input(INPUT_POST, 'dni', FILTER_VALIDATE_FLOAT)\n+                        : false,\n+                    'idtipoiva' => isset($_POST['idtipoiva'])\n+                        ? filter_input(INPUT_POST, 'idtipoiva', FILTER_SANITIZE_NUMBER_INT)\n+                        : false,\n+                    'razonSocial' => isset($_POST['razonSocial'])\n+                        ? filter_input(INPUT_POST, 'razonSocial', FILTER_SANITIZE_STRING)\n+                        : false,\n+                    'cuit' => isset($_POST['cuit'])\n+                        ? filter_input(INPUT_POST, 'cuit', FILTER_VALIDATE_FLOAT)\n+                        : false,\n+                );\n+                if (!$datos['iue'])\n+                    $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+                $client->modificarUsuario($datos);\n+            } else {\n+                $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');\n+            }\n+        } else {\n+            $client->rechazarRecurso(400, 'Recurso invalido');\n+        }\n+        break;\n+\n+    case \"pass\":\n+        if ($metodo != 'POST') {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n+        } else {\n+            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);\n+            $id_usuario = filter_input(INPUT_POST, 'id_usuario', FILTER_SANITIZE_NUMBER_INT);\n+            $pass_actual = filter_input(INPUT_POST, 'pass_actual', FILTER_SANITIZE_URL);\n+            $pass_nuevo = filter_input(INPUT_POST, 'pass_nuevo', FILTER_SANITIZE_URL);\n+            $client->modificarPassUsuario($iue, $id_usuario, $pass_actual, $pass_nuevo);\n+        }\n+        break;\n+\n+    case \"login\":\n+        if ($metodo == 'GET') {\n+            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+\n+            $email = filter_input(INPUT_GET, 'email', FILTER_VALIDATE_EMAIL);\n+            $pass = filter_input(INPUT_GET, 'pass', FILTER_SANITIZE_URL);\n+            $client->validarUsuario($iue, $email, $pass);\n+        } else {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n+        }\n+        break;\n+\n+    case \"loginsinpw\":\n+        if ($metodo == 'GET') {\n+            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+            $email = filter_input(INPUT_GET, 'email', FILTER_VALIDATE_EMAIL);\n+            $client->validarUsuarioSinPw($iue, $email);\n+        } else {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n+        }\n+        break;\n+\n+    case \"validar\":\n+        if ($metodo == 'GET') {\n+            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+            $random = filter_input(INPUT_GET, 'random', FILTER_SANITIZE_URL);\n+            $mail = filter_input(INPUT_GET, 'mail', FILTER_VALIDATE_EMAIL);\n+\n+            switch ($recursos[1]) {\n+                default:\n+                    $client->rechazarRecurso(400, 'Recurso inválido');\n+                    break;\n+\n+                case 'mail':\n+                    if ($iue && $random) {\n+                        $client->confirmarMail($iue, $random);\n+                    } else {\n+                        $client->rechazarRecurso(400, 'Recurso inválido');\n+                    }\n+                    break;\n+\n+                case 'pass':\n+                    if ($iue && $random) {\n+                        $client->confirmarPass($iue, $random);\n+                    } else {\n+                        $client->rechazarRecurso(400, 'Recurso inválido');\n+                    }\n+                    break;\n+\n+                case 'escliente':\n+                    if ($iue && $mail) {\n+                        $client->esCliente($iue, $mail, $pass);\n+                    } else {\n+                        $client->rechazarRecurso(400, 'Recurso inválido');\n+                    }\n+                    break;\n+\n+                case 'esclienteml':\n+                    $datos = array(\n+                        'iue' => $iue,\n+                        'apodo' => filter_input(INPUT_GET, 'apodo', FILTER_SANITIZE_STRING),\n+                        'order_id' => filter_input(INPUT_GET, 'order_id', FILTER_SANITIZE_URL)\n+                    );\n+                    $client->esClienteMl($datos);\n+                    break;\n+\n+                case 'resetearpass':\n+                    if ($iue && $mail) {\n+                        $client->resetearPass($iue, $mail);\n+                    } else {\n+                        $client->rechazarRecurso(400, 'Recurso inválido');\n+                    }\n+                    break;\n+            }\n+        } else {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n+        }\n+        break;\n+\n+    case \"forzarmail\":\n+        if ($metodo == 'POST') {\n+            $datos = array(\n+                'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),\n+                'id_cliente' => filter_input(INPUT_POST, 'id_cliente', FILTER_SANITIZE_URL),\n+                'mail' => filter_input(INPUT_POST, 'mail', FILTER_SANITIZE_URL)\n+            );\n+            $client->forzarMail($datos);\n+        } else {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n+        }\n+        break;\n+\n+    case \"tablas\":\n+        if ($metodo == 'GET') {\n+            switch ($recursos[1]) {\n+                default:\n+                    $client->rechazarRecurso(400, 'Tipo de tabla inválido');\n+                    break;\n+\n+                case 'iva':\n+                    $datos = array(\n+                        'iue' => $iue,\n+                    );\n+                    $client->listarTiposIva($datos);\n+                    break;\n+            }\n+        } else {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n+        }\n+        break;\n+\n+    case \"passForzada\":\n+        if ($metodo != 'POST') {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n+        } else {\n+            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);\n+            $id_usuario = filter_input(INPUT_POST, 'id_usuario', FILTER_SANITIZE_NUMBER_INT);\n+            $pass_nuevo = filter_input(INPUT_POST, 'pass_nuevo', FILTER_SANITIZE_URL);\n+            $client->modificarPassUsuarioForzada($iue, $id_usuario, $pass_nuevo);\n+        }\n+        break;\n+\n+    case 'cajas':\n+        if ($metodo == 'GET') {\n+            if ($recursos[1]) {\n+                $client->rechazarRecurso(400, 'Recurso invalido');\n+            } else {\n+                $datos = array(\n+                    'iue' => $iue,\n+                    'idcaja' => filter_input(INPUT_GET, 'idcaja', FILTER_SANITIZE_NUMBER_INT),\n+                    'idtipocaja' => filter_input(INPUT_GET, 'idtipocaja', FILTER_SANITIZE_NUMBER_INT),\n+                    'idconcepto' => filter_input(INPUT_GET, 'idconcepto', FILTER_SANITIZE_NUMBER_INT),\n+                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_STRING),\n+                    'hasta' => filter_input(INPUT_GET, 'hasta', FILTER_SANITIZE_STRING)\n+                );\n+                $client->listarMovimientosCajas($datos);\n+            }\n+        } else {\n+            $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');\n+        }\n+        break;\n+\n+    case 'iva':\n+        if ($metodo == 'GET') {\n+            switch ($recursos[1]) {\n+                default:\n+                    $client->rechazarRecurso(400, 'Tipo de tabla inválido');\n+                    break;\n+\n+                case 'ventas':\n+                    $datos = array(\n+                        'iue' => $iue,\n+                        'periodo' => filter_input(INPUT_GET, 'periodo', FILTER_SANITIZE_URL)\n+                    );\n+                    $client->listarLibroIvaVentas($datos);\n+                    break;\n+\n+                case 'compras':\n+                    $datos = array(\n+                        'iue' => $iue,\n+                        'periodo' => filter_input(INPUT_GET, 'periodo', FILTER_SANITIZE_URL)\n+                    );\n+                    $client->listarLibroIvaCompras($datos);\n+                    break;\n+            }\n+        } else {\n+            $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');\n+        }\n+        break;\n+\n+    case 'botonmercadopago':\n+        require PATH_TOOLS . 'mercadopago/mercadopago.php';\n+        if ($metodo != 'GET') {\n+            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n+        } else {\n+            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+            $datos = array(\n+                'titulo' => filter_input(INPUT_GET, 'titulo', FILTER_SANITIZE_URL),\n+                'cantidad' => filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL),\n+                'precio' => filter_input(INPUT_GET, 'precio', FILTER_SANITIZE_URL),\n+                'external_reference' => filter_input(INPUT_GET, 'external_reference', FILTER_SANITIZE_URL),\n+                'back_urls_success' => filter_input(INPUT_GET, 'back_urls_success', FILTER_SANITIZE_URL),\n+                'back_urls_failure' => filter_input(INPUT_GET, 'back_urls_failure', FILTER_SANITIZE_URL),\n+                'back_urls_pending' => filter_input(INPUT_GET, 'back_urls_pending', FILTER_SANITIZE_URL),\n+                'notification_url' => filter_input(INPUT_GET, 'notification_url', FILTER_SANITIZE_URL),\n+            );\n+            $client->crearBotonMP($datos, $iue);\n+        }\n+        break;\n+\n+        case 'clientes':\n+            if ($metodo == 'GET') {\n+                if (!$recursos[1]) {\n+                    $datos = array(\n+                        'iue' => $iue,\n+                        'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_STRING),\n+                        'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n+                        'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 10, \"min_range\" => 1, \"max_range\" => 100)))),\n+                        'modificados' => filter_input(INPUT_GET, 'modificados', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 0, \"min_range\" => 1, \"max_range\" => 1000))),\n+                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n+                        'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n+                        'idtipocliente' => filter_input(INPUT_GET, 'idtipocliente', FILTER_SANITIZE_NUMBER_INT),\n+                        'idlista' => filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT),\n+                        'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_NUMBER_INT),\n+                        'mostrarmoneda' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_NUMBER_INT),\n+                    );\n+                    // Por si no funcionan las opciones del filter_input\n+                    if (!$datos['cantidad']) {\n+                        $datos['cantidad'] = '10';\n+                    } else if (intval($datos['cantidad']) > 100) {\n+                        $datos['cantidad'] = '100';\n+                    }\n+                    if (intval($datos['desde']) < 1) {\n+                        $datos['desde'] = 0;\n+                    }\n+\n+                    $client->listarClientes($datos);\n+                } else {\n+                    $datos = array(\n+                        'idcliente' => $recursos[1],\n+                        'iue' => $iue,\n+                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n+                    );\n+                    $client->verCliente($datos);\n+                }\n+            } elseif ($metodo == 'POST') {\n+                if (!$recursos[1]) {\n+                    $datos = file_get_contents('php://input');\n+                    $client->agregarCliente($datos);\n+                } else {\n+                    $datos = file_get_contents('php://input');\n+                    $idcliente = $recursos[1];\n+                    $client->modificarCliente($datos, $idcliente);\n+                }\n+            } elseif ($metodo == 'PUT') {\n+                // Acá va el servicio para modificar productos\n+                $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n+            } elseif ($metodo == 'DELETE') {\n+                if ($recursos[1]) {\n+                    $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+                    $idcliente = $recursos[1];\n+                    $client->eliminarCliente($iue, $idcliente);\n+                }\n+            }\n+            break;\n+\n+        case 'servicios':\n+            if ($metodo == 'GET') {\n+                if (!$recursos[1]) {\n+                    $datos = array(\n+                        'iue' => $iue,\n+                        'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_STRING),\n+                        'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n+                        'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 10, \"min_range\" => 1, \"max_range\" => 100)))),\n+                        'modificados' => filter_input(INPUT_GET, 'modificados', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 0, \"min_range\" => 1, \"max_range\" => 1000))),\n+                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n+                        'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n+                        'idtiposervicio' => filter_input(INPUT_GET, 'idtiposervicio', FILTER_SANITIZE_NUMBER_INT),\n+                        'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_NUMBER_INT),\n+                        'estado' => filter_input(INPUT_GET, 'estado', FILTER_SANITIZE_NUMBER_INT),\n+\n+                        'fechasolicitado' => filter_input(INPUT_GET, 'fechasolicitado', FILTER_SANITIZE_STRING),\n+                        'fechasolicitado_desde' => filter_input(INPUT_GET, 'fechasolicitado_desde', FILTER_SANITIZE_STRING),\n+                        'fechasolicitado_hasta' => filter_input(INPUT_GET, 'fechasolicitado_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechainicio' => filter_input(INPUT_GET, 'fechainicio', FILTER_SANITIZE_STRING),\n+                        'fechainicio_desde' => filter_input(INPUT_GET, 'fechainicio_desde', FILTER_SANITIZE_STRING),\n+                        'fechainicio_hasta' => filter_input(INPUT_GET, 'fechainicio_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechafin' => filter_input(INPUT_GET, 'fechafin', FILTER_SANITIZE_STRING),\n+                        'fechafin_desde' => filter_input(INPUT_GET, 'fechafin_desde', FILTER_SANITIZE_STRING),\n+                        'fechafin_hasta' => filter_input(INPUT_GET, 'fechafin_hasta', FILTER_SANITIZE_STRING),\n+\n+                        'fechalimite' => filter_input(INPUT_GET, 'fechalimite', FILTER_SANITIZE_STRING),\n+                        'fechalimite_desde' => filter_input(INPUT_GET, 'fechalimite_desde', FILTER_SANITIZE_STRING),\n+                        'fechalimite_hasta' => filter_input(INPUT_GET, 'fechalimite_hasta', FILTER_SANITIZE_STRING),\n+                    );\n+                    // Por si no funcionan las opciones del filter_input\n+                    if (!$datos['cantidad']) {\n+                        $datos['cantidad'] = '10';\n+                    } else if (intval($datos['cantidad']) > 100) {\n+                        $datos['cantidad'] = '100';\n+                    }\n+                    if (intval($datos['desde']) < 1) {\n+                        $datos['desde'] = 0;\n+                    }\n+\n+                    $client->listarServicios($datos);\n+                } else {\n+                    $datos = array(\n+                        'idservicio' => $recursos[1],\n+                        'iue' => $iue,\n+                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n+                        'pass' => filter_input(INPUT_GET, 'pass', FILTER_SANITIZE_URL),\n+                    );\n+                    $client->verServicio($datos);\n+                }\n+            } elseif ($metodo == 'POST') {\n+                if (!$recursos[1]) {\n+                    $datos = file_get_contents('php://input');\n+                    $client->agregarServicio($datos);\n+                } else {\n+                    $datos = file_get_contents('php://input');\n+                    $idservicio = $recursos[1];\n+                    $client->modificarServicio($datos, $idservicio);\n+                }\n+            } elseif ($metodo == 'PUT') {\n+                // Acá va el servicio para modificar productos\n+                $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n+            } elseif ($metodo == 'DELETE') {\n+                if ($recursos[1]) {\n+                    $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n+                    $idservicio = $recursos[1];\n+                    $client->eliminarServicio($iue, $idservicio);\n+                }\n+            }\n+            break;\n+}\n+\n+unlink($consultando_api);\n\\ No newline at end of file\n"}], "date": 1739385900028, "name": "Commit-0", "content": "<?php\nrequire __DIR__.'/../../../acc/acc.php';\nrequire __DIR__.'/../../vendor/autoload.php';\nrequire __DIR__.'/../../funciones_api.php';\n\n// Esto está deprecado, hay que pasar al autoload\nfunction __autoload($classname) {\n    $filename = $classname . '.php';\n    if (is_readable($filename)) {\n        require_once $filename;\n    }\n}\n\nspl_autoload_register('__autoload');\n\n// PARCHE: Mando un montón de headers para que funcione el cross-domain, pero hay que limpiar y dejar solo los que tienen que ir teniendo en cuenta la seguridad\nif (preg_match(\"/MSIE/i\", $_SERVER['HTTP_USER_AGENT'])) {\n    header('P3P: CP=\"IDC DSP COR ADM DEVi TAIi PSA PSD IVAi IVDi CONi HIS OUR IND CNT\"');\n}\nheader('Content-type: application/json');\nheader('Access-Control-Allow-Origin: *');\nheader('Access-Control-Allow-Credentials: true');\nheader('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');\n//header('Access-Control-Allow-Headers', 'x-requested-with, X-CSRFToken');\nheader('Access-Control-Max-Age: 1000');\n\n$metodo = $_SERVER['REQUEST_METHOD'];\n$body = file_get_contents('php://input');\n$url = filter_input(INPUT_GET, 'url', FILTER_SANITIZE_URL);\n$iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\nif (!$iue && isset($_GET['IUE']))\n    $iue = filter_input(INPUT_GET, 'IUE', FILTER_SANITIZE_URL);\n\n$recursos = array();\n$temp_array = array_filter(explode('/', $url));\n\nfor ($i = 0, $cantidad = count($temp_array); $i < $cantidad; $i++) {\n    if (!in_array($temp_array[$i], ['api', '$1', 'v0.1', 'v0.2', 'ml', 'v1'])) {\n        if (strpos($temp_array[$i], '=') === false) {\n            $recursos[] = $temp_array[$i];\n        } else {\n            $temp_array = explode('=', $temp_array[$i]);\n            $recursos[$temp_array[0]] = $temp_array[1];\n        }\n    }\n}\n\n$client = new SaasClient();\n$client->setCallback(filter_input(INPUT_GET, 'callback', FILTER_SANITIZE_URL));\n\n// PARCHE: otro parche horrible porque no puedo hacer que me tome la misma session_id() así que lo fuerzo enviándolo\nif ($_GET['S']) {\n    session_id($_GET['S']);\n} elseif ($_POST['S']) {\n    session_id($_POST['S']);\n} elseif ($metodo == \"PUT\" || $metodo == \"DELETE\") {\n    parse_str($body, $parametros);\n    if ($parametros['S']) {\n        session_id($parametros['S']);\n    }\n}\nsession_start();\n\n// LIMITACIÓN PARA BLOQUEAR CONSUTLAS EN PARALELAS AL MISMO RECURSO'_'.$recursos[0].\n$consultando_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.consultando_api';\n$recursos_consultas_paralelas = ['rubros'];\nif (file_exists($consultando_api)\n    && !in_array($recursos[0], $recursos_consultas_paralelas)) {\n    file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\")\n        . ' - No se pueden ejecutar consultas en paralelo al mismo recurso: '\n        . $iue.'_'.$recursos[0], FILE_APPEND);\n    http_response_code(429);\n    exit(json_encode(array('error' => 'No se pueden ejecutar consultas en paralelo al mismo recurso')));\n\n} else {\n    touch($consultando_api);\n}\n\n// PARCHE: Mando los datos a un log para saber que pasa\nfile_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . \"\\r\\n\" . date(\"Y-m-d H:i:s\") . ' - ' . $_SERVER['SERVER_NAME'], FILE_APPEND);\nfile_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'REQUEST: ' . $metodo . \" \" . $url, FILE_APPEND);\nfile_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'BODY: ' . $body, FILE_APPEND);\nfile_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'GET: ' . json_encode($_GET), FILE_APPEND);\nfile_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'POST: ' . json_encode($_POST), FILE_APPEND);\nfile_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'PARAMETROS: ' . json_encode($parametros), FILE_APPEND);\nfile_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n\" . 'SESSION_ID: ' . session_id(), FILE_APPEND);\n\n\nswitch ($recursos[0]) {\n    default:\n        $client->rechazarRecurso(400, 'Recurso inválido');\n        break;\n\n    case \"notificaciones\":\n        switch ($recursos[1]) {\n            default:\n                $client->rechazarRecurso(400, 'Tipo de notificación inválidas');\n                break;\n        }\n        break;\n\n    case 'productos':\n        if ($metodo == 'GET') {\n            if (!$recursos[1]) {\n                $datos = array(\n                    'iue' => $iue,\n                    'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_STRING),\n                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 10, \"min_range\" => 1, \"max_range\" => 100)))),\n                    'modificados' => filter_input(INPUT_GET, 'modificados', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 0, \"min_range\" => 1, \"max_range\" => 1000))),\n                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                    'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n                    'idrubro' => filter_input(INPUT_GET, 'idrubro', FILTER_SANITIZE_NUMBER_INT),\n                    'ML_item_id' => filter_input(INPUT_GET, 'ML_item_id', FILTER_SANITIZE_NUMBER_INT),\n                    'mostrarcosto' => filter_input(INPUT_GET, 'mostrarcosto', FILTER_SANITIZE_NUMBER_INT),\n                    'mostrarsinstock' => filter_input(INPUT_GET, 'mostrarsinstock', FILTER_SANITIZE_NUMBER_INT),\n                    'mostrarimagenes' => filter_input(INPUT_GET, 'mostrarimagenes', FILTER_SANITIZE_NUMBER_INT),\n                    'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n                    'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n                    'tipo' => (filter_input(INPUT_GET, 'tipo', FILTER_SANITIZE_STRING) ? filter_input(INPUT_GET, 'tipo', FILTER_SANITIZE_STRING) : 'completo'),\n                    'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_NUMBER_INT),\n                );\n                // Por si no funcionan las opciones del filter_input\n                if (!$datos['cantidad']) {\n                    $datos['cantidad'] = '10';\n                } else if (intval($datos['cantidad']) > 100) {\n                    $datos['cantidad'] = '100';\n                }\n                if (intval($datos['desde']) < 1) {\n                    $datos['desde'] = 0;\n                }\n\n                $client->listarProductos($datos);\n            } else {\n                $datos = array(\n                    'producto' => $recursos[1],\n                    'iue' => $iue,\n                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                    'mostrarcosto' => filter_input(INPUT_GET, 'mostrarcosto', FILTER_SANITIZE_URL),\n                    'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_URL),\n                    'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n                    'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n                );\n                $client->verProducto($datos);\n            }\n        } elseif ($metodo == 'POST') {\n            if (!$recursos[1]) {\n                $datos = file_get_contents('php://input');\n\n                $client->agregarProducto($datos);\n            } else {\n                switch ($recursos[1]) {\n                    case \"sincronizar\":\n                        $datos = file_get_contents('php://input');\n                        $client->modificarProductos($datos);\n                        break;\n\n                    case \"eliminar\":\n                        if ($recursos[2]) {\n                            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);\n                            $idProducto = $recursos[2];\n                            $client->eliminarProducto($iue, $idProducto);\n                        } else {\n                            // TODO: Falta rechazar el llamado acá\n                        }\n                        break;\n                }\n            }\n        } elseif ($metodo == 'PUT') {\n            // Acá va el servicio para modificar productos\n            $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n        } elseif ($metodo == 'DELETE') {\n            // Acá va el servicio para eliminar productos\n            $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n        }\n        break;\n\n    case 'combos':\n    case 'combosbycodigo':\n        if ($metodo == 'GET') {\n            if (!$recursos[1]) {\n\n                $datos = array(\n                    'iue' => $iue,\n                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 10, \"min_range\" => 1, \"max_range\" => 100)))),\n                );\n                $client->verCombos($datos);\n\n            } else {\n                $datos = array(\n                    'iue' => $iue,\n                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                    'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),\n                    'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),             );\n                if ($recursos[0] == 'combos') {\n                    $datos['idproducto'] = $recursos[1];\n                } elseif ($recursos[0] == 'combosbycodigo') {\n                    $datos['codigo'] = $recursos[1];\n                } else {\n                    $client->rechazarRecurso(400, 'Error en id');\n                    break;\n                }\n                $client->verCombo($datos);\n            }\n\n        } elseif ($metodo == 'DELETE') {\n            $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n            if ($recursos[0] == 'combos') {\n                $datos['idproducto'] = $recursos[1];\n            } elseif ($recursos[0] == 'combosbycodigo') {\n                $datos['codigo'] = $recursos[1];\n            }\n            $client->eliminarCombo($datos);\n        } else {\n            if ($metodo == 'POST' || $metodo == 'PUT') {\n                $inputJSON = json_decode(file_get_contents('php://input'), true);\n                $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n                $datos['body'] = $inputJSON;\n\n                if ($recursos[0] == 'combos') {\n                    $datos['idproducto'] = $recursos[1];\n                } elseif ($recursos[0] == 'combosbycodigo') {\n                    $datos['codigo'] = $recursos[1];\n                } else {\n                    $client->rechazarRecurso(400, 'Error en id');\n                    break;\n                }\n\n                $client->editarCombo($datos, $metodo);\n            } else {\n                $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n            }\n        }\n        break;\n\n    case 'rubros':\n        if ($metodo != 'GET') {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n        } else {\n            $datos = array(\n                'iue' => $iue,\n                'idrubro' => filter_input(INPUT_GET, 'idrubro', FILTER_SANITIZE_URL),    //Agrego sub-rubros\n            );\n            $client->listarRubros($datos);\n        }\n        break;\n\n    case 'ventas':\n        if ($metodo == 'POST') {\n            if ($recursos[1]) {\n                $idventa = $recursos[1];\n                $datos = array(\n                    'venta' => $idventa,\n                    'external_reference' => filter_input(INPUT_POST, 'external_reference', FILTER_SANITIZE_URL),\n                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),\n                    'datosextras' => filter_input(INPUT_POST, 'datosextras', FILTER_SANITIZE_URL),\n                );\n                $client->cargarExternalReference($datos);\n            } else {\n\n                $datos = array(\n                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),\n                    'idtipoventa' => filter_input(INPUT_POST, 'idtipoventa', FILTER_SANITIZE_URL),\n                    'idcliente' => filter_input(INPUT_POST, 'idcliente', FILTER_SANITIZE_URL),\n                    'condicionventa' => filter_input(INPUT_POST, 'condicionventa', FILTER_SANITIZE_URL),\n                    'fecha' => filter_input(INPUT_POST, 'fecha', FILTER_SANITIZE_URL),\n                    'vencimiento1' => filter_input(INPUT_POST, 'vencimiento1', FILTER_SANITIZE_URL),\n                    'vencimiento2' => filter_input(INPUT_POST, 'vencimiento2', FILTER_SANITIZE_URL),\n                    'descuento' => filter_input(INPUT_POST, 'descuento', FILTER_SANITIZE_URL),\n                    'observacion' => filter_input(INPUT_POST, 'observacion', FILTER_SANITIZE_URL),\n                    'MP_external_reference' => filter_input(INPUT_POST, 'MP_external_reference', FILTER_SANITIZE_URL),\n                    'productos' => array(),\n                );\n\n                $i = 1;\n                while (isset($_POST[$i . '_idproducto']) || isset($_POST[$i . '_nombre'])) {\n                    $datos['productos'][] = array(\n                        $i . '_idproducto' => filter_input(INPUT_POST, $i . '_idproducto'),\n                        $i . '_codigo' => filter_input(INPUT_POST, $i . '_codigo'),\n                        $i . '_cantidad' => filter_input(INPUT_POST, $i . '_cantidad'),\n                        $i . '_idunidad' => filter_input(INPUT_POST, $i . '_idunidad'),\n                        $i . '_idiva' => filter_input(INPUT_POST, $i . '_idiva'),\n                        $i . '_nombre' => filter_input(INPUT_POST, $i . '_nombre'),\n                        $i . '_costo' => filter_input(INPUT_POST, $i . '_costo'),\n                        $i . '_precio' => filter_input(INPUT_POST, $i . '_precio'),\n                        $i . '_descuento' => filter_input(INPUT_POST, $i . '_descuento'),\n                        $i . '_observacion' => filter_input(INPUT_POST, $i . '_observacion'),\n                    );\n                    $i++;\n                }\n            }\n        } else if ($metodo == 'GET') {\n            if (!is_numeric($recursos[1])) {\n                $datos = array(\n                    'iue' => $iue,\n                    'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_URL),\n                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL),\n                    'cantidad' => filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL),\n                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                    'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n                    'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_URL),\n                );\n                if (!$datos['cantidad']) {\n                    $datos['cantidad'] = '10';\n                }\n\n                $client->listarVentas($datos);\n            } else {\n                $idventa = $recursos[1];\n                $datos = array(\n                    'venta' => $idventa,\n                    'iue' => $iue,\n                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                );\n                $client->verVenta($datos);\n            }\n        } else {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta GET, PUT y POST como métodos');\n        }\n        break;\n\n    case 'pedidos':\n        if ($metodo == 'GET' && !$recursos[1]) {\n            $datos = array(\n                'iue' => $iue,\n                'busqueda' => '',\n                'desde' => '',\n                'cantidad' => '',\n                'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n                'idrubro' => '',\n                'mostrarcosto' => filter_input(INPUT_GET, 'mostrarcosto', FILTER_SANITIZE_URL),\n            );\n            if (!$datos['cantidad']) {\n                $datos['cantidad'] = '10';\n            }\n\n            if (!$datos['desde']) {\n                $datos['desde'] = '0';\n            }\n\n            $client->verPedido($datos);\n        } elseif ($metodo == 'POST') {\n            // TODO: Validar que tanto el idproducto, como la cantidad sean enteros, además hay que verificar que el idproducto exista\n            $idproducto = $recursos[1];\n            $cantidad = $recursos[2];\n            $client->agregarProductoAlCarrito($idproducto, $cantidad);\n        } elseif ($metodo == 'PUT') {\n            $idproducto == $recursos[1];\n            // TODO: Falta implementar este recurso\n            // Actualiza un producto del pedido, por ejemplo la cantidad. Si no existe en el pedido ver si rechazamos la solicitud o lo agregamos\n        } elseif ($metodo == 'DELETE') {\n            $idproducto = $recursos[1];\n            $client->eliminarProductoDelCarrito($idproducto);\n        }\n        break;\n\n    case 'cerrar':\n        if ($metodo != 'POST') {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n        } else {\n            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);\n            $external_reference = filter_input(INPUT_POST, 'external_reference', FILTER_SANITIZE_URL);\n            $datos = array(\n                'formapago' => filter_input(INPUT_POST, 'formapago', FILTER_SANITIZE_URL),\n                'observacion' => filter_input(INPUT_POST, 'observacion', FILTER_SANITIZE_STRING),\n                'envio' => filter_input(INPUT_POST, 'envio', FILTER_SANITIZE_STRING),\n                'descripcionenvio' => filter_input(INPUT_POST, 'descripcionenvio', FILTER_SANITIZE_STRING),\n                'importeenvio' => filter_input(INPUT_POST, 'importeenvio', FILTER_SANITIZE_STRING),\n                'idextra' => filter_input(INPUT_POST, 'idextra', FILTER_SANITIZE_STRING),\n                'direccion' => filter_input(INPUT_POST, 'direccion', FILTER_SANITIZE_STRING),\n                'ivaenvio' => filter_input(INPUT_POST, 'ivaenvio', FILTER_SANITIZE_STRING)\n            );\n            if (!$iue)\n                $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n            $client->cerrarPedido($iue, $external_reference, $datos);\n        }\n        break;\n\n    case 'pedido':\n    case 'cerrarpedidoconitems':\n        if ($metodo != 'POST') {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n        } else {\n            $inputJSON = json_decode(file_get_contents('php://input'), true);\n            $iue = $inputJSON['iue'];\n            $datos = [];\n            $datos['items'] = $inputJSON['items'];\n            $datos['descuento'] = $inputJSON['descuento'];\n            $datos['total'] = $inputJSON['total'];\n            $datos['idlista'] = $inputJSON['idlista'];\n            $datos['iddeposito'] = $inputJSON['iddeposito'];\n            $datos['idtipoventa'] = $inputJSON['idtipoventa'];\n            $datos['estado'] = $inputJSON['estado'];\n            $datos['cliente'] = $inputJSON['cliente'];\n            $datos['extras'] = $inputJSON['datosextra'];\n            $datos['observacion'] = $inputJSON['observacion'];\n            $datos['external_reference'] = $inputJSON['external_reference'];\n\n            if ($iue) {\n                $client->cerrarPedidoConItems($iue, $datos);\n            }\n        }\n        break;\n\n        case 'anularventa':\n            if ($metodo != 'POST') {\n                $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n            } else {\n                $inputJSON = json_decode(file_get_contents('php://input'), true);\n                $iue = $inputJSON['iue'];\n                $idventa = $inputJSON['idventa'];\n\n                if ($iue) {\n                    $client->anularventa($iue, $idventa);\n                }\n            }\n            break;\n\n    case 'cerrarconget':\n        if ($metodo != 'GET') {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n        } else {\n            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n            $external_reference = filter_input(INPUT_GET, 'external_reference', FILTER_SANITIZE_URL);\n            $client->cerrarPedido($iue, $external_reference, false);\n        }\n        break;\n    case 'cerrarConDatosExtra':\n        if ($metodo != 'POST') {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n        } else {\n            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);\n            $external_reference = filter_input(INPUT_POST, 'external_reference', FILTER_SANITIZE_URL);\n            $extras = json_decode(filter_input(INPUT_POST, 'extras', FILTER_SANITIZE_URL));\n            $client->cerrarPedidoConDatosExtra($iue, $external_reference, $extras);\n        }\n        break;\n\n    case 'usuarios':\n        if ($metodo == 'GET') {\n            if (!$recursos[1]) {\n                $client->rechazarRecurso(400, 'Recurso invalido');\n            } elseif ($recursos[1] != $_SESSION['usuario']) {\n                $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');\n            } else {\n                file_put_contents(PATH_LOGS.'api-v1/' . date(\"Y-m-d\") . '.log', \"\\r\\n GET 1 \" . $recursos[1], FILE_APPEND);\n                $datos = array(\n                    'iue' => $iue,\n                    'idcliente' => $recursos[1],\n                );\n                $client->verUsuario($datos);\n            }\n\n            // Para crear a un usuario debería venir por PUT, pero no me funcionó, así que lo hago por POST por ahora\n        } elseif ($metodo == 'PUT') {\n            $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');\n        } elseif ($metodo == 'POST') {\n            if (!$recursos[1] && !$_SESSION['usuario']) {\n                $datos = array(\n                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),\n                    'idcliente' => $recursos[1],\n                    'estado' => 0,\n                    'nombre' => filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_STRING),\n                    'mail' => filter_input(INPUT_POST, 'mail', FILTER_VALIDATE_EMAIL),\n                    'pass' => filter_input(INPUT_POST, 'pass', FILTER_SANITIZE_STRING),\n                    'telefonos' => filter_input(INPUT_POST, 'telefonos', FILTER_SANITIZE_STRING),\n                    'domicilio' => filter_input(INPUT_POST, 'domicilio', FILTER_SANITIZE_STRING),\n                    'idlocalidad' => filter_input(INPUT_POST, 'idlocalidad', FILTER_SANITIZE_NUMBER_INT),\n                    'dni' => filter_input(INPUT_POST, 'dni', FILTER_VALIDATE_FLOAT),\n                    'idtipoiva' => filter_input(INPUT_POST, 'idtipoiva', FILTER_SANITIZE_NUMBER_INT),\n                    'razonSocial' => filter_input(INPUT_POST, 'razonSocial', FILTER_SANITIZE_STRING),\n                    'cuit' => filter_input(INPUT_POST, 'cuit', FILTER_VALIDATE_FLOAT),\n                );\n                if (!$datos['iue'])\n                    $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n                $client->crearUsuario($datos);\n            } elseif ($recursos[1] && $recursos[1] == $_SESSION['usuario']) {\n                $datos = array(\n                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),\n                    'idcliente' => $recursos[1],\n                    'nombre' => isset($_POST['nombre'])\n                        ? filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_STRING)\n                        : false,\n                    'mail' => isset($_POST['mail'])\n                        ? filter_input(INPUT_POST, 'mail', FILTER_VALIDATE_EMAIL)\n                        : false,\n                    'pass' => isset($_POST['pass'])\n                        ? filter_input(INPUT_POST, 'pass', FILTER_SANITIZE_STRING)\n                        : false,\n                    'telefonos' => isset($_POST['telefonos'])\n                        ? filter_input(INPUT_POST, 'telefonos', FILTER_SANITIZE_STRING)\n                        : false,\n                    'domicilio' => isset($_POST['domicilio'])\n                        ? filter_input(INPUT_POST, 'domicilio', FILTER_SANITIZE_STRING)\n                        : false,\n                    'idlocalidad' => isset($_POST['idlocalidad'])\n                        ? filter_input(INPUT_POST, 'idlocalidad', FILTER_SANITIZE_NUMBER_INT)\n                        : false,\n                    'dni' => isset($_POST['dni'])\n                        ? filter_input(INPUT_POST, 'dni', FILTER_VALIDATE_FLOAT)\n                        : false,\n                    'idtipoiva' => isset($_POST['idtipoiva'])\n                        ? filter_input(INPUT_POST, 'idtipoiva', FILTER_SANITIZE_NUMBER_INT)\n                        : false,\n                    'razonSocial' => isset($_POST['razonSocial'])\n                        ? filter_input(INPUT_POST, 'razonSocial', FILTER_SANITIZE_STRING)\n                        : false,\n                    'cuit' => isset($_POST['cuit'])\n                        ? filter_input(INPUT_POST, 'cuit', FILTER_VALIDATE_FLOAT)\n                        : false,\n                );\n                if (!$datos['iue'])\n                    $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n                $client->modificarUsuario($datos);\n            } else {\n                $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');\n            }\n        } else {\n            $client->rechazarRecurso(400, 'Recurso invalido');\n        }\n        break;\n\n    case \"pass\":\n        if ($metodo != 'POST') {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n        } else {\n            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);\n            $id_usuario = filter_input(INPUT_POST, 'id_usuario', FILTER_SANITIZE_NUMBER_INT);\n            $pass_actual = filter_input(INPUT_POST, 'pass_actual', FILTER_SANITIZE_URL);\n            $pass_nuevo = filter_input(INPUT_POST, 'pass_nuevo', FILTER_SANITIZE_URL);\n            $client->modificarPassUsuario($iue, $id_usuario, $pass_actual, $pass_nuevo);\n        }\n        break;\n\n    case \"login\":\n        if ($metodo == 'GET') {\n            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n\n            $email = filter_input(INPUT_GET, 'email', FILTER_VALIDATE_EMAIL);\n            $pass = filter_input(INPUT_GET, 'pass', FILTER_SANITIZE_URL);\n            $client->validarUsuario($iue, $email, $pass);\n        } else {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n        }\n        break;\n\n    case \"loginsinpw\":\n        if ($metodo == 'GET') {\n            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n            $email = filter_input(INPUT_GET, 'email', FILTER_VALIDATE_EMAIL);\n            $client->validarUsuarioSinPw($iue, $email);\n        } else {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n        }\n        break;\n\n    case \"validar\":\n        if ($metodo == 'GET') {\n            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n            $random = filter_input(INPUT_GET, 'random', FILTER_SANITIZE_URL);\n            $mail = filter_input(INPUT_GET, 'mail', FILTER_VALIDATE_EMAIL);\n\n            switch ($recursos[1]) {\n                default:\n                    $client->rechazarRecurso(400, 'Recurso inválido');\n                    break;\n\n                case 'mail':\n                    if ($iue && $random) {\n                        $client->confirmarMail($iue, $random);\n                    } else {\n                        $client->rechazarRecurso(400, 'Recurso inválido');\n                    }\n                    break;\n\n                case 'pass':\n                    if ($iue && $random) {\n                        $client->confirmarPass($iue, $random);\n                    } else {\n                        $client->rechazarRecurso(400, 'Recurso inválido');\n                    }\n                    break;\n\n                case 'escliente':\n                    if ($iue && $mail) {\n                        $client->esCliente($iue, $mail, $pass);\n                    } else {\n                        $client->rechazarRecurso(400, 'Recurso inválido');\n                    }\n                    break;\n\n                case 'esclienteml':\n                    $datos = array(\n                        'iue' => $iue,\n                        'apodo' => filter_input(INPUT_GET, 'apodo', FILTER_SANITIZE_STRING),\n                        'order_id' => filter_input(INPUT_GET, 'order_id', FILTER_SANITIZE_URL)\n                    );\n                    $client->esClienteMl($datos);\n                    break;\n\n                case 'resetearpass':\n                    if ($iue && $mail) {\n                        $client->resetearPass($iue, $mail);\n                    } else {\n                        $client->rechazarRecurso(400, 'Recurso inválido');\n                    }\n                    break;\n            }\n        } else {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n        }\n        break;\n\n    case \"forzarmail\":\n        if ($metodo == 'POST') {\n            $datos = array(\n                'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),\n                'id_cliente' => filter_input(INPUT_POST, 'id_cliente', FILTER_SANITIZE_URL),\n                'mail' => filter_input(INPUT_POST, 'mail', FILTER_SANITIZE_URL)\n            );\n            $client->forzarMail($datos);\n        } else {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n        }\n        break;\n\n    case \"tablas\":\n        if ($metodo == 'GET') {\n            switch ($recursos[1]) {\n                default:\n                    $client->rechazarRecurso(400, 'Tipo de tabla inválido');\n                    break;\n\n                case 'iva':\n                    $datos = array(\n                        'iue' => $iue,\n                    );\n                    $client->listarTiposIva($datos);\n                    break;\n            }\n        } else {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n        }\n        break;\n\n    case \"passForzada\":\n        if ($metodo != 'POST') {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');\n        } else {\n            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);\n            $id_usuario = filter_input(INPUT_POST, 'id_usuario', FILTER_SANITIZE_NUMBER_INT);\n            $pass_nuevo = filter_input(INPUT_POST, 'pass_nuevo', FILTER_SANITIZE_URL);\n            $client->modificarPassUsuarioForzada($iue, $id_usuario, $pass_nuevo);\n        }\n        break;\n\n    case 'cajas':\n        if ($metodo == 'GET') {\n            if ($recursos[1]) {\n                $client->rechazarRecurso(400, 'Recurso invalido');\n            } else {\n                $datos = array(\n                    'iue' => $iue,\n                    'idcaja' => filter_input(INPUT_GET, 'idcaja', FILTER_SANITIZE_NUMBER_INT),\n                    'idtipocaja' => filter_input(INPUT_GET, 'idtipocaja', FILTER_SANITIZE_NUMBER_INT),\n                    'idconcepto' => filter_input(INPUT_GET, 'idconcepto', FILTER_SANITIZE_NUMBER_INT),\n                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_STRING),\n                    'hasta' => filter_input(INPUT_GET, 'hasta', FILTER_SANITIZE_STRING)\n                );\n                $client->listarMovimientosCajas($datos);\n            }\n        } else {\n            $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');\n        }\n        break;\n\n    case 'iva':\n        if ($metodo == 'GET') {\n            switch ($recursos[1]) {\n                default:\n                    $client->rechazarRecurso(400, 'Tipo de tabla inválido');\n                    break;\n\n                case 'ventas':\n                    $datos = array(\n                        'iue' => $iue,\n                        'periodo' => filter_input(INPUT_GET, 'periodo', FILTER_SANITIZE_URL)\n                    );\n                    $client->listarLibroIvaVentas($datos);\n                    break;\n\n                case 'compras':\n                    $datos = array(\n                        'iue' => $iue,\n                        'periodo' => filter_input(INPUT_GET, 'periodo', FILTER_SANITIZE_URL)\n                    );\n                    $client->listarLibroIvaCompras($datos);\n                    break;\n            }\n        } else {\n            $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');\n        }\n        break;\n\n    case 'botonmercadopago':\n        require PATH_TOOLS . 'mercadopago/mercadopago.php';\n        if ($metodo != 'GET') {\n            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');\n        } else {\n            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n            $datos = array(\n                'titulo' => filter_input(INPUT_GET, 'titulo', FILTER_SANITIZE_URL),\n                'cantidad' => filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL),\n                'precio' => filter_input(INPUT_GET, 'precio', FILTER_SANITIZE_URL),\n                'external_reference' => filter_input(INPUT_GET, 'external_reference', FILTER_SANITIZE_URL),\n                'back_urls_success' => filter_input(INPUT_GET, 'back_urls_success', FILTER_SANITIZE_URL),\n                'back_urls_failure' => filter_input(INPUT_GET, 'back_urls_failure', FILTER_SANITIZE_URL),\n                'back_urls_pending' => filter_input(INPUT_GET, 'back_urls_pending', FILTER_SANITIZE_URL),\n                'notification_url' => filter_input(INPUT_GET, 'notification_url', FILTER_SANITIZE_URL),\n            );\n            $client->crearBotonMP($datos, $iue);\n        }\n        break;\n\n        case 'clientes':\n            if ($metodo == 'GET') {\n                if (!$recursos[1]) {\n                    $datos = array(\n                        'iue' => $iue,\n                        'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_STRING),\n                        'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n                        'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 10, \"min_range\" => 1, \"max_range\" => 100)))),\n                        'modificados' => filter_input(INPUT_GET, 'modificados', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 0, \"min_range\" => 1, \"max_range\" => 1000))),\n                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                        'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n                        'idtipocliente' => filter_input(INPUT_GET, 'idtipocliente', FILTER_SANITIZE_NUMBER_INT),\n                        'idlista' => filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT),\n                        'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_NUMBER_INT),\n                        'mostrarmoneda' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_NUMBER_INT),\n                    );\n                    // Por si no funcionan las opciones del filter_input\n                    if (!$datos['cantidad']) {\n                        $datos['cantidad'] = '10';\n                    } else if (intval($datos['cantidad']) > 100) {\n                        $datos['cantidad'] = '100';\n                    }\n                    if (intval($datos['desde']) < 1) {\n                        $datos['desde'] = 0;\n                    }\n\n                    $client->listarClientes($datos);\n                } else {\n                    $datos = array(\n                        'idcliente' => $recursos[1],\n                        'iue' => $iue,\n                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                    );\n                    $client->verCliente($datos);\n                }\n            } elseif ($metodo == 'POST') {\n                if (!$recursos[1]) {\n                    $datos = file_get_contents('php://input');\n                    $client->agregarCliente($datos);\n                } else {\n                    $datos = file_get_contents('php://input');\n                    $idcliente = $recursos[1];\n                    $client->modificarCliente($datos, $idcliente);\n                }\n            } elseif ($metodo == 'PUT') {\n                // Acá va el servicio para modificar productos\n                $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n            } elseif ($metodo == 'DELETE') {\n                if ($recursos[1]) {\n                    $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n                    $idcliente = $recursos[1];\n                    $client->eliminarCliente($iue, $idcliente);\n                }\n            }\n            break;\n\n        case 'servicios':\n            if ($metodo == 'GET') {\n                if (!$recursos[1]) {\n                    $datos = array(\n                        'iue' => $iue,\n                        'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_STRING),\n                        'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array(\"options\" => array(\"default\" => 0, \"min_range\" => 0, \"max_range\" => 1000000))),\n                        'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 10, \"min_range\" => 1, \"max_range\" => 100)))),\n                        'modificados' => filter_input(INPUT_GET, 'modificados', FILTER_VALIDATE_INT, array(\"options\" => array(\"default\" => 0, \"min_range\" => 1, \"max_range\" => 1000))),\n                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                        'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),\n                        'idtiposervicio' => filter_input(INPUT_GET, 'idtiposervicio', FILTER_SANITIZE_NUMBER_INT),\n                        'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_NUMBER_INT),\n                        'estado' => filter_input(INPUT_GET, 'estado', FILTER_SANITIZE_NUMBER_INT),\n                    );\n                    // Por si no funcionan las opciones del filter_input\n                    if (!$datos['cantidad']) {\n                        $datos['cantidad'] = '10';\n                    } else if (intval($datos['cantidad']) > 100) {\n                        $datos['cantidad'] = '100';\n                    }\n                    if (intval($datos['desde']) < 1) {\n                        $datos['desde'] = 0;\n                    }\n\n                    $client->listarServicios($datos);\n                } else {\n                    $datos = array(\n                        'idservicio' => $recursos[1],\n                        'iue' => $iue,\n                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),\n                    );\n                    $client->verServicio($datos);\n                }\n            } elseif ($metodo == 'POST') {\n                if (!$recursos[1]) {\n                    $datos = file_get_contents('php://input');\n                    $client->agregarServicio($datos);\n                } else {\n                    $datos = file_get_contents('php://input');\n                    $idservicio = $recursos[1];\n                    $client->modificarServicio($datos, $idservicio);\n                }\n            } elseif ($metodo == 'PUT') {\n                // Acá va el servicio para modificar productos\n                $client->rechazarRecurso(400, 'Este recurso no está habilitado');\n            } elseif ($metodo == 'DELETE') {\n                if ($recursos[1]) {\n                    $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);\n                    $idservicio = $recursos[1];\n                    $client->eliminarServicio($iue, $idservicio);\n                }\n            }\n            break;\n}\n\nunlink($consultando_api);"}]}