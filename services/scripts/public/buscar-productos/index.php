<?php
// Configuración de parámetros - mantener compatibilidad total
$iues = isset($_GET['iue']) ? explode(',', $_GET['iue']) : [];
$empresas = isset($_GET['empresa']) ? explode(',', $_GET['empresa']) : [];
$cantidad = isset($_GET['cantidad']) && is_numeric($_GET['cantidad']) ? (int)$_GET['cantidad'] : 10;

// Procesar campos disponibles
$camposDisponibles = ['empresa', 'codigo', 'nombre', 'precio', 'preciofinal', 'stock'];
$camposPredeterminados = ['codigo', 'nombre', 'precio', 'preciofinal', 'stock'];
$camposParam = isset($_GET['campos']) ? $_GET['campos'] : '';

$campos = [];
if ($camposParam) {
    $camposSolicitados = explode(',', $camposParam);
    foreach ($camposSolicitados as $campo) {
        $campo = trim($campo);
        if (in_array($campo, $camposDisponibles)) {
            $campos[] = $campo;
        }
    }
}

if (empty($campos)) {
    $campos = $camposPredeterminados;
}

// Función para buscar productos en la API
function buscarProductosAPI($busqueda, $iues, $empresas, $cantidad) {
    $resultados = [];

    foreach ($iues as $index => $iue) {
        $empresa = isset($empresas[$index]) ? $empresas[$index] : 'Empresa ' . ($index + 1);

        $url = "https://api.saasargentina.com/v1/productos/" .
               "?iue=" . urlencode($iue) .
               "&busqueda=" . urlencode($busqueda) .
               "&cantidad=" . urlencode($cantidad);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'SaaS Argentina Buscador Productos Script');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            $resultados[] = [
                'empresa' => $empresa,
                'error' => true,
                'message' => 'Error de conexión: ' . $error,
                'productos' => []
            ];
            continue;
        }

        if ($httpCode !== 200) {
            $resultados[] = [
                'empresa' => $empresa,
                'error' => true,
                'message' => 'Error HTTP: ' . $httpCode,
                'productos' => []
            ];
            continue;
        }

        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $resultados[] = [
                'empresa' => $empresa,
                'error' => true,
                'message' => 'Error al procesar respuesta',
                'productos' => []
            ];
            continue;
        }

        $resultados[] = [
            'empresa' => $empresa,
            'error' => false,
            'message' => '',
            'productos' => isset($data['resultados']) ? $data['resultados'] : []
        ];
    }

    return $resultados;
}

// Manejar solicitudes AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'buscar') {
    header('Content-Type: application/json');

    $busqueda = trim($_POST['busqueda'] ?? '');

    if (empty($busqueda)) {
        echo json_encode(['error' => true, 'message' => 'La búsqueda es requerida']);
        exit;
    }

    if (empty($iues)) {
        echo json_encode(['error' => true, 'message' => 'No se han configurado empresas para buscar']);
        exit;
    }

    $resultados = buscarProductosAPI($busqueda, $iues, $empresas, $cantidad);
    echo json_encode(['error' => false, 'resultados' => $resultados, 'campos' => $campos]);
    exit;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Buscador de productos</title>
    <link href="bootstrap.min.css" rel="stylesheet">
    <style>
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 0.125rem solid #f3f3f3;
            border-top: 0.125rem solid #0d6efd;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .table-container {
            margin-top: 1.5rem;
        }
        .empresa-section {
            margin-bottom: 1rem;
        }
        .empresa-title {
            background-color: #f8f9fa;
            padding: 0.5rem 1rem;
            border-left: 4px solid #0d6efd;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        .no-results {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 1rem;
        }
    </style>
</head>
<body>
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <h1 class="mb-4 text-center">Buscador de productos</h1>

            <div class="card">
                <div class="card-body">
                    <form id="busquedaForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="buscado" class="form-label">Buscar productos:</label>
                                    <input type="text" name="buscado" id="buscado" class="form-control" placeholder="Ingrese el nombre o código del producto" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" id="buscar" class="btn btn-primary">
                                            <span id="btnText">Buscar</span>
                                            <span id="btnSpinner" class="loading-spinner ms-2" style="display: none;"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="terminalPrecios" checked>
                            <label class="form-check-label" for="terminalPrecios" title="Active esta opción si utiliza esta funcionalidad para una terminal de precios pública que no dispone de teclado o mouse">
                                Terminal de precios
                            </label>
                        </div>
                    </form>
                </div>
            </div>

            <div id="resultado" class="table-container"></div>
        </div>
    </div>
</div>

<script src="jquery-3.7.1.min.js"></script>
<script src="bootstrap.bundle.min.js"></script>
<script>
    // Configuración desde PHP - mantener compatibilidad
    var configData = {
        iues: <?php echo json_encode($iues); ?>,
        empresas: <?php echo json_encode($empresas); ?>,
        cantidad: <?php echo json_encode($cantidad); ?>,
        campos: <?php echo json_encode($campos); ?>
    };

    $(function() {
        $("#buscado").focus();

        // Manejar el envío del formulario
        $("#busquedaForm").on('submit', function(e) {
            e.preventDefault();
            buscar();
        });

        // Permitir buscar con Enter
        $("#buscado").on('keypress', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                buscar();
            }
        });
    });

    function buscar() {
        var buscado = $("#buscado").val().trim();
        var resultado = $("#resultado");
        var btnText = $("#btnText");
        var btnSpinner = $("#btnSpinner");
        var buscarBtn = $("#buscar");

        // Validación
        if (!buscado) {
            mostrarAlerta("Por favor ingrese un término de búsqueda", "warning");
            $("#buscado").focus();
            return;
        }

        // Mostrar indicador de carga
        btnText.text("Buscando...");
        btnSpinner.show();
        buscarBtn.prop('disabled', true);
        resultado.html('<div class="text-center mt-3"><div class="loading-spinner"></div><br>Buscando productos...</div>');

        // Realizar búsqueda via AJAX al PHP local
        $.ajax({
            url: '',
            method: 'POST',
            data: {
                action: 'buscar',
                busqueda: buscado
            },
            dataType: 'json'
        })
        .done(function(response) {
            if (response.error) {
                mostrarError(response.message);
            } else {
                mostrarResultados(response.resultados, response.campos);
            }
        })
        .fail(function(jqxhr, textStatus, error) {
            var errorMsg = "Error al realizar la búsqueda";
            if (jqxhr.responseJSON && jqxhr.responseJSON.message) {
                errorMsg = jqxhr.responseJSON.message;
            }
            mostrarError(errorMsg);
        })
        .always(function() {
            // Restaurar botón
            btnText.text("Buscar");
            btnSpinner.hide();
            buscarBtn.prop('disabled', false);

            // Limpiar campo si es terminal de precios
            if ($("#terminalPrecios").is(":checked")) {
                $("#buscado").val("").focus();
            }
        });
    }

    function mostrarResultados(resultados, campos) {
        var html = '';
        var totalProductos = 0;

        // Títulos de columnas
        var titulos = {
            'empresa': 'Empresa',
            'codigo': 'Código',
            'nombre': 'Nombre',
            'precio': 'Precio',
            'preciofinal': 'Precio Final',
            'stock': 'Stock'
        };

        resultados.forEach(function(empresaData) {
            if (empresaData.error) {
                html += '<div class="empresa-section">';
                html += '<div class="empresa-title text-danger">' + escapeHtml(empresaData.empresa) + ' - Error</div>';
                html += '<div class="alert alert-danger">' + escapeHtml(empresaData.message) + '</div>';
                html += '</div>';
                return;
            }

            if (empresaData.productos.length > 0) {
                totalProductos += empresaData.productos.length;

                html += '<div class="empresa-section">';
                html += '<div class="empresa-title">' + escapeHtml(empresaData.empresa) + ' (' + empresaData.productos.length + ' productos)</div>';
                html += '<div class="table-responsive">';
                html += '<table class="table table-striped table-hover">';

                // Encabezados
                html += '<thead class="table-dark"><tr>';
                campos.forEach(function(campo) {
                    html += '<th>' + titulos[campo] + '</th>';
                });
                html += '</tr></thead>';

                // Productos
                html += '<tbody>';
                empresaData.productos.forEach(function(producto) {
                    html += '<tr>';
                    campos.forEach(function(campo) {
                        switch(campo) {
                            case 'empresa':
                                html += '<td>' + escapeHtml(empresaData.empresa) + '</td>';
                                break;
                            case 'codigo':
                                html += '<td><code>' + escapeHtml(producto.codigo || '') + '</code></td>';
                                break;
                            case 'nombre':
                                html += '<td>' + escapeHtml(producto.nombre || '') + '</td>';
                                break;
                            case 'precio':
                                html += '<td class="text-end"><strong>$' + formatearPrecio(producto.precio) + '</strong></td>';
                                break;
                            case 'preciofinal':
                                html += '<td class="text-end"><strong>$' + formatearPrecio(producto.preciofinal) + '</strong></td>';
                                break;
                            case 'stock':
                                var stock = producto.stockactual || 0;
                                var stockClass = stock > 0 ? 'text-success' : 'text-danger';
                                html += '<td class="text-center"><span class="' + stockClass + '">' + stock + '</span></td>';
                                break;
                        }
                    });
                    html += '</tr>';
                });
                html += '</tbody>';
                html += '</table>';
                html += '</div>';
                html += '</div>';
            } else {
                html += '<div class="empresa-section">';
                html += '<div class="empresa-title">' + escapeHtml(empresaData.empresa) + '</div>';
                html += '<div class="no-results">No se encontraron productos con esta búsqueda</div>';
                html += '</div>';
            }
        });

        if (totalProductos === 0) {
            html = '<div class="alert alert-info mt-4" role="alert">';
            html += '<i class="bi bi-info-circle-fill me-2"></i>';
            html += '<strong>Sin resultados:</strong> No se encontraron productos con el término de búsqueda especificado.';
            html += '</div>';
        } else {
            html = '<div class="alert alert-success mt-4" role="alert">' +
                   '<strong>Búsqueda completada:</strong> Se encontraron ' + totalProductos + ' productos en total.' +
                   '</div>' + html;
        }

        $("#resultado").html(html);
    }

    function mostrarError(mensaje) {
        var html = '<div class="alert alert-danger mt-4" role="alert">';
        html += '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
        html += '<strong>Error:</strong> ' + escapeHtml(mensaje);
        html += '</div>';
        $("#resultado").html(html);
    }

    function mostrarAlerta(mensaje, tipo) {
        var alertClass = 'alert-info';
        var icon = 'bi-info-circle-fill';

        switch(tipo) {
            case 'warning':
                alertClass = 'alert-warning';
                icon = 'bi-exclamation-triangle-fill';
                break;
            case 'success':
                alertClass = 'alert-success';
                icon = 'bi-check-circle-fill';
                break;
            case 'danger':
                alertClass = 'alert-danger';
                icon = 'bi-x-circle-fill';
                break;
        }

        var html = '<div class="alert ' + alertClass + ' mt-4" role="alert">';
        html += '<i class="' + icon + ' me-2"></i>' + escapeHtml(mensaje);
        html += '</div>';
        $("#resultado").html(html);
    }

    function formatearPrecio(precio) {
        if (!precio || precio === null || precio === undefined) return '0.00';
        var num = parseFloat(precio);
        if (isNaN(num)) return '0.00';
        return num.toFixed(2);
    }

    function escapeHtml(text) {
        if (!text) return '';
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.toString().replace(/[&<>"']/g, function(m) { return map[m]; });
    }
</script>

</body>
</html>
