<?php
// Configuración del IUE - puede venir por GET o estar hardcodeado
$iue = isset($_GET['iue']) ? $_GET['iue'] : 'HU7jaq4YHEe39T3zPUrn';

// Función para realizar la consulta a la API
function consultarServicioAPI($numeroServicio, $password, $iue) {
    $url = "https://api-beta.saasargentina.com/v1/servicios/" . urlencode($numeroServicio) . "?iue=" . urlencode($iue) . "&pass=" . urlencode($password);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'SaaS Argentina Consultar Servicios Script');

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        return ['error' => true, 'message' => 'Error de conexión: ' . $error];
    }

    if ($httpCode !== 200) {
        $errorMessages = [
            404 => 'Servicio no encontrado',
            401 => 'Acceso denegado. Verifique la contraseña',
            403 => 'Acceso denegado. Verifique la contraseña',
            500 => 'Error interno del servidor'
        ];

        $message = isset($errorMessages[$httpCode]) ? $errorMessages[$httpCode] : 'Error HTTP: ' . $httpCode;
        return ['error' => true, 'message' => $message];
    }

    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['error' => true, 'message' => 'Error al procesar la respuesta del servidor'];
    }

    return ['error' => false, 'data' => $data];
}

// Manejar solicitudes AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'consultar') {
    header('Content-Type: application/json');

    $numeroServicio = trim($_POST['numeroServicio'] ?? '');
    $password = trim($_POST['password'] ?? '');

    if (empty($numeroServicio)) {
        echo json_encode(['error' => true, 'message' => 'El número de servicio es requerido']);
        exit;
    }

    if (empty($password)) {
        echo json_encode(['error' => true, 'message' => 'La contraseña es requerida']);
        exit;
    }

    $resultado = consultarServicioAPI($numeroServicio, $password, $iue);
    echo json_encode($resultado);
    exit;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consultar Servicios</title>
    <link href="bootstrap.min.css" rel="stylesheet">
    <style>
        .servicio-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
            background-color: #f8f9fa;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .servicio-field {
            margin-bottom: 0.75rem;
        }
        .servicio-field strong {
            display: inline-block;
            width: 150px;
            color: #495057;
        }
        .estado-badge {
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 600;
        }
        .estado-sin-comenzar {
            background-color: #fff3cd;
            color: #664d03;
            border: 1px solid #ffecb5;
        }
        .estado-en-proceso {
            background-color: #cff4fc;
            color: #055160;
            border: 1px solid #b6effb;
        }
        .estado-finalizado {
            background-color: #d1e7dd;
            color: #0f5132;
            border: 1px solid #badbcc;
        }
        .prioridad-alta {
            color: #dc3545;
            font-weight: 600;
        }
        .prioridad-media {
            color: #fd7e14;
            font-weight: 600;
        }
        .prioridad-baja {
            color: #198754;
            font-weight: 600;
        }
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 0.125rem solid #f3f3f3;
            border-top: 0.125rem solid #0d6efd;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <h1 class="mb-4 text-center">Consultar Servicios</h1>

            <div class="card">
                <div class="card-body">
                    <form id="consultaForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="numeroServicio" class="form-label">Número de Servicio:</label>
                                    <input type="text" name="numeroServicio" id="numeroServicio" class="form-control" placeholder="Ingrese el número de servicio" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Contraseña:</label>
                                    <input type="password" name="password" id="password" class="form-control" placeholder="Ingrese la contraseña" required>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" id="consultar" class="btn btn-primary">
                                <span id="btnText">Consultar Servicio</span>
                                <span id="btnSpinner" class="loading-spinner ms-2" style="display: none;"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div id="resultado"></div>
        </div>
    </div>
</div>

<script src="jquery-3.7.1.min.js"></script>
<script src="bootstrap.bundle.min.js"></script>
<script>
    $(function() {
        $("#numeroServicio").focus();

        // Manejar el envío del formulario
        $("#consultaForm").on('submit', function(e) {
            e.preventDefault();
            consultarServicio();
        });

        // Permitir consultar con Enter en ambos campos
        $("#numeroServicio, #password").on('keypress', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                consultarServicio();
            }
        });
    });

    function consultarServicio() {
        var numeroServicio = $("#numeroServicio").val().trim();
        var password = $("#password").val().trim();
        var resultado = $("#resultado");
        var btnText = $("#btnText");
        var btnSpinner = $("#btnSpinner");
        var consultarBtn = $("#consultar");

        // Validaciones
        if (!numeroServicio) {
            mostrarAlerta("Por favor ingrese el número de servicio", "warning");
            $("#numeroServicio").focus();
            return;
        }

        if (!password) {
            mostrarAlerta("Por favor ingrese la contraseña", "warning");
            $("#password").focus();
            return;
        }

        // Mostrar indicador de carga
        btnText.text("Consultando...");
        btnSpinner.show();
        consultarBtn.prop('disabled', true);
        resultado.html('<div class="text-center mt-3"><div class="loading-spinner"></div><br>Consultando servicio...</div>');

        // Realizar consulta via AJAX al PHP local
        $.ajax({
            url: '',
            method: 'POST',
            data: {
                action: 'consultar',
                numeroServicio: numeroServicio,
                password: password
            },
            dataType: 'json'
        })
        .done(function(response) {
            if (response.error) {
                mostrarError(response.message);
            } else if (response.data && response.data.estado && response.data.resultados && response.data.resultados.length > 0) {
                mostrarServicio(response.data.resultados[0]);
            } else {
                mostrarError("No se encontró el servicio o los datos de acceso son incorrectos");
            }
        })
        .fail(function(jqxhr, textStatus, error) {
            var errorMsg = "Error al consultar el servicio";
            if (jqxhr.responseJSON && jqxhr.responseJSON.message) {
                errorMsg = jqxhr.responseJSON.message;
            }
            mostrarError(errorMsg);
        })
        .always(function() {
            // Restaurar botón
            btnText.text("Consultar Servicio");
            btnSpinner.hide();
            consultarBtn.prop('disabled', false);
        });
    }

    function mostrarServicio(servicio) {
        var html = '<div class="servicio-card mt-4">';
        html += '<h3 class="mb-3 text-primary">Servicio #' + escapeHtml(servicio.idservicio) + '</h3>';

        if (servicio.titulo) {
            html += '<div class="servicio-field"><strong>Título:</strong> ' + escapeHtml(servicio.titulo) + '</div>';
        }

        html += '<div class="servicio-field"><strong>Estado:</strong> <span class="estado-badge ' + getEstadoClass(servicio.estado) + '">' + escapeHtml(servicio.estado) + '</span></div>';

        if (servicio.prioridad) {
            html += '<div class="servicio-field"><strong>Prioridad:</strong> <span class="' + getPrioridadClass(servicio.prioridad) + '">' + getPrioridadTexto(servicio.prioridad) + '</span></div>';
        }

        html += '<div class="servicio-field"><strong>Tipo:</strong> ' + escapeHtml(servicio.tiposervicio || 'No especificado') + '</div>';
        html += '<div class="servicio-field"><strong>Cliente:</strong> ' + escapeHtml(servicio.cliente || 'No especificado') + '</div>';

        if (servicio.fechainicio) {
            html += '<div class="servicio-field"><strong>Fecha Inicio:</strong> ' + formatearFecha(servicio.fechainicio) + '</div>';
        }

        if (servicio.fechafin) {
            html += '<div class="servicio-field"><strong>Fecha Fin:</strong> ' + formatearFecha(servicio.fechafin) + '</div>';
        }

        if (servicio.fechalimite) {
            html += '<div class="servicio-field"><strong>Fecha Límite:</strong> ' + formatearFecha(servicio.fechalimite) + '</div>';
        }

        html += '<div class="servicio-field"><strong>Fecha Solicitado:</strong> ' + formatearFecha(servicio.fechasolicitado) + '</div>';

        if (servicio.tiempodedicado && servicio.tiempodedicado !== '00:00:00') {
            html += '<div class="servicio-field"><strong>Tiempo Dedicado:</strong> ' + escapeHtml(servicio.tiempodedicado) + '</div>';
        }

        if (servicio.obssolicitado) {
            html += '<div class="servicio-field"><strong>Observaciones Solicitadas:</strong><br><div class="mt-2 p-2 bg-light rounded">' + escapeHtml(servicio.obssolicitado).replace(/\n/g, '<br>') + '</div></div>';
        }

        if (servicio.obsrealizado) {
            html += '<div class="servicio-field"><strong>Observaciones Realizadas:</strong><br><div class="mt-2 p-2 bg-light rounded">' + escapeHtml(servicio.obsrealizado).replace(/\n/g, '<br>') + '</div></div>';
        }

        if (servicio.saldo !== null && servicio.saldo !== undefined) {
            html += '<div class="servicio-field"><strong>Saldo:</strong> <span class="fw-bold">$' + parseFloat(servicio.saldo).toFixed(2) + '</span></div>';
        }

        html += '</div>';

        $("#resultado").html(html);
    }

    function mostrarError(mensaje) {
        var html = '<div class="alert alert-danger mt-4" role="alert">';
        html += '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
        html += '<strong>Error:</strong> ' + escapeHtml(mensaje);
        html += '</div>';
        $("#resultado").html(html);
    }

    function mostrarAlerta(mensaje, tipo) {
        var alertClass = 'alert-info';
        var icon = 'bi-info-circle-fill';

        switch(tipo) {
            case 'warning':
                alertClass = 'alert-warning';
                icon = 'bi-exclamation-triangle-fill';
                break;
            case 'success':
                alertClass = 'alert-success';
                icon = 'bi-check-circle-fill';
                break;
            case 'danger':
                alertClass = 'alert-danger';
                icon = 'bi-x-circle-fill';
                break;
        }

        var html = '<div class="alert ' + alertClass + ' mt-4" role="alert">';
        html += '<i class="' + icon + ' me-2"></i>' + escapeHtml(mensaje);
        html += '</div>';
        $("#resultado").html(html);
    }

    function getEstadoClass(estado) {
        if (!estado) return '';
        var estadoLower = estado.toLowerCase();
        if (estadoLower.includes('sin comenzar') || estadoLower.includes('pendiente')) {
            return 'estado-sin-comenzar';
        } else if (estadoLower.includes('proceso') || estadoLower.includes('progreso') || estadoLower.includes('en curso')) {
            return 'estado-en-proceso';
        } else if (estadoLower.includes('finalizado') || estadoLower.includes('completado') || estadoLower.includes('terminado') || estadoLower.includes('cerrado')) {
            return 'estado-finalizado';
        }
        return 'estado-sin-comenzar'; // Por defecto
    }

    function getPrioridadClass(prioridad) {
        if (prioridad == '1') return 'prioridad-alta';
        if (prioridad == '2') return 'prioridad-media';
        if (prioridad == '3') return 'prioridad-baja';
        return 'prioridad-media'; // Por defecto
    }

    function getPrioridadTexto(prioridad) {
        if (prioridad == '1') return 'Alta';
        if (prioridad == '2') return 'Media';
        if (prioridad == '3') return 'Baja';
        return 'Media';
    }

    function formatearFecha(fecha) {
        if (!fecha) return '';

        // Intentar parsear la fecha
        var date = new Date(fecha);
        if (isNaN(date.getTime())) {
            // Si no se puede parsear, intentar con formato específico
            var parts = fecha.split(' ');
            if (parts.length === 2) {
                var dateParts = parts[0].split('-');
                var timeParts = parts[1].split(':');
                if (dateParts.length === 3 && timeParts.length === 3) {
                    date = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], timeParts[0], timeParts[1], timeParts[2]);
                }
            }
        }

        if (isNaN(date.getTime())) return fecha;

        // Formatear fecha en español
        var options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        };

        return date.toLocaleDateString('es-AR', options);
    }

    function escapeHtml(text) {
        if (!text) return '';
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.toString().replace(/[&<>"']/g, function(m) { return map[m]; });
    }
</script>

</body>
</html>