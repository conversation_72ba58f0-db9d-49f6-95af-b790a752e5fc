<!DOCTYPE html>
<html>
<head>
    <title>Consultar Servicios</title>
    <link rel="stylesheet" href="bootstrap.min.css">
    <style>
        .servicio-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
        .servicio-field {
            margin-bottom: 10px;
        }
        .servicio-field strong {
            display: inline-block;
            width: 150px;
            color: #333;
        }
        .estado-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .estado-sin-comenzar {
            background-color: #ffc107;
            color: #212529;
        }
        .estado-en-proceso {
            background-color: #17a2b8;
            color: white;
        }
        .estado-finalizado {
            background-color: #28a745;
            color: white;
        }
        .prioridad-alta {
            color: #dc3545;
            font-weight: bold;
        }
        .prioridad-media {
            color: #ffc107;
            font-weight: bold;
        }
        .prioridad-baja {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>Consultar Servicios</h1>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="numeroServicio">Número de Servicio:</label>
                <input type="text" name="numeroServicio" id="numeroServicio" class="form-control" placeholder="Ingrese el número de servicio">
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="password">Contraseña:</label>
                <input type="password" name="password" id="password" class="form-control" placeholder="Ingrese la contraseña">
            </div>
        </div>
    </div>

    <div class="form-group">
        <input type="submit" name="consultar" id="consultar" value="Consultar Servicio" onclick="consultarServicio()" class="btn btn-primary">
    </div>

    <div id="resultado"></div>
</div>

<script src="jquery-2.2.4.min.js"></script>
<script>
    // Configuración del IUE - puede venir por GET o estar hardcodeado
    var iue = '<?php echo isset($_GET['iue']) ? $_GET['iue'] : 'HU7jaq4YHEe39T3zPUrn'; ?>';

    $(function() {
        $("#numeroServicio").focus();

        // Permitir consultar con Enter en ambos campos
        $("#numeroServicio, #password").keypress(function(e) {
            if (e.keyCode == 13) {
                consultarServicio();
            }
        });
    });

    function consultarServicio() {
        var numeroServicio = $("#numeroServicio").val().trim();
        var password = $("#password").val().trim();
        var resultado = $("#resultado");

        // Validaciones
        if (!numeroServicio) {
            alert("Por favor ingrese el número de servicio");
            $("#numeroServicio").focus();
            return;
        }

        if (!password) {
            alert("Por favor ingrese la contraseña");
            $("#password").focus();
            return;
        }

        // Mostrar indicador de carga
        var cargando = '<div class="text-center"><img src="https://app.saasargentina.com/estilos/estilo_1/images/cargando.gif"><br>Consultando servicio...</div>';
        resultado.html(cargando);

        // Construir URL de la API
        var url = 'https://api.saasargentina.com/v1/servicios/' + numeroServicio + '?iue=' + iue + '&pass=' + password;

        // Realizar consulta a la API
        $.getJSON(url)
            .done(function(data) {
                if (data.estado && data.resultados && data.resultados.length > 0) {
                    mostrarServicio(data.resultados[0]);
                } else {
                    mostrarError("No se encontró el servicio o los datos de acceso son incorrectos");
                }
            })
            .fail(function(jqxhr, textStatus, error) {
                var errorMsg = "Error al consultar el servicio";
                if (jqxhr.status === 404) {
                    errorMsg = "Servicio no encontrado";
                } else if (jqxhr.status === 401 || jqxhr.status === 403) {
                    errorMsg = "Acceso denegado. Verifique la contraseña";
                } else if (jqxhr.responseJSON && jqxhr.responseJSON.error) {
                    errorMsg = jqxhr.responseJSON.error;
                }
                mostrarError(errorMsg);
            });
    }

    function mostrarServicio(servicio) {
        var html = '<div class="servicio-card">';
        html += '<h3>Servicio #' + servicio.idservicio + '</h3>';

        if (servicio.titulo) {
            html += '<div class="servicio-field"><strong>Título:</strong> ' + servicio.titulo + '</div>';
        }

        html += '<div class="servicio-field"><strong>Estado:</strong> <span class="estado-badge ' + getEstadoClass(servicio.estado) + '">' + servicio.estado + '</span></div>';

        if (servicio.prioridad) {
            html += '<div class="servicio-field"><strong>Prioridad:</strong> <span class="' + getPrioridadClass(servicio.prioridad) + '">' + getPrioridadTexto(servicio.prioridad) + '</span></div>';
        }

        html += '<div class="servicio-field"><strong>Tipo:</strong> ' + (servicio.tiposervicio || 'No especificado') + '</div>';
        html += '<div class="servicio-field"><strong>Cliente:</strong> ' + (servicio.cliente || 'No especificado') + '</div>';

        if (servicio.fechainicio) {
            html += '<div class="servicio-field"><strong>Fecha Inicio:</strong> ' + formatearFecha(servicio.fechainicio) + '</div>';
        }

        if (servicio.fechafin) {
            html += '<div class="servicio-field"><strong>Fecha Fin:</strong> ' + formatearFecha(servicio.fechafin) + '</div>';
        }

        if (servicio.fechalimite) {
            html += '<div class="servicio-field"><strong>Fecha Límite:</strong> ' + formatearFecha(servicio.fechalimite) + '</div>';
        }

        html += '<div class="servicio-field"><strong>Fecha Solicitado:</strong> ' + formatearFecha(servicio.fechasolicitado) + '</div>';

        if (servicio.tiempodedicado && servicio.tiempodedicado !== '00:00:00') {
            html += '<div class="servicio-field"><strong>Tiempo Dedicado:</strong> ' + servicio.tiempodedicado + '</div>';
        }

        if (servicio.obssolicitado) {
            html += '<div class="servicio-field"><strong>Observaciones Solicitadas:</strong><br>' + servicio.obssolicitado.replace(/\n/g, '<br>') + '</div>';
        }

        if (servicio.obsrealizado) {
            html += '<div class="servicio-field"><strong>Observaciones Realizadas:</strong><br>' + servicio.obsrealizado.replace(/\n/g, '<br>') + '</div>';
        }

        if (servicio.saldo !== null && servicio.saldo !== undefined) {
            html += '<div class="servicio-field"><strong>Saldo:</strong> $' + parseFloat(servicio.saldo).toFixed(2) + '</div>';
        }

        html += '</div>';

        $("#resultado").html(html);
    }

    function mostrarError(mensaje) {
        var html = '<div class="alert alert-danger" role="alert">';
        html += '<strong>Error:</strong> ' + mensaje;
        html += '</div>';
        $("#resultado").html(html);
    }

    function getEstadoClass(estado) {
        if (!estado) return '';
        var estadoLower = estado.toLowerCase();
        if (estadoLower.includes('sin comenzar') || estadoLower.includes('pendiente')) {
            return 'estado-sin-comenzar';
        } else if (estadoLower.includes('proceso') || estadoLower.includes('progreso')) {
            return 'estado-en-proceso';
        } else if (estadoLower.includes('finalizado') || estadoLower.includes('completado') || estadoLower.includes('terminado')) {
            return 'estado-finalizado';
        }
        return '';
    }

    function getPrioridadClass(prioridad) {
        if (prioridad == '1') return 'prioridad-alta';
        if (prioridad == '2') return 'prioridad-media';
        if (prioridad == '3') return 'prioridad-baja';
        return '';
    }

    function getPrioridadTexto(prioridad) {
        if (prioridad == '1') return 'Alta';
        if (prioridad == '2') return 'Media';
        if (prioridad == '3') return 'Baja';
        return prioridad;
    }

    function formatearFecha(fecha) {
        if (!fecha) return '';
        var date = new Date(fecha);
        if (isNaN(date.getTime())) return fecha;
        return date.toLocaleDateString('es-AR') + ' ' + date.toLocaleTimeString('es-AR');
    }
</script>

</body>
</html>