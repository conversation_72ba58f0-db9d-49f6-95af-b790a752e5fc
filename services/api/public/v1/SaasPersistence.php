<?php

class SaasPersistence {

    private $camposObligatoriosProducto = Array("estado", "idrubro", "codigo", "nombre", "url_amigable", "stockactual", "stockideal", "stockminimo", "idunidad", "idiva", "idproveedor", "costo", "utilidad", "precio", "preciofinal", "controlarstock", "stocknegativo", "mostrartienda");
    private $camposObligatoriosClientes = Array("estado", "nombre", "razonsocial", "idtipoiva");
    private $beta = false;
    private $alfa = false;
    private $idempresa = 0;
    private $bd_link;
    private $read = false;

    public function mostrar_error($valor, $continuar = false)
    {
        $texto = 'ERROR: '.$valor.'<br /><br />
            idempresa: <a href="'.URL_SAAS.'/saas.php?a=verempresa&id='.$this->idempresa.'">'.$this->idempresa.'</a><br />
            beta: '.$this->beta.'<br />
            alfa: '.$this->alfa.'<br />
            read: '.$this->read.'<br />
            continuar: '.$continuar.'<br /><br />
            $_REQUEST: '.json_encode($_REQUEST).'<br />';

        email_queue(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ERROR en API', $texto, false);
    }

    public function conectarBdSaas($iue, $user_id)
    {
        if ($bd_saasargentina = mysqli_connect(BD_HOST, BD_USER, BD_PASS, BD_BD, BD_PORT, BD_SOCKET)) {

            mysqli_set_charset($bd_saasargentina, "utf8mb4");
            $resultado_sql = mysqli_query($bd_saasargentina,
                "SELECT empresas.idempresa,
                    servidores.BD_HOST, servidores.BD_HOST_RO, servidores.BD_USER, servidores.BD_PASS, servidores.BD_BD, servidores.BD_PORT, servidores.BD_SOCKET, servidores.version
                FROM empresas
                    LEFT JOIN servidores ON empresas.idservidor = servidores.idservidor
                WHERE estado IN ('prueba', 'activada', 'demo')
                    AND idsistema != 4
                    AND " . ($iue
                        ? "iue = '$iue'"
                        : "idempresa = (SELECT idempresa FROM tiendas WHERE ML_user_id = '$user_id' LIMIT 1)")
                ." LIMIT 1");
            mysqli_close($bd_saasargentina);

            if (mysqli_num_rows($resultado_sql)) {
                $datos_servidor = mysqli_fetch_assoc($resultado_sql);
                if ($datos_servidor['version'] == 'BETA')
                    $this->beta = true;
                else if ($datos_servidor['version'] == 'ALFA')
                    $this->alfa = true;

                return $this->conectar_db($datos_servidor);

            } else {
                throw new Exception("Error conectando a la base de datos");
            }

        } else {
            throw new Exception("Error conectando a la base de datos");
        }
    }

    public function conectarBdByIUE($iue, $read = false)
    {
        if (!$iue)
            throw new Exception("No se recibió un IUE");

        if ($read)
            $this->read = true;

        return $this->conectarBdSaas($iue, false);
    }

    public function conectarBdByMl($user_id, $read = false)
    {
        if (!$user_id)
            throw new Exception("No se recibió un ML_user_id");

        if ($read)
            $this->read = true;

        return $this->conectarBdSaas(false, $user_id);
    }

    private function conectar_db($datos_servidor)
    {
        $this->idempresa = $datos_servidor['idempresa'];
        $this->bd_link = mysqli_connect(
            ($this->read ? $datos_servidor['BD_HOST_RO'] : $datos_servidor['BD_HOST']),
            $datos_servidor['BD_USER'] . $datos_servidor['idempresa'],
            md5($datos_servidor['BD_PASS'] . $datos_servidor['idempresa']),
            $datos_servidor['BD_BD'] . $datos_servidor['idempresa'],
            $datos_servidor['BD_PORT'],
            $datos_servidor['BD_SOCKET']);
        mysqli_set_charset($this->bd_link, "utf8mb4");

        if (!$this->bd_link)
            throw new Exception("Error seleccionando la base de datos de la empresa");
        else
            return true;
    }

    private function consulta_sql($strSQL)
    {
        $date_start = date("Y-m-d H:i:s");
        $time_start = microtime(true);
        $error = false;

        if (!$resultado_sql = mysqli_query($this->bd_link, $strSQL))
            $error = true;

        $time_end = microtime(true);
        $query_time = $time_end - $time_start;
        if ($query_time > 10) {

            $query = $date_start.SEPARADOR_CSV
                . number_format($query_time, 2).SEPARADOR_CSV
                . ($error === false ? 'OK' : 'ERROR').SEPARADOR_CSV
                . 'api-v1'.SEPARADOR_CSV
                . $this->idempresa.SEPARADOR_CSV
                . $this->iue.SEPARADOR_CSV
                . substr($strSQL, 0, 6).SEPARADOR_CSV
                . $error.SEPARADOR_CSV
                . str_replace(["\r", "\n"], ' ', $strSQL);

            file_put_contents(PATH_LOGS.'queries/'.date("Y-m-d").'.csv', $query.PHP_EOL, FILE_APPEND);
        }

        if ($error) {
            global $metodo;
            global $body;
            global $iue;
            global $url;

            $error = '<br>' . 'Ocurrió un error haciendo una consulta al MySQL en la API en el archivo ' . __FILE__ . '.<br><br><b>Error MySQL:</b> ' . mysqli_error($this->bd_link) . '<br><br>' . '<br><b>&Uacute;ltima consulta:</b> <code>' . $strSQL . '</code>'
                . '<br>metodo: '.json_encode($metodo)
                . '<br>body: '.json_encode($body)
                . '<br>iue: '.json_encode($iue)
                . '<br>idempresa: '.$this->idempresa
                . '<br>url: '.json_encode($url)
                . '</p>';
            // PARCHE: Debería quedar funcionando que se envíe un mail cuando hay algún error, pero por ahora lo guardo en un log
            email_queue(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ERROR: En una query de la API', $error, false);
            file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . $error, FILE_APPEND);
            throw new Exception("Error con la query en la BD. Se registró el error en el log");
        }

        file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . 'Consulta ok: ' . $strSQL . " | " . 'Id: ' . mysqli_insert_id($this->bd_link) . ' | JSON: ' . json_encode(mysqli_fetch_assoc($resultado_sql)), FILE_APPEND);
        if (mysqli_num_rows($resultado_sql)) {
            mysqli_data_seek($resultado_sql, 0);
        }

        return $resultado_sql;
    }

    public function array_sql($resultado_sql)
    {
        return mysqli_fetch_assoc($resultado_sql);
    }

    function array_all_sql($resultado_sql, $key = false)
    {
        $array_all = array();
        while ($array = $this->array_sql($resultado_sql)) {
            if ($key)
                $array_all[$array[$key]] = $array;
            else
                $array_all[] = $array;
        }
        return $array_all;
    }

    public function campo_sql($resultado_sql, $row = 0, $field = 0)
    {
        $this->puntero_sql($resultado_sql, $row);
        $temp_array = $this->array_sql($resultado_sql);

        return $temp_array[$field];
    }

    public function contar_sql($resultado_sql)
    {
        return mysqli_num_rows($resultado_sql);
    }

    public function puntero_sql($resultado_sql, $row = 0)
    {
        return mysqli_data_seek($resultado_sql, $row);
    }

    public function escape_sql($resultado_sql)
    {
        return mysqli_real_escape_string($this->bd_link, $resultado_sql);
    }

    public function id_sql()
    {
        return mysqli_insert_id($this->bd_link);
    }

    public function afectado_sql()
    {
        return mysqli_affected_rows($this->bd_link);
    }

    public function comprobarCodigo($idusuario, $codigo) {
        $sql = "SELECT idcliente FROM clientes WHERE telefonos_codigo = '" . $codigo . "' AND idcliente = '" . $idusuario . "' LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->contar_sql($resultado_sql)) {
            $sql = "UPDATE clientes SET telefonos_verificado = '1' WHERE telefonos_codigo = '" . $codigo . "' AND idcliente = '" . $idusuario . "' LIMIT 1";
            $this->consulta_sql($sql);
            return true;
        } else {
            return false;
        }
    }

    public function obtenerTienda() {
        $sql = "SELECT *,
                (SELECT mail FROM configuraciones LIMIT 1) AS mail_empresa,
                (SELECT url FROM configuraciones LIMIT 1) AS url_empresa
            FROM tienda
            LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        return $this->array_sql($resultado_sql);
    }

    public function actualizarTiendaMl($access_token, $expires_in, $refresh_token) {
        $sql = "UPDATE tienda SET
                ML_access_token = '$access_token',
                ML_expires_in = '$expires_in',
                ML_refresh_token = '$refresh_token'
            LIMIT 1";
        $this->consulta_sql($sql);
    }

    public function actualizarTiendaMp($access_token, $expires_in, $refresh_token) {
        $sql = "UPDATE tienda SET
                MP_access_token = '$access_token',
                MP_expires_in = '$expires_in',
                MP_refresh_token = '$refresh_token'
            LIMIT 1";
        $this->consulta_sql($sql);
    }

    private function obtenerVentaByOrderId($order_id) {
        if (is_numeric($order_id))
            $sql = "SELECT idventa, idcliente, numero FROM ventas WHERE ML_order_id = '" . $order_id . "' LIMIT 1";
        else
            $sql = "SELECT idventa, idcliente, numero FROM ventas WHERE MP_external_reference = '" . $order_id . "' LIMIT 1";

        $resultado_sql = $this->consulta_sql($sql);
        if ($this->contar_sql($resultado_sql))
            return $this->array_sql($resultado_sql);
        else
            return false;
    }

    public function crearCliente($datos_cliente) {
        if (!$datos_cliente['nombre'])
            $datos_cliente['nombre'] = 'Sin Nombre';

        $sql = "INSERT INTO clientes SET
            estado = '" . ($datos_cliente['estado'] === 0 ? 0 : 1) . "',
            nombre = '" . $this->escape_sql($datos_cliente['nombre']) . "',
            contacto = '" . $this->escape_sql($datos_cliente['contacto']) . "',
            idtipocliente = '" . $datos_cliente['idtipocliente'] . "',
            telefonos = '" . $this->escape_sql($datos_cliente['telefonos']) . "',
            mail = '" . $this->escape_sql($datos_cliente['mail']) . "',
            domicilio = '" . $this->escape_sql($datos_cliente['domicilio']) . "',
            idlocalidad = '" . $datos_cliente['idlocalidad'] . "',
            idtipoiva = '" . $datos_cliente['idtipoiva'] . "',
            razonsocial = '" . $this->escape_sql($datos_cliente['razonsocial']) . "',
            cuit = '" . $datos_cliente['cuit'] . "',
            dni = '" . $datos_cliente['dni'] . "',
            pass = '" . ($datos_cliente['pass'] ? md5($datos_cliente['pass']) : '') . "',
            random = '" . $datos_cliente['random'] . "',
            ML_user_id = '" . $datos_cliente['ML_user_id'] . "',
            ML_nickname = '" . $datos_cliente['ML_nickname'] . "',
            observacion = '" . $this->escape_sql($datos_cliente['observacion']) . "'";
        $this->consulta_sql($sql);
        $idcliente = $this->id_sql();

        $this->consulta_sql(
            "INSERT INTO fullsearch_clientes SELECT idcliente,'clientes',
                CONCAT(IFNULL(clientes.idcliente,''), '".FULLSEARCH_SEP_CAMPO."',
                    IFNULL(clientes.nombre,''), '".FULLSEARCH_SEP_CAMPO."',
                    IFNULL(clientes.contacto,''), '".FULLSEARCH_SEP_CAMPO."',
                    IFNULL(clientes.mail,''), '".FULLSEARCH_SEP_CAMPO."',
                    IFNULL(clientes.razonsocial,''), '".FULLSEARCH_SEP_CAMPO."',
                    IFNULL(clientes.cuit,''), '".FULLSEARCH_SEP_CAMPO."',
                    IFNULL(clientes.dni,''), '".FULLSEARCH_SEP_CAMPO."',
                    IFNULL(clientes.ML_user_id,''), '".FULLSEARCH_SEP_CAMPO."',
                    IFNULL(clientes.ML_nickname,'')) AS texto
                FROM clientes
                WHERE clientes.idcliente = $idcliente"
            );

        return $idcliente;
    }

    public function actualizarCliente($datos_cliente) {
        if ($datos_cliente['idcliente'] == 1) {
            return ['No se puede modificar el cliente Consumidor final'];
        }

        $cliente = $this->array_sql($this->consulta_sql("SELECT * FROM clientes WHERE idcliente = '".$datos_cliente['idcliente']."' LIMIT 1"));
        $observacion = '';
        $update = [];

        if (isset($datos_cliente['nombre']) && $datos_cliente['nombre'] && $cliente['nombre'] != $datos_cliente['nombre']) {
            $update[] = "nombre = '".$this->escape_sql($datos_cliente['nombre'])."'";
            $observacion .= "<br><br>El nombre era ".$this->escape_sql($cliente['nombre'])." pero el ".date("d-m-Y")." el usuario lo cambio por ".$this->escape_sql($datos_cliente['nombre']);
        }
        if (isset($datos_cliente['telefonos']) && $datos_cliente['telefonos'] && $cliente['telefonos'] != $datos_cliente['telefonos']) {
            $update[] = "telefonos = '".$this->escape_sql($datos_cliente['telefonos'])."'";
            $observacion .= "<br><br>El telefono era ".$this->escape_sql($cliente['telefonos'])." pero el ".date("d-m-Y")." el usuario lo cambio por ".$this->escape_sql($datos_cliente['telefonos']);
        }
        if (isset($datos_cliente['domicilio']) && $datos_cliente['domicilio'] && $cliente['domicilio'] != $datos_cliente['domicilio']) {
            $update[] = "domicilio = '".$this->escape_sql($datos_cliente['domicilio'])."'";
            $observacion .= "<br><br>El domicilio era ".$this->escape_sql($cliente['domicilio'])." pero el ".date("d-m-Y")." el usuario lo cambio por ".$this->escape_sql($datos_cliente['domicilio']);
        }
        if (isset($datos_cliente['idlocalidad']) && $datos_cliente['idlocalidad'] && $cliente['idlocalidad'] != $datos_cliente['idlocalidad']) {
            $update[] = "idlocalidad = '".$this->escape_sql($datos_cliente['idlocalidad'])."'";
            $observacion .= "<br><br>La localidad cambió el ".date("d-m-Y");
        }

        $update[] = "observacion = '" . $this->escape_sql($datos_cliente['observacion']) . $observacion . "'";

        $sql = "UPDATE clientes
                SET ".implode(', ', $update)."
                WHERE idcliente = '".$datos_cliente['idcliente']."'
                LIMIT 1";

        $this->consulta_sql($sql);

        $this->consulta_sql(
            "UPDATE fullsearch SET fullsearch = (SELECT CONCAT(IFNULL(clientes.idcliente,''),'|#',IFNULL(clientes.nombre,''),'|#',IFNULL(categorias_clientes.nombre,''),'|#',IFNULL(clientes.contacto,''),'|#',IFNULL(clientes.telefonos,''),'|#',IFNULL(clientes.domicilio,''),'|#',IFNULL(categorias_localidades.nombre,''),'|#',IFNULL(tablas_ivas.nombre,''),'|#',IFNULL(clientes.mail,''),'|#',IFNULL(clientes.razonsocial,''),'|#',IFNULL(clientes.cuit,''),'|#',IFNULL(clientes.dni,''),'|#',IFNULL(clientes.observacion,''),'|#',IFNULL(clientes.obsinterna,''),'|#',IFNULL(clientes.obsrecordatorio,''),'|#',IFNULL(clientes.ML_user_id,''),'|#',IFNULL(clientes.ML_nickname,''))
                FROM clientes
                    LEFT OUTER JOIN categorias_clientes ON categorias_clientes.idtipocliente=clientes.idtipocliente
                    LEFT OUTER JOIN categorias_localidades ON categorias_localidades.idlocalidad=clientes.idlocalidad
                    LEFT OUTER JOIN tablas_ivas ON tablas_ivas.idiva=clientes.idtipoiva
                    WHERE clientes.idcliente = '".$datos_cliente['idcliente']."')
                WHERE fullsearch.id = '".$datos_cliente['idcliente']."' AND fullsearch.modulo = 'clientes'"
            );
    }

    public function crearDatosExtra($datosextras)
    {
        if ($datosextras['tipo'] == "lista") {
            $this->consulta_sql("INSERT INTO datosxextras SET
                idextraxmodulo = '".$datosextras['idextraxmodulo']."',
                idrelacion = '".$datosextras['idrelacion']."',
                idlistaxextra = '".$datosextras['idlistaxextra']."'
            ");
        } elseif ($datosextras['tipo'] == "texto") {
            $this->consulta_sql("INSERT INTO datosxextras SET
                idextramodulo = '".$datosextras['idextramodulo']."',
                idrelacion = '".$datosextras['idrelacion']."',
                texto = '".$datosextras['texto']."'
            ");
        }
    }

    public function obtenerIdClienteByEmail($email) {
        $sql = "SELECT idcliente FROM clientes WHERE mail = '$email' LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->contar_sql($resultado_sql)) {
            return $this->campo_sql($resultado_sql, 0, 'idcliente');
        } else {
            return false;
        }
    }

    public function consultarExistenciaML($resource) {
        $temp_array = explode('/', $resource);
        $sql = "SELECT idventa FROM ventas WHERE ML_order_id = '" . $temp_array[2] . "' LIMIT 1";

        $resultado_sql = $this->consulta_sql($sql);
        return $this->contar_sql($resultado_sql);
    }

    public function consultarExistenciaMP($resource) {
        $temp_array = explode('/', $resource);
        $sql = "SELECT idventapago FROM ventaspagos WHERE MP_operation_id = '" . $temp_array[5] . "' LIMIT 1";

        $resultado_sql = $this->consulta_sql($sql);
        return $this->contar_sql($resultado_sql);
    }

    private function obtenerTipoVenta($idtipoventa) {
        $sql = "SELECT letra, puntodeventa, discrimina, tienesituacion, situacion, ultimonumero, muevesaldo, muevestock, operacioninversa, tipofacturacion, observacion, iddeposito, idusuario FROM categorias_ventas WHERE idtipoventa = '$idtipoventa' LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        return $this->array_sql($resultado_sql);
    }

    public function contarProductos($datos) {

        $tienda = $this->obtenerTienda();
        $iddeposito = ($datos['iddeposito'] ? $datos['iddeposito'] : 1);

        $sql = "SELECT COUNT(*) AS total FROM productos";
        if (!$tienda['tienda_sinstock'] && !$datos['mostrarsinstock']) {
            $sql.= " LEFT JOIN stock ON productos.idproducto = stock.idproducto AND iddeposito = $iddeposito";
        }

        if ($datos['mostrarestado'])
            $sql.= " WHERE mostrartienda = '1'";
        else
            $sql.= " WHERE mostrartienda = '1' AND productos.estado = '1' AND productos.estadoventa = '1'";

        if (!$tienda['tienda_sinstock'] && !$datos['mostrarsinstock']) {
            $sql.= " AND stock.stockactual > 0";
        }

        if ($datos['idrubro'])
            $sql.= " AND productos.idrubro='" . ((int) $datos['idrubro']) . "'";

        $resultado_sql = $this->consulta_sql($sql);
        $total = $this->array_sql($resultado_sql);
        return $total['total'];
    }

    public function listarProductos($datos) {

        $idlista = ($datos['idlista'] ? $datos['idlista'] : 1);
        $iddeposito = ($datos['iddeposito'] ? $datos['iddeposito'] : 1);
        $idProductoList = [];

        $tienda = $this->obtenerTienda();


        if ($datos['tipo'] == 'basico') {
            $sql = "SELECT productos.idproducto, productos.codigo, productos.codigoproveedor, productos.sku,
                    stock.stockactual, stock.stockminimo, precios.precio, precios.preciofinal
                FROM productos
                    LEFT JOIN precios ON productos.idproducto = precios.idproducto AND idlista = $idlista
                    LEFT JOIN stock ON productos.idproducto = stock.idproducto AND iddeposito = $iddeposito";

        } else {
            $sql = "SELECT productos.idproducto, productos.codigo, productos.codigoproveedor, productos.sku,
                stock.stockactual, stock.stockminimo, precios.precio, precios.preciofinal,
                productos.nombre, url_amigable, productos.idrubro, codigo, ML_item_id, combo, obstienda,
                categorias_rubros.nombre AS rubro, tablas_unidades.nombre AS unidad, tablas_ivas.valor AS iva";

            if ($datos['mostrarcosto'])
                $sql.= ", productos.costo";
            if ($datos['mostrarestado'])
                $sql.= ", productos.estado, productos.estadoventa";
            if ($datos['mostrarproveedor'])
                $sql.= ", productos.idproveedor, proveedores.nombre AS proveedor";

            $filtrosextras = '';
            if ($datos['datosextras']) {
                $resultado_sql = $this->consulta_sql("SELECT idextraxmodulo, tipo, nombre FROM extrasxmodulos WHERE modulo='productos'");
                while ($extra = $this->array_sql($resultado_sql)) {
                    if ($extra['tipo'] == 'texto') {
                        $sql.= ", IFNULL((SELECT texto FROM datosxextras WHERE idextraxmodulo='" . $extra['idextraxmodulo'] . "' AND datosxextras.idrelacion=productos.idproducto LIMIT 1),'') AS '" . $extra['nombre'] . "'";

                    } else {
                        $sql.= ", (SELECT nombre FROM listasxextras WHERE idlistaxextra=(SELECT idlistaxextra FROM datosxextras WHERE idextraxmodulo='" . $extra['idextraxmodulo'] . "' AND idrelacion=productos.idproducto LIMIT 1) LIMIT 1) AS '" . $extra['nombre'] . "'";
                    }
                }
            }
            $sql.= " FROM productos LEFT JOIN categorias_rubros ON productos.idrubro = categorias_rubros.idrubro
                LEFT JOIN tablas_unidades ON productos.idunidad = tablas_unidades.idunidad
                LEFT JOIN tablas_ivas ON productos.idiva = tablas_ivas.idiva
                LEFT JOIN precios ON productos.idproducto = precios.idproducto AND idlista = $idlista
                LEFT JOIN stock ON productos.idproducto = stock.idproducto AND iddeposito = $iddeposito
                LEFT JOIN proveedores ON productos.idproveedor = proveedores.idproveedor";
        }


        if ($datos['mostrarestado'])
            $sql.= " WHERE mostrartienda = '1'";
        else
            $sql.= " WHERE productos.estado = '1' AND productos.estadoventa = '1' AND mostrartienda = '1'";


        if ($datos['ML_item_id'])
            $sql.= " AND (ML_item_id = '".$datos['ML_item_id']."' OR ML_item_id2 = '".$datos['ML_item_id']."') ";

        if ($datos['tipo'] != 'basico') {
            if (is_numeric($datos['idrubro']) && $datos['idrubro'] > 0) {
                $idrubros_hijos = array($datos['idrubro']);
                $idrubros_hijos = $this->buscarRubrosHijos($datos['idrubro'], $idrubros_hijos);
                $sql .= " AND productos.idrubro IN (".implode(',', $idrubros_hijos).")";
            }
            if (strlen($datos['busqueda']) > 3) {
                $datos['busqueda'] = str_replace(' ', '%', $datos['busqueda']);
                $sql.= " AND (productos.nombre LIKE '%"
                        . $datos['busqueda'] . "%'"
                        . " OR productos.codigo LIKE '%"
                        . $datos['busqueda'] . "%')";
            }
            if ($datos['modificados']) {
                $sql.= " AND productos.updated_at > '"
                        . date("Y-m-d H:i", time() - $datos['modificados'] * 3600) . "'";
            }
            $sql.= $filtrosextras;
            if (!$tienda['tienda_sinstock'] && !$datos['mostrarsinstock']) {
                $sql .= " AND stock.stockactual > 0";
            }
            switch ($datos['orden']) {
                default:
                case 'ultimos':
                    $sql.= " ORDER BY productos.idproducto DESC";
                    break;

                case 'nombre':
                    $sql.= ' ORDER BY productos.nombre ASC';
                    break;
            }
        }

        if ($datos['cantidad'] != 'completo') {
            $sql.= " LIMIT " . ((int) $datos['cantidad']) . " OFFSET " . ((int) $datos['desde']);
        }

        $resultado_sql = $this->consulta_sql($sql);

        $productos = array();
        $i = 0;

        while ($producto = $this->array_sql($resultado_sql)) {
            $idProductoList[] = $producto['idproducto'];
            $productos[] = $producto;
        }
        if ($datos['mostrarimagenes']) {
            $imagenes = $this->obtenerImagenes($idProductoList);
        } else {
            $imagenes = [];
        }
        foreach ($productos as $producto) {
            $idproducto = $producto['idproducto'];
            if ($datos['tipo'] != 'basico') {
                if ($datos['mostrarimagenes'] &&
                    strlen($imagenes[$idproducto]['iua']) == 20) {
                    $producto['archivo'] = $imagenes[$idproducto]['archivo'];
                    $producto['iua'] = $imagenes[$idproducto]['iua'];
                    $producto['publico'] = "1";
                    $producto['imagen'] = URL_S3 . $imagenes[$idproducto]['iua'] . '/imagen';
                    $producto['miniatura'] = URL_S3 . $imagenes[$idproducto]['iua'] . '/miniatura';
                    $producto['imagen_url'] = URL_S3 . $imagenes[$idproducto]['iua'] . '/'. urlencode($imagenes[$idproducto]['archivo']);
                    $producto['imagen_url2A'] = strlen($imagenes[$idproducto]['iua']);
                    $producto['imagen_url3A'] = $imagenes[$idproducto];
                } elseif ($imagenes[$idproducto]['url']) {
                    $producto['imagen'] = $producto['miniatura'] = $imagenes[$idproducto]['url'];
                    $producto['imagen_url'] = URL_S3 . $imagenes[$idproducto]['iua'] . '/'. urlencode($imagenes[$idproducto]['archivo']);
                } else {
                    $producto['iua'] = $producto['imagen'] = $producto['miniatura'] = false;
                }
                $producto['pedido'] = (isset($_SESSION['pedido']) && $_SESSION['pedido'][$idproducto]) ? $_SESSION['pedido'][$idproducto] : 0;
                if ($producto['combo']) {
                    $producto['stockdinamico'] = $this->stockdinamico($idproducto, 1);
                }
            }

            $productos[$i] = $producto;
            $i++;
        }

        return $productos;
    }

    public function verProducto($datos) {
        $tienda = $this->obtenerTienda();
        $idlista = ($datos['idlista'] ? $datos['idlista'] : 1);
        $iddeposito = ($datos['iddeposito'] ? $datos['iddeposito'] : 1);
        $sql_sucursales = ' stock.stockactual, stock.stockminimo, precios.precio, precios.preciofinal, ';
        $sql_join       = ' LEFT JOIN precios ON productos.idproducto = precios.idproducto AND idlista = '.$idlista.' LEFT JOIN stock ON productos.idproducto = stock.idproducto AND iddeposito = '.$iddeposito.' ';

        $sql = "SELECT productos.idproducto, productos.nombre, productos.codigo, productos.codigoproveedor, url_amigable, sku, categorias_rubros.nombre AS rubro, codigo, ML_item_id, combo, tablas_unidades.nombre AS unidad, tablas_ivas.valor AS iva, ".$sql_sucursales." obstienda, productos.idrubro";
        if ($datos['mostrarcosto'])
            $sql.= ", productos.costo";
        if ($datos['mostrarestado'])
            $sql.= ", productos.estado, productos.estadoventa";
        if ($datos['mostrarproveedor'])
            $sql.= ", productos.idproveedor, proveedores.nombre AS proveedor";

        $filtrosextras = '';
        if ($datos['datosextras']) {
            $resultado_sql = $this->consulta_sql("SELECT idextraxmodulo, tipo, nombre FROM extrasxmodulos WHERE modulo='productos'");
            while ($extra = $this->array_sql($resultado_sql)) {
                if ($extra['tipo'] == 'texto') {
                    $sql.= ", (SELECT texto FROM datosxextras WHERE idextraxmodulo='" . $extra['idextraxmodulo'] . "' AND idrelacion=productos.idproducto LIMIT 1) AS '" . $extra['nombre'] . "'";
                } else {
                    $sql.= ", (SELECT nombre FROM listasxextras WHERE idlistaxextra=(SELECT idlistaxextra FROM datosxextras WHERE idextraxmodulo='" . $extra['idextraxmodulo'] . "' AND idrelacion=productos.idproducto LIMIT 1) LIMIT 1) AS '" . $extra['nombre'] . "'";
                }
            }
        }
        $sql.= " FROM productos LEFT JOIN categorias_rubros ON productos.idrubro=categorias_rubros.idrubro LEFT JOIN tablas_unidades ON productos.idunidad=tablas_unidades.idunidad LEFT JOIN tablas_ivas ON productos.idiva=tablas_ivas.idiva LEFT JOIN proveedores ON productos.idproveedor = proveedores.idproveedor";
        $sql.= $sql_join;
        if ($datos['mostrarestado'])
            $sql.= " WHERE mostrartienda = '1'";
        else
            $sql.= " WHERE productos.estado = '1' AND productos.estadoventa = '1' AND mostrartienda = '1'";
        if (!$tienda['tienda_sinstock']){
            $sql .= " AND stock.stockactual > 0";
        }
        if ($datos['idrubro'] && is_numeric($datos['idrubro']))
            $sql.= " AND productos.idrubro='" . ((int) $datos['idrubro']) . "'";
        if ($datos['busqueda']) {
            $datos['busqueda'] = str_replace(' ', '%', $datos['busqueda']);
            $sql.= " AND (productos.nombre like '%" . $datos['busqueda'] . "%'
                OR productos.codigo like '%" . $datos['busqueda'] . "%')";
        }
        $sql.= $filtrosextras;

        if ($datos['producto'] && is_numeric($datos['producto']))
            $sql.= " AND productos.idproducto = " . $datos['producto'];

        switch ($datos['orden']) {
            default:
            case 'ultimos':
                $sql.= " ORDER BY productos.idproducto DESC";
                break;

            case 'nombre':
                $sql.= ' ORDER BY productos.nombre ASC';
                break;
        }
        $sql.= " LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        $producto = $this->array_sql($resultado_sql);

        $idproducto = $producto['idproducto'];
        $sql_archivos = "SELECT iua, publico FROM archivos WHERE modulo='productos' AND id='$idproducto' AND tipo='imagen' ORDER BY idarchivo";
        $resultado_sql_archivos = $this->consulta_sql($sql_archivos);
        $archivos = array();
        $i = 0;
        while ($archivo = $this->array_sql($resultado_sql_archivos)) {
            $a = array();
            if (strlen($archivo['iua']) == 20 && $archivo['publico']) {
                $a['imagen'] = URL_S3 . $archivo['iua'] . '/imagen';
                $a['miniatura'] = URL_S3 . $archivo['iua'] . '/miniatura';
            }
            $archivos[$i] = $a;
            $i++;
        }
        $producto['archivos'] = $archivos;
        $producto['pedido'] = isset($_SESSION['pedido']) && $_SESSION['pedido'][$producto['idproducto']] ? $_SESSION['pedido'][$producto['idproducto']] : $producto['pedido'] = 0;
        if ($producto['combo']) {
            $producto['stockdinamico'] = $this->stockdinamico($producto['idproducto'], $iddeposito);
        }
        return $producto;
    }

    public function contarCombos($datos) {

        $sql = "SELECT COUNT(DISTINCT idcombo) AS total
            FROM productosxcombos AS pxc
                LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto
            WHERE p.combo = 1 AND p.mostrartienda = 1";

        $resultado_sql = $this->consulta_sql($sql);
        $total = $this->array_sql($resultado_sql);
        return $total['total'];
    }

    public function listarCombos($datos) {
        // Primero obtenemos los IDs de los combos paginados
        $sql_combos = "SELECT DISTINCT pxc.idcombo
            FROM productosxcombos AS pxc
                LEFT JOIN productos AS c ON pxc.idcombo = c.idproducto
            WHERE c.combo = 1 AND c.mostrartienda = 1
            ORDER BY pxc.idcombo";

        if ($datos['cantidad'] != 'completo') {
            $sql_combos .= " LIMIT " . ((int) $datos['cantidad']) . " OFFSET " . ((int) $datos['desde']);
        }

        $resultado_combos = $this->consulta_sql($sql_combos);
        $combo_ids = [];
        while ($combo = $this->array_sql($resultado_combos)) {
            $combo_ids[] = $combo['idcombo'];
        }

        if (empty($combo_ids)) {
            return [];
        }

        // Luego obtenemos todos los detalles de esos combos
        $sql = "SELECT pxc.idcombo, c.codigo AS codigo_combo, c.nombre AS combo,
                    pxc.idproducto, p.codigo, p.nombre AS producto, pxc.cantidad
            FROM productosxcombos AS pxc
                LEFT JOIN productos AS c ON pxc.idcombo = c.idproducto
                LEFT JOIN productos AS p ON pxc.idproducto = p.idproducto
            WHERE c.combo = 1
                AND c.mostrartienda = 1
                AND pxc.idcombo IN (" . implode(',', $combo_ids) . ")
            ORDER BY pxc.idcombo";

        $resultado_sql = $this->consulta_sql($sql);
        $combos = array();
        $productos = array();
        $idcombo = null;
        $codigo_combo = $nombre_combo = '';

        while ($combo = $this->array_sql($resultado_sql)) {
            // Si es el primer registro o cambiamos de combo
            if ($idcombo != $combo['idcombo']) {
                // Si no es el primer registro, guardamos el combo anterior
                if ($idcombo !== null) {
                    $combos[] = [
                        'idcombo' => $idcombo,
                        'codigo' => $codigo_combo,
                        'nombre' => $nombre_combo,
                        'productos' => $productos
                    ];
                }

                // Iniciamos un nuevo combo
                $idcombo = $combo['idcombo'];
                $codigo_combo = $combo['codigo_combo'];
                $nombre_combo = $combo['combo'];
                $productos = array();
            }

            // Agregamos el producto al combo actual
            $productos[] = [
                'idproducto' => $combo['idproducto'],
                'codigo' => $combo['codigo'],
                'nombre' => $combo['producto'],
                'cantidad' => $combo['cantidad']
            ];
        }

        // Agregamos el último combo
        if ($idcombo !== null) {
            $combos[] = [
                'idcombo' => $idcombo,
                'codigo' => $codigo_combo,
                'nombre' => $nombre_combo,
                'productos' => $productos
            ];
        }

        return $combos;
    }

    public function verCombo($datos) {
        $idlista = ($datos['idlista'] ? $datos['idlista'] : 1);
        $iddeposito = ($datos['iddeposito'] ? $datos['iddeposito'] : 1);
        $sql_sucursales = ' stock.stockactual, stock.stockminimo, precios.precio, precios.preciofinal, ';
        $sql_join       = ' LEFT JOIN precios ON productos.idproducto = precios.idproducto AND idlista = '.$idlista.' LEFT JOIN stock ON productos.idproducto = stock.idproducto AND iddeposito = '.$iddeposito.' ';

        $sql = "SELECT productos.idproducto, productos.nombre, productos.codigo, productos.codigoproveedor, url_amigable, sku, categorias_rubros.nombre AS rubro, codigo, ML_item_id, combo, tablas_unidades.nombre AS unidad, tablas_ivas.valor AS iva, ".$sql_sucursales." obstienda, productos.idrubro";
        if ($datos['mostrarcosto'])
            $sql.= ", productos.costo";
        if ($datos['mostrarestado'])
            $sql.= ", productos.estado, productos.estadoventa";

        $filtrosextras = '';
        if ($datos['datosextras']) {
            $resultado_sql = $this->consulta_sql("SELECT idextraxmodulo, tipo, nombre FROM extrasxmodulos WHERE modulo = 'productos'");
            while ($extra = $this->array_sql($resultado_sql)) {
                if ($extra['tipo'] == 'texto') {
                    $sql.= ", (SELECT texto FROM datosxextras WHERE idextraxmodulo = '" . $extra['idextraxmodulo'] . "' AND idrelacion = productos.idproducto LIMIT 1) AS '" . $extra['nombre'] . "'";
                } else {
                    $sql.= ", (SELECT nombre FROM listasxextras WHERE idlistaxextra = (SELECT idlistaxextra FROM datosxextras WHERE idextraxmodulo = '" . $extra['idextraxmodulo'] . "' AND idrelacion=productos.idproducto LIMIT 1) LIMIT 1) AS '" . $extra['nombre'] . "'";
                }
            }
        }
        $sql.= "    FROM productos LEFT JOIN categorias_rubros ON productos.idrubro = categorias_rubros.idrubro
                    LEFT JOIN tablas_unidades ON productos.idunidad = tablas_unidades.idunidad
                    LEFT JOIN tablas_ivas ON productos.idiva = tablas_ivas.idiva";
        $sql.= $sql_join;
        $sql.= " WHERE productos.estado = '1' AND productos.estadoventa = '1' AND mostrartienda = '1'";
        $sql.= $filtrosextras;
        if ($datos['idproducto']) {
            $sql.= " AND productos.idproducto = '" . $datos['idproducto'] . "'";
        } else {
            $sql.= " AND productos.codigo = '" . $datos['codigo'] . "'";
        }
        $sql.= " LIMIT 1";

        $resultado_sql = $this->consulta_sql($sql);
        $combo = $this->array_sql($resultado_sql);

        $idproducto = $combo['idproducto'];
        $sql_archivos = "SELECT iua, publico FROM archivos WHERE modulo = 'productos' AND id = '$idproducto' AND tipo = 'imagen' ORDER BY idarchivo";
        $resultado_sql_archivos = $this->consulta_sql($sql_archivos);
        $archivos = array();
        $i = 0;
        while ($archivo = $this->array_sql($resultado_sql_archivos)) {
            $a = array();
            if (strlen($archivo['iua']) == 20 && $archivo['publico']) {
                $a['imagen'] = URL_S3 . $archivo['iua'] . '/imagen';
                $a['miniatura'] = URL_S3 . $archivo['iua'] . '/miniatura';
            }
            $archivos[$i] = $a;
            $i++;
        }
        $combo['archivos'] = $archivos;
        $combo['pedido'] = isset($_SESSION['pedido']) && $_SESSION['pedido'][$combo['idproducto']] ? $_SESSION['pedido'][$combo['idproducto']] : $combo['pedido'] = 0;
        $combo['stockdinamico'] = $this->stockdinamico($combo['idproducto'], $iddeposito);

        $resultado_sql_composicion = $this->consulta_sql("SELECT * FROM productosxcombos WHERE idcombo = '".$idproducto."'");
        $i = 0;
        while ($composicion = $this->array_sql($resultado_sql_composicion)) {
            $productos[$i] = $this->array_sql($this->consulta_sql(
                    "SELECT productos.idproducto, codigo, sku, nombre, stock.stockactual, stock.stockminimo, precios.precio, precios.preciofinal
                    FROM productos
                    LEFT JOIN precios ON productos.idproducto = precios.idproducto AND idlista = '$idlista'
                    LEFT JOIN stock ON productos.idproducto = stock.idproducto AND iddeposito = '$iddeposito'
                    WHERE productos.idproducto = '".$composicion['idproducto']."'
                "));
            $productos[$i]['cantidad'] = $composicion['cantidad'];
            $i++;
        }
        $combo['composicion'] = $productos;

        return $combo;
    }

    public function editarCombo($datos, $metodo)
    {
        $body = $datos['body'];
        $idcombo = ($datos['idproducto']
            // combo = 1 = validación que sea combo
            ? $this->campo_sql($this->consulta_sql("SELECT idproducto FROM productos WHERE idproducto = '".$datos['idproducto']."' AND combo = 1"), 0, 'idproducto')
            : $this->campo_sql($this->consulta_sql("SELECT idproducto FROM productos WHERE codigo = '".$datos['codigo']."' AND combo = 1"), 0, 'idproducto')
        );

        $sql = "";
        $composicion = true;

        foreach($body['composicion'] as $producto) {
            //AND estadocombo = 1 = validación que sea combo
            if ($producto['idproducto']) {
                $temp_idproducto = $this->campo_sql($this->consulta_sql("SELECT idproducto FROM productos WHERE idproducto = '".$producto['idproducto']."' AND estadocombo = 1"), 0, 'idproducto');
            } else {
                $temp_idproducto = $this->campo_sql($this->consulta_sql("SELECT idproducto FROM productos WHERE codigo = '".$producto['codigo']."' AND estadocombo = 1"), 0, 'idproducto');
            }

            //Valido que tenga idcombo, idproducto y cantidad
            if ($idcombo && $temp_idproducto && $producto['cantidad']) {
                if ($metodo == 'PUT') {
                    $sql .= "DELETE FROM productosxcombos WHERE idproducto = ".$temp_idproducto." AND idcombo = ".$idcombo.PHP_EOL;
                }
                $sql .= "INSERT INTO productosxcombos SET idproducto = ".$temp_idproducto.", cantidad = ".$producto['cantidad'].", idcombo = ".$idcombo.PHP_EOL;
            } else {
                $composicion = false;
                break;
            }
        }

        //Valido que el combo esté vacío
        if (($metodo == 'POST' && $temp_idproducto && $idcombo) &&
            ($this->contar_sql($this->consulta_sql("SELECT idproductoxcombo FROM productosxcombos WHERE idproducto = ".$temp_idproducto." AND idcombo = ".$idcombo)))) {
                $composicion = false;
        }

        if ($composicion) {
            $lineas = explode(PHP_EOL, $sql);

            foreach($lineas as $query) {
                if ($query) {
                    $this->consulta_sql($query);
                }
            }
            $resultado = $this->verCombo($datos);
        } else {
            $resultado = "Error: al generar composición de combo";
        }

        return $resultado;
    }

    public function eliminarCombo($datos) {
        $idcombo = ($datos['idproducto']
            // combo = 1 = validación que sea combo
            ? $this->campo_sql($this->consulta_sql("SELECT idproducto FROM productos WHERE idproducto = '".$datos['idproducto']."' AND combo = 1"), 0, 'idproducto')
            : $this->campo_sql($this->consulta_sql("SELECT idproducto FROM productos WHERE codigo = '".$datos['codigo']."' AND combo = 1"), 0, 'idproducto')
        );
        if ($idcombo) {
            $this->consulta_sql("DELETE FROM productosxcombos WHERE idcombo = ".$idcombo);
            $resultado = $this->verCombo($datos);
        } else {
            $resultado = "Error: al eliminar composición de combo";
        }
        return $resultado;
    }

    private function completar_numero($valor, $digitos) {
        if ($valor > 0 && $digitos > 0) {
            $valor = "$valor";
            $return = '';
            $largo_valor = strlen($valor);
            if ($largo_valor > $digitos) {
                $return = substr($valor, 0, $digitos);
            } else {
                $return = $valor;
                for ($i = 0; $i < ($digitos - $largo_valor); $i++) {
                    $return = '0' . $return;
                }
            }
            return $return;
        } elseif ($valor == 0) {
            $return = '';
            for ($j = 0; $j < $digitos; $j++)
                $return.='0';

            return $return;
        } else
            return false;
    }

    public function buscarRubrosHijos($idrubro, $idrubros_hijos) {
        $rubros_hijos_sql = $this->consulta_sql(
            "SELECT idrubro FROM categorias_rubros WHERE idrubropadre = $idrubro");
        while ($rubro_hijo = $this->array_sql($rubros_hijos_sql)) {
            $idrubros_hijos[] = $rubro_hijo['idrubro'];
            $idrubros_hijos = $this->buscarRubrosHijos($rubro_hijo['idrubro'], $idrubros_hijos);
        }
        return $idrubros_hijos;
    }

    public function listarRubros($datos) {
        $sql = "SELECT idrubro, nombre, idrubropadre
            FROM categorias_rubros
            WHERE estado = '1'";

        if ($datos['incluir_subrubros']) {
            // Return all rubros when including subrubros
            if (is_numeric($datos['idrubro']) && $datos['idrubro'] > 0) {
                $idrubros_hijos = array($datos['idrubro']);
                $idrubros_hijos = $this->buscarRubrosHijos($datos['idrubro'], $idrubros_hijos);
                $sql .= " AND idrubro IN (".implode(',', $idrubros_hijos).")";
            }
            // No else - when no idrubro specified, return ALL rubros
            $sql .= " AND idrubro > 0";
        } else {
            // Original behavior
            if (is_numeric($datos['idrubro']) && $datos['idrubro'] > 0) {
                $idrubros_hijos = array();
                $idrubros_hijos = $this->buscarRubrosHijos($datos['idrubro'], $idrubros_hijos);
                if (count($idrubros_hijos))
                    $sql .= " AND idrubro IN (".implode(',', $idrubros_hijos).")";
                else
                    $sql .= " AND idrubro = 0"; // Debería devolver vacío
            } else {
                $sql .= " AND idrubropadre = 0";
            }
            $sql .= "
                AND idrubro > 0
                AND EXISTS (SELECT * FROM productos
                    WHERE productos.idrubro = categorias_rubros.idrubro
                    AND productos.mostrartienda = '1' AND productos.estado = '1'
                    LIMIT 1)";
        }

        $sql .= " ORDER BY categorias_rubros.nombre ASC";

        $resultado_sql = $this->consulta_sql($sql);
        $rubros = $this->array_all_sql($resultado_sql);

        return $rubros;
    }

    public function consultarEstadoApi() {
        $resultado_sql = $this->consulta_sql("SELECT API_estado FROM tienda WHERE API_estado = '1' LIMIT 1");
        if ($this->contar_sql($resultado_sql)) {
            return true;
        } else {
            throw new Exception("Error: la empresa no tiene habilitada la API");
        }
    }

    public function verPedido($datos = []) {

        $productos = array();
        $sql_sucursales = ' stock.stockactual, precios.precio, precios.preciofinal, ';
        $sql_join       = ' LEFT JOIN precios ON productos.idproducto = precios.idproducto AND idlista = 1 LEFT JOIN stock ON productos.idproducto = stock.idproducto AND iddeposito = 1 ';

        if (count($_SESSION['pedido'])) {
            $sql = "SELECT productos.idproducto, productos.nombre, productos.idrubro, url_amigable, categorias_rubros.nombre AS rubro, codigo, tablas_unidades.nombre AS unidad, tablas_ivas.valor AS iva, ".$sql_sucursales." obstienda";
            if ($datos['mostrarcosto'])
                $sql.= ", productos.costo";
            $sql.= ", (SELECT iua FROM archivos WHERE modulo='productos' AND id=productos.idproducto AND tipo='imagen' ORDER BY idarchivo LIMIT 1) AS iua";

            $filtrosextras = '';
            if ($datos['datosextras']) {
                $resultado_sql = $this->consulta_sql("SELECT idextraxmodulo, tipo, nombre FROM extrasxmodulos WHERE modulo='productos'");
                while ($extra = $this->array_sql($resultado_sql)) {
                    if ($extra['tipo'] == 'texto') {
                        $sql.= ", IFNULL((SELECT texto FROM datosxextras WHERE idextraxmodulo='" . $extra['idextraxmodulo'] . "' AND datosxextras.idrelacion=productos.idproducto LIMIT 1),'') AS '" . $extra['nombre'] . "'";
                    } else {
                        $sql.= ", (SELECT nombre FROM listasxextras WHERE idlistaxextra=(SELECT idlistaxextra FROM datosxextras WHERE idextraxmodulo='" . $extra['idextraxmodulo'] . "' AND idrelacion=productos.idproducto LIMIT 1) LIMIT 1) AS '" . $extra['nombre'] . "'";
                    }
                }
            }
            $sql.= " FROM productos LEFT JOIN categorias_rubros ON productos.idrubro=categorias_rubros.idrubro LEFT JOIN tablas_unidades ON productos.idunidad=tablas_unidades.idunidad LEFT JOIN tablas_ivas ON productos.idiva=tablas_ivas.idiva";
            $sql.= $sql_join;
            $sql.= " WHERE productos.estado='1' AND mostrartienda='1'";
            $sql.= $filtrosextras;

            $sql_productos = array();
            foreach ($_SESSION['pedido'] as $key => $value) {
                if (is_numeric($key) && $value > 0)
                    $sql_productos[] = $key;
            }
            if (!count($sql_productos))
                throw new Exception("Error: en la variable del pedido: " . json_encode($_SESSION['pedido']));

            $sql.= " AND productos.idproducto IN (" . implode(',', $sql_productos) . ")";

            $resultado_sql = $this->consulta_sql($sql);

            $i = 0;
            while ($producto = $this->array_sql($resultado_sql)) {
                if (strlen($producto['iua']) == 20) {
                    $producto['imagen'] = URL_S3 . $producto['iua'] . '/imagen';
                    $producto['miniatura'] = URL_S3 . $producto['iua'] . '/miniatura';
                } elseif ($producto['url']) {
                    $producto['imagen'] = $producto['miniatura'] = $producto['url'];
                } else {
                    $producto['iua'] = $producto['imagen'] = $producto['miniatura'] = false;
                }
                $producto['pedido'] = isset($_SESSION['pedido']) && $_SESSION['pedido'][$producto['idproducto']] ? $_SESSION['pedido'][$producto['idproducto']] : $producto['pedido'] = 0;

                // $producto['preciofinal'] = redondeo($producto['precio'] * (1 + $producto['iva'] / 100));
                $producto['importe'] = redondeo($producto['pedido'] * $producto['preciofinal']);
                $productos[$i] = $producto;
                $i++;
            }
        }

        return $productos;
    }

    public function crearVentaDesdeCarrito($external_reference, $datos) {
        if (isset($_SESSION['usuario'])) {
            $tienda = $this->obtenerTienda();
            $tipoventa = $this->obtenerTipoVenta($tienda['tienda_idtipoventa']);
            $fecha = date("Y-m-d H:i:s");
            $items = $this->verPedido();
            $numero = $tipoventa['ultimonumero'] + 1;
            $idcliente = $_SESSION['usuario'];
            $descuento = in_array($datos['formapago'], ['efectivo', 'transferencia', 'f1'])
                ? 30
                : 0;

            //detecto los datos del item de envio
            $envio = (isset($datos["envio"])?true:false);
            $descripcionenvio=isset($datos["descripcionenvio"])?$datos["descripcionenvio"]:"Envio";
            $importeenvio = (isset($datos["importeenvio"])?$datos["importeenvio"]:0);
            $ivaenvio = (isset($datos["ivaenvio"])?$datos["ivaenvio"]:0);
            $direccion = (isset($datos["direccion"])?$datos["direccion"]:"");
            $idextra = (isset($datos["idextra"])?$datos["idextra"]:0);

            if ($datos['observacion']) {
                $tipoventa['observacion'] = $tipoventa['observacion'] .'<br>'.$datos['observacion'];
            }

            if (!count($items)) {
                throw new Exception("Error: al cerrar un pedido desde el carrito");
            } else {

                $subtotal = 0;
                foreach ($items as $item) {
                    $subtotal += $item['importe'];
                }

                if ($envio && $importeenvio > 0){
                    $subtotal += redondeo($importeenvio);
                }

                $iva = $nogravado = $exento = 0;
                $neto = $total = redondeo($subtotal * (100 - $descuento) / 100);

                $cliente = $this->obtenerCliente($idcliente);

                if (strlen($cliente['cuit']) == 11)
                    $cliente['tipodoc'] = "80";
                else if (strlen($cliente['dni']) >= 5)
                    $cliente['tipodoc'] = "96";
                else
                    $cliente['tipodoc'] = "99";

                $this->consulta_sql("INSERT INTO ventas SET
                    idtipoventa = '" . $tienda['tienda_idtipoventa'] . "',
                    estado = 'cerrado',
                    situacion = '" . ($tipoventa['tienesituacion'] ? $tipoventa['situacion'] : 'sin_especificar') . "',
                    idusuario = '" . ($this->beta ? $tipoventa['idusuario'] : $tienda['tienda_idusuario']) . "',
                    idcliente = '" . $idcliente . "',
                    idtipoiva = '" . $cliente['idtipoiva'] . "',
                    iddeposito = '" . $tipoventa['iddeposito'] . "',
                    idlista = '" . $cliente['idlista'] . "',
                    idlocalidad = " . ($cliente['idlocalidad'] ? $cliente['idlocalidad'] : "(SELECT idlocalidad FROM configuraciones LIMIT 1)") . ",
                    cuit = '" . $cliente['cuit'] . "',
                    dni = '" . $cliente['dni'] . "',
                    tipodoc = '" . $cliente['tipodoc'] . "',
                    condicionventa = 'cuentacorriente',
                    fecha = '" . $fecha . "',
                    numero = '" . $numero . "',
                    subtotal = '" . $subtotal . "',
                    descuento = '" . $descuento . "',
                    neto = '" . $neto . "',
                    total = '" . $total . "',
                    muevestock = '" . $tipoventa['muevestock'] . "',
                    muevesaldo = '" . $tipoventa['muevesaldo'] . "',
                    operacioninversa = '" . $tipoventa['operacioninversa'] . "',
                    MP_external_reference = '" . $external_reference . "',
                    observacion = '" . $this->escape_sql($tipoventa['observacion']) . "'
                ");
                $idventa = $this->id_sql();

                $this->consulta_sql("UPDATE categorias_ventas SET ultimonumero = " . $numero . " WHERE idtipoventa = '" . $tienda['tienda_idtipoventa'] . "' LIMIT 1");

                if ($tipoventa['muevesaldo']) {
                    $this->consulta_sql("INSERT INTO ventasxclientes SET
                    idcliente = '" . $idcliente . "',
                    idtipoventa = '" . $tienda['tienda_idtipoventa'] . "',
                    id = '" . $idventa . "',
                    fecha = '" . $fecha . "',
                    total = '" . $total . "',
                    numero = '" . $tipoventa['letra'] . $this->completar_numero($tipoventa['puntodeventa'], 5) . '-' . $this->completar_numero($numero, 8) . "'");

                    $this->actualizar_saldo('clientes', $idcliente);
                    $this->actualizar_saldo('ventas', $idventa);
                }

                $sql_values = array(); // Para cargar cada productoxventa
                $sql_when = array(); // Para cargar cada actualización de stock
                $sql_where = array(); // Para cargar cada idproducto que necesita actualizar stock
                $ajustar_venta = array(); // Para ajustar los valores de IVAs en la venta
                $ajustar_ivas = array(); // Para ajustar los valores de IVAs en la tabla ivasxventas

                foreach ($items as $item) {
                    // Busco si puedo encontrar el producto
                    $id = $item["idproducto"];
                    $sql_sucursales = ' stock.stockactual, precios.precio, precios.preciofinal, ';
                    $sql_join       = ' LEFT JOIN precios ON productos.idproducto = precios.idproducto AND idlista = '. $cliente['idlista'].' LEFT JOIN stock ON productos.idproducto = stock.idproducto AND iddeposito = '. $tipoventa['iddeposito'];

                    $resultado_sql = $this->consulta_sql(
                            "SELECT productos.idproducto, codigo, idunidad, productos.idiva, productos.nombre, combo, costo, observacion, controlarstock,
                            ".$sql_sucursales."
                        tablas_ivas.valor AS iva
                    FROM productos
                        LEFT JOIN tablas_ivas ON productos.idiva = tablas_ivas.idiva
                        ".$sql_join."
                    WHERE productos.idproducto = '$id'
                    LIMIT 1");

                    if ($this->contar_sql($resultado_sql)) {
                        $producto = $this->array_sql($resultado_sql);
                        $sql_values[] = "('$idventa', '" . $producto['idproducto'] . "', '" . $producto['codigo'] . "', '" . $item['pedido'] . "', '" . $producto['idunidad'] . "', '" . $producto['idiva'] . "', '" . $this->escape_sql($producto['nombre']) . "', '" . $producto['costo'] . "', '" . $producto['precio'] . "', '" . $producto['preciofinal'] . "', '" . $this->escape_sql($producto['observacion']) . "')";

                        if ($tipoventa['muevestock'] && $producto['controlarstock']) {
                            if($producto['combo'] && $producto['stockactual'] < $item['pedido']){
                                $falta = ($producto['stockactual'] - $item['pedido'])*-1;

                                $this->mover_stock_combo($producto['idproducto'], $falta, 'combo-producto', $tipoventa['iddeposito']);
                                $this->log_productos($producto['idproducto'], 'venta', $idventa);
                            } else {
                                // Fin armado combo con su respectivos logs
                                $sql_when[] = " WHEN '" . $producto['idproducto'] . "' THEN '" . ($producto['stockactual'] - $item['pedido'] . "'");
                                $sql_where[] = $producto['idproducto'];
                            }
                        }

                        if ($tipoventa['discrimina'] == 'B') {
                            // Le agrego el IVA y ajusto también el neto
                            $producto['importe'] = $item['pedido'] * $producto['precio'];
                            if ($producto['idiva'] > 2) { // Tiene IVA
                                $ajustar_venta['neto'] += $item['pedido'] * $producto['precio'];
                                $ajustar_venta['iva'] += $item['pedido'] * ($producto['preciofinal'] - $producto['precio']);
                                $ajustar_ivas[$producto['idiva']]+= $item['pedido'] * ($producto['preciofinal'] - $producto['precio']);
                            } else if ($producto['idiva'] == 2) { // IVA Exento
                                $ajustar_venta['exento'] += $producto['importe'];
                            } else { // No gravado
                                $ajustar_venta['nogravado'] += $producto['importe'];
                            }

                        } elseif ($tipoventa['discrimina'] != 'C') {
                            // No se puede recibir discrimina A o R
                            $this->mostrar_error('Tipo de venta que recibe Tienda con discrimina ' . $tipoventa['discrimina'], true);
                        }
                    }
                }

                if (count($sql_values)) {

                    //inserto el item de envio
                    if ($envio && $importeenvio > 0){

                        //obtengo el iva para sacar los importes adecuados
                        $importeneto = $importeenvio ;
                        $importefinal = $importeenvio ;
                        if ($ivaenvio> 2){
                            $resultado_sql_ivas = $this->consulta_sql("select * from tablas_ivas where idiva = ".$ivaenvio);
                            if ($this->contar_sql($resultado_sql_ivas)) {
                                $ivas = $this->array_sql($resultado_sql_ivas);
                                $importeneto = $importeenvio - ($importeenvio * $ivas["valor"] /100);
                            }
                        }

                        $sql_values[] = "('$idventa', '0', '', '1', '0', '".$ivaenvio."', '" . $this->escape_sql($descripcionenvio) . "', '0', '" . $importeneto . "', '" .$importefinal . "', '')";

                        if ($tipoventa['discrimina'] == 'B') {
                            // Le agrego el IVA y ajusto también el neto
                            if ($ivaenvio > 2) { // Tiene IVA
                                $ajustar_venta['neto'] += $importeneto;
                                $ajustar_venta['iva'] += $importefinal - $importeneto;
                                $ajustar_ivas[$producto['idiva']]+= $importefinal - $importeneto;
                            } else if ($producto['idiva'] == 2) { // IVA Exento
                                $ajustar_venta['exento'] += $importeneto;
                            } else { // No gravado
                                $ajustar_venta['nogravado'] += $importeneto;
                            }

                        } elseif ($tipoventa['discrimina'] != 'C') {
                            // No se puede recibir discrimina A o R
                            $this->mostrar_error('Tipo de venta que recibe Tienda con discrimina ' . $tipoventa['discrimina'], true);
                        }

                        $this->consulta_sql("INSERT INTO datosxextras SET
                            idextraxmodulo = '".$idextra."',
                            idrelacion = '".$idventa."',
                            idlistaxextra = '0',
                            texto='".$direccion."'
                        ");
                    }

                    $this->consulta_sql("INSERT INTO productosxventas (idventa, idproducto, codigo, cantidad, idunidad, idiva, nombre, costo, precio, preciofinal, observacion) VALUES " . implode(',', $sql_values));



                    // Si hay stock que ajustar armo y ejecuto la consulta
                    if (count($sql_where)) {
                        $this->consulta_sql("UPDATE stock SET stockactual = CASE idproducto " . implode(' ', $sql_when) . " END WHERE idproducto IN (" . implode(',', $sql_where) . ") AND iddeposito = ". $tipoventa['iddeposito']);
                        foreach ($sql_where as $idproducto) {
                            $this->log_productos($idproducto, 'venta', $idventa);
                        }
                    }

                    // Si hay que ajustar los totales y los ivas, entonces actualizo la venta
                    if (count($ajustar_venta)) {
                        $iva = $ajustar_venta['iva'] * (100 - $descuento) / 100;
                        $neto = $ajustar_venta['neto'] * (100 - $descuento) / 100;
                        $nogravado = $ajustar_venta['nogravado'] * (100 - $descuento) / 100;
                        $exento = $ajustar_venta['exento'] * (100 - $descuento) / 100;

                        $this->consulta_sql("UPDATE ventas SET
                                iva = '" . $iva . "',
                                neto = '" . $neto . "',
                                nogravado = '" . $nogravado . "',
                                exento = '" . $exento . "'
                            WHERE idventa = '$idventa'
                            LIMIT 1");

                        if (count($ajustar_ivas)) {
                            foreach($ajustar_ivas as $idiva => $iva) {
                                $iva = $iva * (100 - $descuento) / 100;
                                $this->consulta_sql("INSERT INTO ivasxventas SET
                                        idventa = '$idventa',
                                        idiva = '$idiva',
                                        iva = '" . $iva . "'");
                            }
                        }
                    }
                }

                if ($tipoventa['tipofacturacion'] == 'electronico')
                    file_put_contents(PATH_LOGS . 'sincae.csv',
                    $this->idempresa . "\r\n"
                    , FILE_APPEND);

                $_SESSION['pedido'] = array();

                if ($tienda['idplantilla_venta_tienda']) {
                    $plantilla = $this->obtenerPlantillaMail($tienda['idplantilla_venta_tienda']);
                    $plantilla = str_replace(
                        array('{{cliente}}', '{{idventa}}', '{{numero}}', '{{mail}}'),
                        array($cliente['nombre'], $idventa, $this->obtenerNumeroVenta($idventa), $cliente['mail']),
                        $plantilla);

                    $smtp = $this->obtenerSmtp();
                    if (is_array($smtp)) {
                        $remitente = $smtp['mail'];
                        file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . 'Envié mail de ventas_tienda a: ' . $cliente['mail'] . ' con los datos ' . json_encode($plantilla) . json_encode($smtp), FILE_APPEND);
                        enviar_mail(array($remitente => $remitente), $cliente['mail'], $plantilla['asunto'], $plantilla['texto'], false, $smtp);

                    } else {
                        $remitente = $tienda['mail_empresa'];
                        email_queue(array($remitente => $remitente), $cliente['mail'], $plantilla['asunto'], $plantilla['texto'], false);
                    }

                }

                $return = array(
                    'idventa' => $idventa,
                    'idtipoventa' => $tienda['tienda_idtipoventa'],
                    'situacion' => ($tipoventa['tienesituacion'] ? $tipoventa['situacion'] : 'sin_especificar'),
                    'idcliente' => $idcliente,
                    'fecha' => $fecha,
                    'numero' => $tipoventa['letra'] . $this->completar_numero($tipoventa['puntodeventa'], 5) . '-' . $this->completar_numero($numero, 8),
                    'subtotal' => $subtotal,
                    'iva' => $iva,
                    'neto' => $neto,
                    'nogravado' => $nogravado,
                    'total' => $total,
                    'MP_external_reference' => $external_reference,
                );
                return $return;
            }
        } else {
            return "0";
        }
    }

    public function crearVentaDesdeCarritoConDatosExtra($external_reference, $datos) {
        if (isset($_SESSION['usuario'])) {
            $tienda = $this->obtenerTienda();
            $tipoventa = $this->obtenerTipoVenta($tienda['tienda_idtipoventa']);
            $fecha = date("Y-m-d H:i:s");
            $items = $this->verPedido();
            $numero = $tipoventa['ultimonumero'] + 1;
            $idcliente = $_SESSION['usuario'];

            if (!count($items)) {
                throw new Exception("Error: al cerrar un pedido desde el carrito");
            } else {

                $total = 0;
                foreach ($items as $item) {
                    $total += $item['importe'];
                }

                $extras = "";
                if ($datos != null) {
                    foreach ($datos as $key => $value) {
                        $extras .= $key . '=' . $value . ",";
                    }
                }

                $cliente = $this->obtenerCliente($idcliente);

                if (strlen($cliente['cuit']) == 11)
                    $cliente['tipodoc'] = "80";
                else if (strlen($cliente['dni']) >= 5)
                    $cliente['tipodoc'] = "96";
                else
                    $cliente['tipodoc'] = "99";

                $this->consulta_sql("INSERT INTO ventas SET
                    idtipoventa = '" . $tienda['tienda_idtipoventa'] . "',
                    estado = 'cerrado',
                    situacion = '" . ($tipoventa['tienesituacion'] ? $tipoventa['situacion'] : 'sin_especificar') . "',
                    idusuario = '" . ($this->beta ? $tipoventa['idusuario'] : $tienda['tienda_idusuario']) . "',
                    idcliente = '" . $idcliente . "',
                    idtipoiva = '" . $cliente['idtipoiva'] . "',
                    iddeposito = '" . $tipoventa['iddeposito'] . "',
                    idlista = '" . $cliente['idlista'] . "',
                    cuit = '" . $cliente['cuit'] . "',
                    dni = '" . $cliente['dni'] . "',
                    tipodoc = '" . $cliente['tipodoc'] . "',
                    idlocalidad = " . ($cliente['idlocalidad'] ? $cliente['idlocalidad'] : "(SELECT idlocalidad FROM configuraciones LIMIT 1)") . ",
                    condicionventa = 'cuentacorriente',
                    fecha = '" . $fecha . "',
                    numero = '" . $numero . "',
                    subtotal = '" . $total . "',
                    neto = '" . $total . "',
                    total = '" . $total . "', " . $extras . "
                    muevestock = '" . $tipoventa['muevestock'] . "',
                    muevesaldo = '" . $tipoventa['muevesaldo'] . "',
                    operacioninversa = '" . $tipoventa['operacioninversa'] . "',
                    MP_external_reference = '" . $external_reference . "',
                    observacion = '" . $this->escape_sql($tipoventa['observacion']) . "'
                ");
                $idventa = $this->id_sql();

                $this->consulta_sql("UPDATE categorias_ventas SET ultimonumero = " . $numero . " WHERE idtipoventa = '" . $tienda['tienda_idtipoventa'] . "' LIMIT 1");

                if ($tipoventa['muevesaldo']) {
                    $this->consulta_sql("INSERT INTO ventasxclientes SET
                    idcliente = '" . $idcliente . "',
                    idtipoventa = '" . $tienda['tienda_idtipoventa'] . "',
                    id = '" . $idventa . "',
                    fecha = '" . $fecha . "',
                    total = '" . $total . "',
                    numero = '" . $tipoventa['letra'] . $this->completar_numero($tipoventa['puntodeventa'], 5) . '-' . $this->completar_numero($numero, 8) . "'");
                    $this->actualizar_saldo('clientes', $idcliente);
                    $this->actualizar_saldo('ventas', $idventa);
                }

                $sql_values = array(); // Para cargar cada productoxventa
                $sql_when = array(); // Para cargar cada actualización de stock
                $sql_where = array(); // Para cargar cada idproducto que necesita actualizar stock
                $ajustar_venta = array(); // Para ajustar los valores de IVAs

                foreach ($items as $item) {
                    // Busco si puedo encontrar el producto
                    $id = $item["idproducto"];
                    $sql_sucursales = ' stock.stockactual, precios.precio, precios.preciofinal, ';
                    $sql_join       = ' LEFT JOIN precios ON productos.idproducto = precios.idproducto AND idlista = '. $cliente['idlista'] .' LEFT JOIN stock ON productos.idproducto = stock.idproducto AND iddeposito = ' . $tipoventa['iddeposito'];
                    $resultado_sql = $this->consulta_sql(
                            "SELECT productos.idproducto, codigo, idunidad, productos.idiva, productos.nombre, costo, observacion, controlarstock,
                            ".$sql_sucursales."
                        tablas_ivas.valor AS iva
                    FROM productos
                        LEFT JOIN tablas_ivas ON productos.idiva = tablas_ivas.idiva
                        ".$sql_join."
                    WHERE productos.idproducto = '$id'
                    LIMIT 1");

                    if ($this->contar_sql($resultado_sql)) {
                        $producto = $this->array_sql($resultado_sql);
                        $sql_values[] = "('$idventa', '" . $producto['idproducto'] . "', '" . $producto['codigo'] . "', '" . $item['pedido'] . "', '" . $producto['idunidad'] . "', '" . $producto['idiva'] . "', '" . $this->escape_sql($producto['nombre']) . "', '" . $producto['costo'] . "', '" . $producto['precio'] . "', '" . $producto['preciofinal'] . "', '" . $this->escape_sql($producto['observacion']) . "')";

                        if ($tipoventa['muevestock'] && $producto['controlarstock']) {
                            $sql_when[] = " WHEN '" . $producto['idproducto'] . "' THEN '" . ($producto['stockactual'] - $item['pedido'] . "'");
                            $sql_where[] = $producto['idproducto'];
                        }

                        if ($tipoventa['discrimina'] == 'B') {
                            // Le agrego el IVA y ajusto también el neto
                            $producto['importe'] = $item['pedido'] * $producto['precio'];
                            if ($producto['idiva']) {
                                $producto['base_imponible'] = redondeo($producto['importe'] / (1 + $producto['iva'] / 100));
                                $ajustar_venta['neto'] += $producto['base_imponible'];
                                $ajustar_venta['iva' . $producto['idiva']] += $producto['importe'] - $producto['base_imponible'];
                            } else {
                                $ajustar_venta['nogravado'] += $producto['importe'];
                            }
                        } elseif ($tipoventa['discrimina'] != 'C') {
                            // No se puede recibir discrimina A o R
                            $this->mostrar_error('Tipo de venta que recibe ML con discrimina ' . $tipoventa['discrimina'], true);
                        }
                    } else {
                        $sql_values[] = "('$idventa', '0', '', '" . $item['pedido'] . "', '0', '0', '" . $item['nombre'] . "', '0', '" . $item['precio'] . "', '" . $item['precio'] . "', '')";

                        // Le agrego el IVA y acomodo esos valores según el tipo de venta
                        if ($tipoventa['discrimina'] == 'B') {
                            $ajustar_venta['nogravado']+= $item['pedido'] * $item['precio'];
                        }
                    }
                }

                if (count($sql_values)) {

                    $this->consulta_sql("INSERT INTO productosxventas (idventa, idproducto, codigo, cantidad, idunidad, idiva, nombre, costo, precio, preciofinal, observacion) VALUES " . implode(',', $sql_values));

                    // Si hay stock que ajustar armo y ejecuto la consulta
                    if (count($sql_where)) {
                        $this->consulta_sql("UPDATE stock SET stockactual = CASE idproducto " . implode(' ', $sql_when) . " END WHERE idproducto IN (" . implode(',', $sql_where) . ") AND iddeposito = ". $tipoventa['iddeposito']);
                        foreach ($sql_where as $idproducto) {
                            $this->log_productos($idproducto, 'venta', $idventa);
                        }
                    }

                    // Si hay que ajustar los totales y los ivas, entonces actualizo la venta
                    if (count($ajustar_venta)) {
                        $this->consulta_sql("UPDATE ventas SET
                                iva = '" . $ajustar_venta['iva'] . "',
                                neto = '" . $ajustar_venta['neto'] . "'
                            WHERE idventa = '$idventa'
                            LIMIT 1");
                    }
                    $_SESSION['pedido'] = array();

                    return array(
                        'idventa' => $idventa,
                        'idtipoventa' => $tienda['tienda_idtipoventa'],
                        'situacion' => ($tipoventa['tienesituacion'] ? $tipoventa['situacion'] : 'sin_especificar'),
                        'idcliente' => $idcliente,
                        'fecha' => $fecha,
                        'numero' => $tipoventa['letra'] . $this->completar_numero($tipoventa['puntodeventa'], 5) . '-' . $this->completar_numero($numero, 8),
                        'subtotal' => $total,
                        'neto' => $total,
                        'total' => $total,
                        'MP_external_reference' => $external_reference,
                    );
                }
            }
        } else {
            return "0";
        }
    }

    public function crearVentaDesdePedido($datos)
    {
        $tienda = $this->obtenerTienda();
        $tipoventa = $this->obtenerTipoVenta($tienda['tienda_idtipoventa']);
        $fecha = date("Y-m-d H:i:s");
        $items = $datos['items'];
        $numero = $tipoventa['ultimonumero'] + 1;
        $descuento = 0;
        $external_reference = $datos['external_reference'];
        if (!$datos['idlista']) $datos['idlista'] = 1;

        // Si se recibe campo email en lugar de mail, lo cambio
        if (!isset($datos['cliente']['mail']) && isset($datos['cliente']['email'])) {
            $datos['cliente']['mail'] = $datos['cliente']['email'];
            unset($datos['cliente']['email']);
        }
        $idcliente = $this->obtenerIdClienteByEmail($datos['cliente']['mail']);

        if (!$idcliente) {
            $datos['cliente']['idlocalidad'] = $this->obtenerIdLocalidad($datos['cliente']);
            $datos['cliente']['idtipocliente'] = $tienda['tienda_idtipocliente'];
            $datos['cliente']['observacion'] = "Creado automáticamente desde tienda";
            $idcliente = $this->crearCliente($datos['cliente']);

            if ($datos['extras'] && $datos['extras']['modulo'] == 'clientes') {
                $datos['extras']['idrelacion'] = $idcliente;
                $this->crearDatosExtra($datos['extras']);
            }

        } else {
            $datos['cliente']['idcliente'] = $idcliente;
            $datos['cliente']['idlocalidad'] = $this->obtenerIdLocalidad($datos['cliente']);
            $datos['cliente']['observacion'] = "Actualizado automáticamente desde tienda";
            $this->actualizarCliente($datos['cliente']);
        }

        //Descuento % de categorias_clientes
        $cliente = $this->obtenerCliente($idcliente);
        $descuento = $cliente['descuento'];

        if ($descuento) {
            $descuento_total = $datos['total'] - ($datos['total'] * (1 - $descuento / 100));
        }

        //El descuento general de la venta viene en $datos['descuento'], lo transformamos en %
        if ($datos['descuento']) {
            $descuento += ($datos['descuento'] * 100) / ($datos['total'] + $datos['descuento']);
        }

        if ($datos['observacion']) {
            $tipoventa['observacion'] = $tipoventa['observacion'] .'<br>'.$datos['observacion'];
        }

        if (!count($items)) {
            throw new Exception("Error: al cerrar un pedido desde el carrito");
        } else {
            $iva = $nogravado = $exento = 0;
            $subtotal = $datos['total'] + $datos['descuento'];
            $neto = $total = $datos['total'] - $descuento_total;
            if (!in_array($datos['estado'], ['abierto','cerrado','anulado']))
                $datos['estado'] = 'cerrado';

            if (strlen($cliente['cuit']) == 11)
                $cliente['tipodoc'] = "80";
            else if (strlen($cliente['dni']) >= 5)
                $cliente['tipodoc'] = "96";
            else
                $cliente['tipodoc'] = "99";

            $this->consulta_sql("INSERT INTO ventas SET
                idtipoventa =  '". ($datos['idtipoventa'] ? $datos['idtipoventa'] : $tienda['tienda_idtipoventa']) . "',
                estado = '". $datos['estado'] . "',
                situacion = '" . ($tipoventa['tienesituacion'] ? $tipoventa['situacion'] : 'sin_especificar') . "',
                idusuario = '" . $tipoventa['idusuario'] . "',
                idcliente = '" . $idcliente . "',
                idtipoiva = '" . $cliente['idtipoiva'] . "',
                iddeposito = '". $tipoventa['iddeposito'] . "',
                idlista =  '". ($datos['idlista'] ? $datos['idlista'] : 1) . "',
                domicilio = '" . $this->escape_sql($cliente['domicilio']) . "',
                idlocalidad = " . ($cliente['idlocalidad'] ? $cliente['idlocalidad'] : "(SELECT idlocalidad FROM configuraciones LIMIT 1)") . ",
                cuit = '" . $cliente['cuit'] . "',
                dni = '" . $cliente['dni'] . "',
                tipodoc = '" . $cliente['tipodoc'] . "',
                condicionventa = 'cuentacorriente',
                fecha = '" . $fecha . "',
                numero = '" . $numero . "',
                subtotal = '" . $subtotal . "',
                descuento = '" . $descuento . "',
                neto = '" . $neto . "',
                total = '" . $total . "',
                muevestock = '" . $tipoventa['muevestock'] . "',
                muevesaldo = '" . $tipoventa['muevesaldo'] . "',
                operacioninversa = '" . $tipoventa['operacioninversa'] . "',
                MP_external_reference = '" . $this->escape_sql($external_reference) . "',
                observacion = '" . $this->escape_sql($tipoventa['observacion']) . "'");
            $idventa = $this->id_sql();

            $this->consulta_sql("UPDATE categorias_ventas SET ultimonumero = " . $numero . " WHERE idtipoventa = '". ($datos['idtipoventa'] ? $datos['idtipoventa'] : $tienda['tienda_idtipoventa']) . "' LIMIT 1");

            if ($tipoventa['muevesaldo'] && $datos['estado'] != 'abierto') {
                $this->consulta_sql("INSERT INTO ventasxclientes SET
                    idcliente = '" . $idcliente . "',
                    idtipoventa = '". ($datos['idtipoventa'] ? $datos['idtipoventa'] : $tienda['tienda_idtipoventa']) . "',
                    id = '" . $idventa . "',
                    fecha = '" . $fecha . "',
                    total = '" . $total . "',
                    numero = '" . $tipoventa['letra'] . $this->completar_numero($tipoventa['puntodeventa'], 5) . '-' . $this->completar_numero($numero, 8) . "'");

                $this->actualizar_saldo('clientes', $idcliente);
                $this->actualizar_saldo('ventas', $idventa);
            }

            $sql_values = array(); // Para cargar cada productoxventa
            $sql_when = array(); // Para cargar cada actualización de stock
            $sql_where = array(); // Para cargar cada idproducto que necesita actualizar stock
            $ajustar_venta = array(); // Para ajustar los valores de IVAs en la venta
            $ajustar_ivas = array(); // Para ajustar los valores de IVAs en la tabla ivasxventas
            $nuevo_total = 0; //Por si pagacosto

            // Dividir los productos que tienen cantidad mayor a 1 en varios productos
            if (in_array($this->idempresa, [8100])) {
                foreach ($items as $j => $item) {
                    if ($item['cantidad'] > 1) {
                        $cantidad = $item['cantidad'];
                        $item['cantidad'] = 1;
                        for ($i = 0; $i < $cantidad; $i++) {
                            $items[] = $item;
                        }
                        unset($items[$j]);
                    }
                }
            }

            foreach ($items as $item) {
                // Busco si puedo encontrar el producto
                if (is_numeric($item['idproducto']) || !empty($item['codigo'])) {
                    $sql_item = "SELECT productos.idproducto, codigo, idunidad, productos.idiva, productos.nombre, combo, costo, observacion, controlarstock,
                                    stock.stockactual, precios.precio, precios.preciofinal,
                                    tablas_ivas.valor AS iva
                                FROM productos
                                    LEFT JOIN tablas_ivas ON productos.idiva = tablas_ivas.idiva
                                    LEFT JOIN precios ON productos.idproducto = precios.idproducto AND idlista = '{$datos['idlista']}'
                                    LEFT JOIN stock ON productos.idproducto = stock.idproducto AND iddeposito = '{$tipoventa['iddeposito']}'
                                WHERE " . ($item['idproducto'] ? " productos.idproducto = '{$item['idproducto']}' " : " productos.codigo = '{$item['codigo']}' " ) . "
                                LIMIT 1";

                    $resultado_sql = $this->consulta_sql($sql_item);
                    $producto = $this->array_sql($resultado_sql);
                } else {
                    // Vacío variable
                     unset($producto);
                }

                if (!isset($producto['idproducto'])) {

                    $iva = $this->obtenerIva($tienda['ML_idiva']);

                    $producto = [
                        'idproducto' => 0,
                        'codigo'     => '',
                        'nombre' => $item['nombre'],
                        'observacion' => $item['observacion'],
                        'idunidad'   => 0,
                        'idiva' => $iva['idiva'],
                        'costo' => 0,
                        'preciofinal' => $item['preciofinal'],
                        'precio' => $item['preciofinal'] / (1 + $iva['valor'] / 100),
                    ];

                    if ($tipoventa['discrimina'] == 'B') {
                            // Le agrego el IVA y ajusto también el neto
                        $producto['importe'] = $item['cantidad'] * $producto['precio'];
                        if ($producto['idiva'] > 2) { // Tiene IVA
                            $ajustar_venta['neto'] += $item['cantidad'] * $producto['precio'];
                            $ajustar_venta['iva'] += $item['cantidad'] * ($producto['preciofinal'] - $producto['precio']);
                            $ajustar_ivas[$producto['idiva']] += $item['cantidad'] * ($producto['preciofinal'] - $producto['precio']);
                        } else if ($producto['idiva'] == 2) { // IVA Exento
                            $ajustar_venta['exento'] += $producto['importe'];
                        } else { // No gravado
                            $ajustar_venta['nogravado'] += $producto['importe'];
                        }
                    }

                    $sql_values[] = "('$idventa', '" . $producto['idproducto'] . "', '" . $producto['codigo'] . "', '" . $item['cantidad'] . "', '" . $producto['idunidad'] . "', '" . $producto['idiva'] . "', '" . $this->escape_sql($producto['nombre']) . "', '" . $producto['costo'] . "', '" . $producto['precio'] . "', '" . $producto['preciofinal'] . "', '" . $this->escape_sql($producto['observacion']) . "')";

                } else {

                    if ($cliente['pagacosto'] && !$item['envio']) {
                        $producto['precio'] = $producto['costo'];
                        $producto['preciofinal'] = $producto['costo'];
                        $nuevo_total += $producto['costo'] * $item['cantidad'];
                    }

                    if ($item['envio'] && $item['costoenvio']) {
                        $producto['precio'] = $item['costoenvio']; //ver acá si es "B"
                        $producto['preciofinal'] = $item['costoenvio'];
                        $nuevo_total += $item['costoenvio'] * $item['cantidad'];
                    }

                    //Viene con precio desde carrito:
                    if (isset($item['preciofinal']) && $item['preciofinal']) {
                        $producto['preciofinal'] = $item['preciofinal'];
                        $producto['precio'] = $item['preciofinal'] / (1 + $iva['valor'] / 100);
                    }

                    $sql_values[] = "('$idventa', '" . $producto['idproducto'] . "', '" . $producto['codigo'] . "', '" . $item['cantidad'] . "', '" . $producto['idunidad'] . "', '" . $producto['idiva'] . "', '" . $this->escape_sql($producto['nombre']) . "', '" . $producto['costo'] . "', '" . $producto['precio'] . "', '" . $producto['preciofinal'] . "', '" . $this->escape_sql($producto['observacion']) . "')";

                    if ($tipoventa['muevestock'] && $producto['controlarstock'] && $datos['estado'] != 'abierto') {
                        if ($producto['combo'] && $producto['stockactual'] < $item['cantidad']) {
                            $falta = ($producto['stockactual'] - $item['cantidad'])*-1;

                            $this->mover_stock_combo($producto['idproducto'], $falta, 'combo-producto', $tipoventa['iddeposito']);
                            $this->log_productos($producto['idproducto'], 'venta', $idventa);
                        } else {
                            // Fin armado combo con su respectivos logs
                            $sql_when[] = " WHEN '" . $producto['idproducto'] . "' THEN '" . ($producto['stockactual'] - $item['cantidad'] . "'");
                            $sql_where[] = $producto['idproducto'];
                        }
                    }

                    if ($tipoventa['discrimina'] == 'B') {
                        // Le agrego el IVA y ajusto también el neto
                        $producto['importe'] = $item['cantidad'] * $producto['precio'];
                        if ($producto['idiva'] > 2) { // Tiene IVA
                            $ajustar_venta['neto'] += $item['cantidad'] * $producto['precio'];
                            $ajustar_venta['iva'] += $item['cantidad'] * ($producto['preciofinal'] - $producto['precio']);
                            $ajustar_ivas[$producto['idiva']] += $item['cantidad'] * ($producto['preciofinal'] - $producto['precio']);
                        } else if ($producto['idiva'] == 2) { // IVA Exento
                            $ajustar_venta['exento'] += $producto['importe'];
                        } else { // No gravado
                            $ajustar_venta['nogravado'] += $producto['importe'];
                        }

                    } elseif ($tipoventa['discrimina'] != 'C') {
                        // No se puede recibir discrimina A o R
                        $this->mostrar_error('No se puede recibir discrimina A o R ' . $tipoventa['discrimina']
                                . ' para idventa = ' . $idventa, true);
                    }

                }
            }
            //end foreach

            if (count($sql_values)) {
                $this->consulta_sql("INSERT INTO productosxventas (idventa, idproducto, codigo, cantidad, idunidad, idiva, nombre, costo, precio, preciofinal, observacion)
                                    VALUES " . implode(',', $sql_values));

                // Si hay stock que ajustar armo y ejecuto la consulta
                if (count($sql_where) && $datos['estado'] != 'abierto') {
                    $this->consulta_sql("UPDATE stock SET stockactual = CASE idproducto " . implode(' ', $sql_when) . " END WHERE idproducto IN (" . implode(',', $sql_where) . ") AND iddeposito = " .$tipoventa['iddeposito']. "");
                    foreach ($sql_where as $idproducto) {
                        $this->log_productos($idproducto, 'venta', $idventa);
                    }
                }

                // Si hay que ajustar los totales y los ivas, entonces actualizo la venta
                if (count($ajustar_venta) && !$cliente['pagacosto']) {
                    $iva = $ajustar_venta['iva'] * (100 - $descuento) / 100;
                    $neto = $ajustar_venta['neto'] * (100 - $descuento) / 100;
                    $nogravado = $ajustar_venta['nogravado'] * (100 - $descuento) / 100;
                    $exento = $ajustar_venta['exento'] * (100 - $descuento) / 100;

                    $this->consulta_sql("UPDATE ventas SET
                            iva = '" . $iva . "',
                            neto = '" . $neto . "',
                            nogravado = '" . $nogravado . "',
                            exento = '" . $exento . "'
                        WHERE idventa = '$idventa'
                        LIMIT 1");

                    if (count($ajustar_ivas)) {
                        foreach($ajustar_ivas as $idiva => $iva) {
                            $iva = $iva * (100 - $descuento) / 100;
                            $this->consulta_sql("INSERT INTO ivasxventas SET
                                    idventa = '$idventa',
                                    idiva = '$idiva',
                                    iva = '" . $iva . "'");
                        }
                    }
                }

                if ($cliente['pagacosto']) {
                    $iva = 0;
                    $neto = 0;
                    $nogravado = $nuevo_total;
                    $exento = 0;
                    $subtotal = $nuevo_total;
                    $total = $nuevo_total;

                    $this->consulta_sql("UPDATE ventas SET
                            iva = '" . $iva . "',
                            neto = '" . $neto . "',
                            nogravado = '" . $nogravado . "',
                            exento = '" . $exento . "',
                            subtotal = '" . $subtotal ."',
                            total = '" . $total . "'
                        WHERE idventa = '$idventa'
                        LIMIT 1");
                }
            }

            if ($tipoventa['tipofacturacion'] == 'electronico') {
                file_put_contents(PATH_LOGS . 'sincae.csv',
                $this->idempresa . "\r\n"
                , FILE_APPEND);
            }

            /*if ($tienda['idplantilla_venta_tienda']) {
                $plantilla = $this->obtenerPlantillaMail($tienda['idplantilla_venta_tienda']);
                $smtp = $this->obtenerSmtp();
                if (is_array($smtp)) {
                    $remitente = $smtp['mail'];
                } else {
                    $remitente = $tienda['mail_empresa'];
                    $smtp = 'no-reply';
                }
                $cliente = $this->array_sql($this->consulta_sql(
                    "SELECT nombre, mail
                    FROM clientes
                    WHERE idcliente = '$idcliente'"));
                $plantilla = str_replace(
                    array('{{cliente}}', '{{idventa}}', '{{numero}}', '{{mail}}'),
                    array($cliente['nombre'], $idventa, $this->obtenerNumeroVenta($idventa), $cliente['mail']),
                    $plantilla);

                file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . 'Envié mail de ventas_tienda a: ' . $cliente['mail'] . ' con los datos ' . json_encode($plantilla) . json_encode($smtp), FILE_APPEND);
                enviar_mail(array($remitente => $remitente), $cliente['mail'], $plantilla['asunto'], $plantilla['texto'], false, $smtp);
            }*/

            $idtipoventa = ($datos['idtipoventa'] ? $datos['idtipoventa'] : $tienda['tienda_idtipoventa']);
            $return = array(
                'idventa' => $idventa,
                'idtipoventa' => $idtipoventa,
                'situacion' => ($tipoventa['tienesituacion'] ? $tipoventa['situacion'] : 'sin_especificar'),
                'idcliente' => $idcliente,
                'fecha' => $fecha,
                'numero' => $tipoventa['letra'] . $this->completar_numero($tipoventa['puntodeventa'], 5) . '-' . $this->completar_numero($numero, 8),
                'subtotal' => $subtotal,
                'iva' => $iva,
                'neto' => $neto,
                'nogravado' => $nogravado,
                'total' => $total,
            );
            return $return;
        }
    }

    public function anularventa(int $idventa)
    {

        $venta = $this->array_sql($this->consulta_sql("SELECT * FROM ventas WHERE idventa = '".$idventa."' LIMIT 1"));

        if ($venta) {
            if ($venta['muevestock']) {
                $resultado_sql = $this->consulta_sql("SELECT productosxventas.*, stock.stockactual, productos.controlarstock, productos.combo
                    FROM productosxventas
                    LEFT JOIN productos ON productosxventas.idproducto = productos.idproducto
                    LEFT JOIN stock ON productos.idproducto = stock.idproducto AND stock.iddeposito = '".$venta['iddeposito']."'
                    WHERE idventa = '".$idventa."'
                    ");
                while ($productoxventa = $this->array_sql($resultado_sql)) {
                    if ($productoxventa['controlarstock']) {
                        $this->consulta_sql("UPDATE stock SET stockactual = stockactual ".($venta['opinversa'] ? "-" : "+")." '".$productoxventa['cantidad']."' WHERE idproducto = '".$productoxventa['idproducto']."' AND iddeposito = '".$venta['iddeposito']."' LIMIT 1");
                        $this->log_productos($productoxventa['idproducto'], 'venta-anular', $idventa);
                    }
                }
            }

            if ($venta['muevesaldo']) {
                $this->consulta_sql("DELETE FROM ventasxclientes WHERE idtipoventa = '".$venta['idtipoventa']."' AND id = '".$venta['idventa']."'");
            }

            $this->consulta_sql("UPDATE ventas SET
                estado = 'anulado'
                WHERE idventa = '".$idventa."'
            ");

            if ($venta['muevesaldo']) {
                $this->actualizar_saldo('ventas', $idventa);
                $this->actualizar_saldo('clientes', $venta['idcliente']);
                // if ($venta['tiporelacion'] == 'servicio') {
                //     $this->actualizar_saldo('ventasxservicios', $venta['idrelacion']);
                // }
            }

            return true;
        }
    }

    private function concatenarObsMl($idventa, $concatenar) {
        // Le agrego nueva info del pago relacionado solamente si no se lo agregué antes
        $this->consulta_sql("UPDATE ventas SET
                obsML = CONCAT(obsml, '$concatenar')
            WHERE idventa = '$idventa'
                AND obsML NOT LIKE '%$concatenar%'
            LIMIT 1");
    }

    public function obtenerIdClienteByEmailYPass($email, $pass) {
        $sql = "SELECT idcliente FROM clientes WHERE mail = '$email' AND pass = '" . md5($pass) . "'  LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->contar_sql($resultado_sql)) {
            return $this->campo_sql($resultado_sql, 0, 'idcliente');
        } else {
            return false;
        }
    }

    public function isClienteHabilitado($email, $pass) {
        $sql = "SELECT idcliente FROM clientes WHERE estado = '1' AND mail = '$email' AND pass = '" . md5($pass) . "'  LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->contar_sql($resultado_sql)) {
            return $this->campo_sql($resultado_sql, 0, 'idcliente');
        } else {
            return false;
        }
    }

    public function listarVentas($datos) {
        $ventas = array();
        if (isset($_SESSION['usuario'])) {
            $idcliente = $_SESSION['usuario'];
            $sql = "SELECT idventa, fecha, numero, total, ventas.situacion,
                    categorias_ventas.idtipoventa, categorias_ventas.letra, categorias_ventas.puntodeventa, categorias_ventas.nombre AS tipoventa,
                (total - (SELECT SUM(total) FROM ventaspagos WHERE ventaspagos.idventa = ventas.idventa)) AS saldo
                FROM ventas
                    LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
                WHERE idcliente = '$idcliente' AND ventas.estado = 'cerrado'
                ORDER BY ventas.fecha DESC";
            $resultado_sql = $this->consulta_sql($sql);
            $i = 0;
            $k = 0;

            $sql_productos = ""; // para los productos

            while ($venta = $this->array_sql($resultado_sql)) {
                $idventa = $venta['idventa'];
                $venta['numero'] = $venta['letra'] . $this->completar_numero($venta['puntodeventa'], 5) . '-' . $this->completar_numero($venta['numero'], 8);
                $sql_productos = "SELECT idproductoxventa, idproducto, codigo, cantidad, precio, nombre, idunidad, descuento FROM productosxventas WHERE idventa = '$idventa'";

                $resultado_productos = $this->consulta_sql($sql_productos);
                $productos = Array();
                while ($producto = $this->array_sql($resultado_productos)) {
                    $idproductoxventas = $producto['idproductoxventa'];
                    $producto['cantidad'] = ($producto['idunidad'] == 7 && substr($producto['cantidad'], -3) == '000') ? number_format($producto['cantidad'], 0, '.', '') : $producto['cantidad'];
                    $sql_archivos_ventas = "SELECT iua FROM archivos WHERE modulo='ventas' AND id='$idproductoxventas' AND tipo='imagen'";
                    $resultado_sql_archivo = $this->consulta_sql($sql_archivos_ventas);
                    $archivo = $this->array_sql($resultado_sql_archivo);
                    if ($archivo) {
                        $producto['archivo'] = URL_S3 . $archivo['iua'] . '/archivo';
                    } else {
                        $producto['archivo'] = false;
                    }

                    $sql_trazabilidades = "SELECT movimientosxtrazabilidades.idtrazabilidad, movimientosxtrazabilidades.cantidad,
                            trazabilidades.codigo, trazabilidades.vencimiento, trazabilidades.idarchivo, trazabilidades.observacion,
                            archivos.nombre, archivos.iua, archivos.tipo, archivos.archivo, archivos.publico, archivos.fecha, archivos.bytes, archivos.url,
                            usuarios.nombre AS usuario
                        FROM movimientosxtrazabilidades
                            INNER JOIN trazabilidades ON movimientosxtrazabilidades.idtrazabilidad = trazabilidades.idtrazabilidad
                            LEFT JOIN archivos ON trazabilidades.idarchivo = archivos.idarchivo
                            LEFT JOIN usuarios ON archivos.idusuario = usuarios.idusuario
                        WHERE tiporelacion = 'venta'
                            AND idrelacion = '" . $producto['idproductoxventa'] . "'";
                    $resultado_sql_trazabilidades = $this->consulta_sql($sql_trazabilidades);

                    $trazabilidades = array();
                    while ($trazabilidad = $this->array_sql($resultado_sql_trazabilidades)) {
                        if ($trazabilidad['idarchivo'] && $trazabilidad['publico'])
                            $trazabilidad['url'] = URL_S3 . $trazabilidad['iua'] . '/' . urlencode($trazabilidad['archivo']);
                        $trazabilidad['codigo'] = openssl_encrypt($trazabilidad['codigo'], 'aes128', 'IipbTa07PHsYepRqXaOzi3jIMNh4Gjdw');
                        $trazabilidades[] = $trazabilidad;
                    }
                    $producto['trazabilidades'] = $trazabilidades;
                    $productos[$k] = $producto;
                    $k++;
                }
                $venta['productos'] = $productos;
                $ventas[$i] = $venta;
                $i++;
            }
        }
        return $ventas;
    }

    public function verUsuario($datos) {
        $idcliente = $datos['idcliente'];
        // TODO: No devolver todos los datos, solo los necesarios, para por ejemplo no borrar el pass
        $sql = "SELECT * FROM clientes WHERE idcliente = '$idcliente'";
        $resultado_sql = $this->consulta_sql($sql);
        $cliente = $this->array_sql($resultado_sql);
        $cliente['cuit'] = $cliente['cuit'] ? $cliente['cuit'] : '';
        $cliente['dni'] = $cliente['dni'] ? $cliente['dni'] : '';
        unset($cliente['pass']);
        unset($cliente['obsinterna']);
        return $cliente;
    }

    public function modificarUsuario($datos) {
        if (!$datos['idcliente'])
            throw new Exception('Error en el metodo modificarUsuario en la capa de persistencia porque no se recibió un idcliente');

        if ($datos['idcliente'] < 2)
            throw new Exception('Error en el metodo modificarUsuario en la capa de persistencia porque se intentó modificar el idcliente '.$datos['idcliente']);

        $idcliente = $datos['idcliente'];
        unset($datos['idcliente']);
        unset($datos['iue']);
        unset($datos['pass']);

        // TODO: Esto quizás no debería saltar una Exception sino responder bien que la consulta fue mal hecha verificando antes
        if (!count($datos))
            throw new Exception('Error en el metodo modificarUsuario en la capa de persistencia porque no había datos para modificar');

        $sql_campos = array();
        foreach ($datos as $key => $value) {
            if ($value !== false)
                $sql_campos[] = $key . "='" . $this->escape_sql($value) . "'";
        }
        if (!count($sql_campos))
            return false;

        $sql = "UPDATE clientes SET " . implode(',', $sql_campos) . " WHERE idcliente = '$idcliente' LIMIT 1";
        return $this->consulta_sql($sql);
    }

    public function listarTiposIva() {
        $sql = "SELECT idtipoiva, nombre FROM tablas_condiciones";
        $resultado_sql = $this->consulta_sql($sql);
        $tiposdeiva = Array();
        while ($tipoiva = $this->array_sql($resultado_sql)) {
            $tiposdeiva[$tipoiva['idtipoiva']] = $tipoiva['nombre'];
        }
        return $tiposdeiva;
    }

    public function obtenerSmtp()
    {
        $where = $this->ML_user_id
            ? "WHERE ML_user_id = '$this->ML_user_id'"
            : "WHERE idtienda = '1'";
        $sql = "SELECT * FROM smtps WHERE idsmtp = (SELECT idsmtp FROM tienda ".$where.")";

        $resultado_sql = $this->consulta_sql($sql);
        if (!$this->contar_sql($resultado_sql))
            return false;

        $smtp = $this->array_sql($resultado_sql);
        $smtp['pass'] = openssl_decrypt($smtp['pass'], 'AES-128-CTR', SMTP_PW, 0, '');

        return $smtp;
    }

    public function obtenerPlantillaMail($idplantilla) {
        $sql = "SELECT * FROM plantillas WHERE idplantilla = '$idplantilla' LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->contar_sql($resultado_sql)) {
            return $this->array_sql($resultado_sql);
        } else {
            return false;
        }
    }

    public function confirmarMail($iue, $random) {
        $sql = "UPDATE clientes SET estado = '1', random = '' WHERE random = '$random' LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->afectado_sql($resultado_sql)) {
            return true;
        } else {
            return false;
        }
    }

    public function confirmarPass($iue, $random) {
        $sql = "UPDATE clientes SET pass = '" . md5($random) . "', random = '' WHERE estado = '1' AND random = '$random' LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->afectado_sql($resultado_sql)) {
            return true;
        } else {
            return false;
        }
    }

    public function resetearPass($iue, $mail) {
        $idcliente = $this->obtenerIdClienteByEmail($mail);

        if ($idcliente) {

            $tienda = $this->obtenerTienda();
            if ($tienda['idplantilla_resetear_pass']) {

                $random = generar_random(10);
                $datos = array('idcliente' => $idcliente, 'random' => $random);

                $plantilla = $this->obtenerPlantillaMail($tienda['idplantilla_resetear_pass']);
                $plantilla['texto'] = str_replace(
                    array('{{random}}', '{{pass}}', '{{iue}}'),
                    array($random, $random, $iue),
                    $plantilla['texto']);

                $smtp = $this->obtenerSmtp();
                if (is_array($smtp)) {
                    $remitente = $smtp['mail'];
                    enviar_mail(array($remitente => $remitente), $mail, $plantilla['asunto'], $plantilla['texto'], false, $smtp);

                } else {
                    $remitente = $tienda['mail_empresa'];
                    email_queue(array($remitente => $remitente), $mail, $plantilla['asunto'], $plantilla['texto'], false);
                }

                $this->modificarUsuario($datos);

                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public function obtenerNumeroVenta($idventa) {
        $sql = "SELECT ventas.numero,
                categorias_ventas.letra, categorias_ventas.puntodeventa
            FROM ventas
                LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
            WHERE idventa = '$idventa'
            LIMIT 1";

        $resultado_sql = $this->consulta_sql($sql);
        if ($this->contar_sql($resultado_sql)) {
            $venta = $this->array_sql($resultado_sql);
            return $venta['letra'] . $this->completar_numero($venta['puntodeventa'], 5) . '-' . $this->completar_numero($venta['numero'], 8);
        } else {
            return false;
        }
    }

    public function modificarProductos($productos) {
        $array_precios = ['precio', 'preciofinal', 'utilidad'];
        $array_stock = ['stockactual', 'stockminimo', 'stockideal'];
        $querys = Array();
        $i = 0;
        $valores = "";
        $valores_precios = "";
        $valores_stock = "";
        foreach ($productos as $p) {
            $query = "UPDATE productos SET ";
            $campos = is_array($p->{'campos'}) ? $p->{'campos'} : [];
            foreach ($campos as $campo) {
                if (array_search($campo->{'campo'}, $array_precios) !== false){
                    $valores_precios .= ($valores_precios != '') ? ", " : "";
                } elseif(array_search($campo->{'campo'}, $array_stock) !== false){
                    $valores_stock .= ($valores_stock != '') ? ", " : "";
                } else {
                    $valores .= ($valores != '') ? ", " : "";
                }
                if ($campo->{'campo'} != 'idproducto') {
                    switch ($campo->{'tipoDato'}) {
                        case "string":
                            if (array_search($campo->{'campo'}, $array_precios) !== false)
                                $valores_precios .= $campo->{'campo'} . " = " . "'" . $campo->{'valor'} . "'";
                            elseif(array_search($campo->{'campo'}, $array_stock) !== false)
                                $valores_stock .= $campo->{'campo'} . " = " . "'" . $campo->{'valor'} . "'";
                            else
                                $valores .= $campo->{'campo'} . " = " . "'" . $campo->{'valor'} . "'";
                            break;

                        case "decimal":
                        case "entero":
                            if (array_search($campo->{'campo'}, $array_precios) !== false)
                                $valores_precios .= $campo->{'campo'} . " = " . $campo->{'valor'};
                            elseif(array_search($campo->{'campo'}, $array_stock) !== false)
                                $valores_stock .= $campo->{'campo'} . " = " . $campo->{'valor'};
                            else
                                $valores .= $campo->{'campo'} . " = " . $campo->{'valor'};
                            break;
                    }
                }
            }
            if($valores_precios){
                $query_precios = "UPDATE precios SET ";
                $query_precios .= $valores_precios . " WHERE precios.idproducto = " . $p->{'idproducto'} . " AND idlista = 1";
                $this->consulta_sql($query_precios);
            }
            if($valores_stock){
                $query_stock = "UPDATE stock SET ";
                $query_stock .= $valores_stock . " WHERE stock.idproducto = " . $p->{'idproducto'} . " AND iddeposito = 1";
                $this->consulta_sql($query_stock);
            }
            if ($valores)
                $querys[$i] = $query . $valores . " WHERE idproducto = " . $p->{'idproducto'};
            $i++;
        }
        foreach ($querys as $q) {
            $this->consulta_sql($q);
        }
    }

    public function eliminarProducto($idProducto) {
        $sql = "DELETE FROM productos WHERE idproducto = " . $idProducto;
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->afectado_sql($resultado_sql)) {
            $this->consulta_sql("DELETE FROM precios WHERE idproducto = ".$idProducto." AND idlista = 1");
            $this->consulta_sql("DELETE FROM stock WHERE idproducto = ".$idProducto." AND iddeposito = 1");
            return '1';
        } else {
            return '0';
        }
    }

    public function agregarProducto($campos) {
        $valores = "";
        $definicion = "";
        $valores_precios = "";
        $definicion_precios = "";
        $valores_stock = "";
        $definicion_stock = "";
        $camposObligatoriosExistentes = Array();
        $i = 0;
        $array_precios = ['precio', 'preciofinal', 'utilidad'];
        $array_stock = ['stockactual', 'stockminimo', 'stockideal'];
        foreach ($campos as $campo) {
            if (array_search($campo->{'campo'}, $array_precios) !== false){
                $valores_precios .= ($valores_precios != '') ? ", " : "";
                $definicion_precios .= ($valores_precios != '') ? ", " : "";
            } elseif(array_search($campo->{'campo'}, $array_stock) !== false) {
                $valores_stock .= ($valores_stock != '') ? ", " : "";
                $definicion_stock .= ($valores_stock != '') ? ", " : "";
            } else {
                $valores .= ($valores != '') ? ", " : "";
                $definicion .= ($valores != '') ? ", " : "";
            }
            switch ($campo->{'tipoDato'}) {
                case "string":
                    if (in_array($campo->{'campo'}, $this->camposObligatoriosProducto) && !in_array($campo->{'campo'}, $camposObligatoriosExistentes)) {
                        $camposObligatoriosExistentes[$i] = $campo->{'campo'};
                        $i++;
                    }
                    if (array_search($campo->{'campo'}, $array_precios) !== false){
                        $valores_precios .= "'" . $campo->{'valor'} . "'";
                        $definicion_precios .= $campo->{'campo'};
                    } elseif(array_search($campo->{'campo'}, $array_stock) !== false) {
                        $valores_stock .= "'" . $campo->{'valor'} . "'";
                        $definicion_stock .= $campo->{'campo'};
                    } else {
                        $valores .= "'" . $campo->{'valor'} . "'";
                        $definicion .= $campo->{'campo'};
                    }
                    break;

                case "entero":
                    if (in_array($campo->{'campo'}, $this->camposObligatoriosProducto) && !in_array($campo->{'campo'}, $camposObligatoriosExistentes)) {
                        $camposObligatoriosExistentes[$i] = $campo->{'campo'};
                        $i++;
                    }
                    if (array_search($campo->{'campo'}, $array_precios) !== false){
                        $valores_precios .= $campo->{'valor'};
                        $definicion_precios .= $campo->{'campo'};
                    } elseif(array_search($campo->{'campo'}, $array_stock) !== false) {
                        $valores_stock .= $campo->{'valor'};
                        $definicion_stock .= $campo->{'campo'};
                    } else {
                        $valores .= $campo->{'valor'};
                        $definicion .= $campo->{'campo'};
                    }
                    break;

                case "decimal":
                    if (in_array($campo->{'campo'}, $this->camposObligatoriosProducto) && !in_array($campo->{'campo'}, $camposObligatoriosExistentes)) {
                        $camposObligatoriosExistentes[$i] = $campo->{'campo'};
                        $i++;
                    }
                    if (array_search($campo->{'campo'}, $array_precios) !== false){
                        $valores_precios .= "'" . $campo->{'valor'} . "'";
                        $definicion_precios .= $campo->{'campo'};
                    } elseif(array_search($campo->{'campo'}, $array_stock) !== false) {
                        $valores_stock .= "'" . $campo->{'valor'} . "'";
                        $definicion_stock .= $campo->{'campo'};
                    } else {
                        $valores .= "'" . $campo->{'valor'} . "'";
                        $definicion .= $campo->{'campo'};
                    }
                    break;
            }
        }
        $query = "INSERT INTO productos (" . $definicion . ") VALUES (" . $valores . ")";
        $query_precios = "";
        $query_stock = "";
        if (count($this->camposObligatoriosProducto) == count($camposObligatoriosExistentes)) {
            $this->consulta_sql($query);
            $idproducto = $this->id_sql();
            if($valores_precios){
                $query_precios .= "INSERT INTO precios (idproducto, idlista, " . $definicion_precios . ") VALUES (" . $idproducto . ", 1, " .$valores_precios . ")";
                $this->consulta_sql($query_precios);
            }
            if($valores_stock){
                $query_stock .= "INSERT INTO stock (idproducto, iddeposito, " . $definicion_stock . ") VALUES (" . $idproducto . ", 1, " .$valores_stock . ")";
                $this->consulta_sql($query_stock);
            }
        } else {
            $camposFaltantes = '';
            foreach ($this->camposObligatoriosProducto as $capoObligatorio) {
                if (!in_array($capoObligatorio, $camposObligatoriosExistentes)) {
                    $camposFaltantes .= ($camposFaltantes != '' ? ',' : '');
                    $camposFaltantes .= $capoObligatorio;
                }
            }
            throw new Exception("No se inserto el producto ya que faltan campos obligatorios: " . $camposFaltantes);
        }
    }

    public function modificarPassUsuario($id_usuario, $pass_actual, $pass_nuevo) {
        // TODO: sacar lo de id_usuario y reemplazarlo por lo que está en la variable de sesión
        $sql = "SELECT idcliente FROM clientes WHERE estado = '1' AND idcliente = " . $id_usuario . " and pass = '" . md5($pass_actual) . "' LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->contar_sql($resultado_sql)) {
            $sql = "UPDATE clientes SET pass = '" . md5($pass_nuevo) . "' WHERE estado = '1' AND idcliente = " . $id_usuario . " and pass = '" . md5($pass_actual) . "' LIMIT 1";
            $this->consulta_sql($sql);
            return '1';
        } else {
            return '0';
        }
    }

    public function cargarExternalReference($datos) {
        $sql = "UPDATE ventas SET MP_external_reference = '" . $datos['external_reference'] . "' WHERE idventa = " . $datos['venta'] . " LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->afectado_sql($resultado_sql)) {
            return '1';
        } else {
            return '2';
        }
    }

    public function verVenta($datos) {
        file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . 'VENTAS 4', FILE_APPEND);
        $sql = "SELECT idventa, ventas.situacion, fecha, numero, total, MP_external_reference,
                categorias_ventas.idtipoventa, categorias_ventas.letra, categorias_ventas.puntodeventa, categorias_ventas.nombre AS tipoventa,
            (total - (SELECT SUM(total) FROM ventaspagos WHERE ventaspagos.idventa = ventas.idventa)) AS saldo, idcliente
            FROM ventas
                LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
            WHERE idventa = '" . $datos['venta'] . "' AND ventas.estado = 'cerrado' ORDER BY ventas.fecha DESC, ventas.situacion ASC";
        $resultado_sql = $this->consulta_sql($sql);

        $k = 0;
        $venta = $this->array_sql($resultado_sql);

        $sql_productos = "SELECT pxv.idproductoxventa, pxv.idproducto, pxv.codigo, pxv.cantidad, pxv.precio, pxv.preciofinal, pxv.nombre, pxv.idunidad, pxv.descuento, p.costo, p.idrubro FROM productosxventas pxv JOIN productos p ON pxv.idproducto = p.idproducto WHERE idventa = '" . $datos['venta'] . "'";

        $resultado_productos = $this->consulta_sql($sql_productos);
        $productos = Array();
        if (!$this->contar_sql($this->consulta_sql($sql_productos))) return false;

        while ($producto = $this->array_sql($resultado_productos)) {
            $idproductoxventas = $producto['idproductoxventa'];
            $producto['cantidad'] = ($producto['idunidad'] == 7 && substr($producto['cantidad'], -3) == '000') ? number_format($producto['cantidad'], 0, '.', '') : $producto['cantidad'];
            $sql_archivos_ventas = "SELECT iua FROM archivos WHERE modulo='ventas' AND id='$idproductoxventas' AND tipo='imagen'";
            $resultado_sql_archivo = $this->consulta_sql($sql_archivos_ventas);
            $archivo = $this->array_sql($resultado_sql_archivo);
            if ($archivo) {
                $producto['archivo'] = URL_S3 . $archivo['iua'] . '/archivo';
            } else {
                $producto['archivo'] = false;
            }

            $sql_trazabilidades = "SELECT movimientosxtrazabilidades.idtrazabilidad, movimientosxtrazabilidades.cantidad,
                        trazabilidades.codigo, trazabilidades.vencimiento, trazabilidades.idarchivo, trazabilidades.observacion,
                        archivos.nombre, archivos.iua, archivos.tipo, archivos.archivo, archivos.publico, archivos.fecha, archivos.bytes, archivos.url,
                        usuarios.nombre AS usuario
                    FROM movimientosxtrazabilidades
                        INNER JOIN trazabilidades ON movimientosxtrazabilidades.idtrazabilidad = trazabilidades.idtrazabilidad
                        LEFT JOIN archivos ON trazabilidades.idarchivo = archivos.idarchivo
                        LEFT JOIN usuarios ON archivos.idusuario = usuarios.idusuario
                    WHERE tiporelacion = 'venta'
                        AND idrelacion = '" . $producto['idproductoxventa'] . "'";
            $resultado_sql_trazabilidades = $this->consulta_sql($sql_trazabilidades);

            $trazabilidades = array();
            while ($trazabilidad = $this->array_sql($resultado_sql_trazabilidades)) {
                if ($trazabilidad['idarchivo'] && $trazabilidad['publico'])
                    $trazabilidad['url'] = URL_S3 . $trazabilidad['iua'] . '/' . urlencode($trazabilidad['archivo']);
                $trazabilidad['codigo'] = openssl_encrypt($trazabilidad['codigo'], 'aes128', 'IipbTa07PHsYepRqXaOzi3jIMNh4Gjdw');
                $trazabilidades[] = $trazabilidad;
            }
            $producto['trazabilidades'] = $trazabilidades;
            $productos[$k] = $producto;
            $k++;
        }

        $venta['cliente'] = $this->obtenerCliente($venta['idcliente']);
        $venta['productos'] = $productos;
        return $venta;
    }

    public function esCliente($mail, $pass) {
        $filtro_pass = '';
        if ($pass != '') {
            $filtro_pass = " AND pass = '" . md5($pass) . "'";
        }
        $sql = "SELECT idcliente, pass FROM clientes WHERE estado = '1' AND mail = '$mail' " . $filtro_pass . "LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        $resultado = array();
        if ($this->contar_sql($resultado_sql)) {
            $resultado['idcliente'] = $this->campo_sql($resultado_sql, 0, 'idcliente');
            $resultado['pass'] = $this->campo_sql($resultado_sql, 0, 'pass');
            return $resultado;
        } else {
            return false;
        }
    }

    public function esClienteMl($datos) {
        $sql = "SELECT idcliente, mail FROM clientes WHERE estado = '1' AND ML_nickname = '" . $datos['apodo'] . "' LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        $resultado = array();
        if ($this->contar_sql($resultado_sql)) {
            $id_tipo_venta = "";
            $order_id = $datos['order_id'];
            $sql_venta = "SELECT * FROM ventas WHERE ML_order_id = '$order_id'";
            $resultado_sql_venta = $this->consulta_sql($sql_venta);
            if ($this->contar_sql($resultado_sql_venta)) {
                $resultado['idcliente'] = $this->campo_sql($resultado_sql, 0, 'idcliente');
                $resultado['mail'] = $this->campo_sql($resultado_sql, 0, 'mail');
                return $resultado;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public function modificarPassUsuarioForzada($id_usuario, $pass_nuevo) {
        $sql = "UPDATE clientes SET pass = '" . md5($pass_nuevo) . "' WHERE estado = '1' AND idcliente = " . $id_usuario . " LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($this->afectado_sql($resultado_sql)) {
            return '1';
        } else {
            return '0';
        }
    }

    public function crearPagoDecidir($datos) {
        $order_id = $datos['external_reference'];
        $venta = $this->obtenerVentaByOrderId($order_id);
        $idventa = $venta['idventa'];
        $numeroventa = $venta['numero'];
        $idcliente = $venta['idcliente'];
        $idventapago = $this->crearVentaPagoDecidir($datos, $idcliente, $idventa);
    }

    public function crearPagoTodoPago($datos) {
        $order_id = $datos['external_reference'];
        $venta = $this->obtenerVentaByOrderId($order_id);
        $idventa = $venta['idventa'];
        $numeroventa = $venta['numero'];
        $idcliente = $venta['idcliente'];
        $idventapago = $this->crearVentaPagoTodoPago($datos, $idcliente, $idventa);
    }

    private function crearVentaPagoDecidir($datos, $idcliente, $idventa) {
/*
        $tienda = $this->obtenerTienda();
        $fecha = date("Y-m-d H:i:s", strtotime(str_replace("/", "-", $datos['fecha'])));
        $total = str_replace(",", ".", $datos['total']);

        // Si no se a que caja mandarlo, por ahora tiro un error
        if (!$tienda['MP_idtipocaja'])
            throw new Exception("Error: No se a que caja mandar un pago de MercadoPago", 1);

        $sql = "INSERT INTO ventaspagos SET
            idcliente = '" . $idcliente . "',
            idusuario = '0',
            idventa = '" . $idventa . "',
            idformapago = '10',
            fecha = '" . $fecha . "',
            total = '" . $total . "',
            observacion = 'Pago cargado automáticamente desde Decidir'";
//            MP_operation_id = '" . $datos->{'collection'}->{'id'} . "'";
        $this->consulta_sql($sql);
        $idventapago = $this->id_sql();

        $sql = "INSERT INTO ventasxclientes SET
            idcliente = '" . $idcliente . "',
            idtipoventa = '0',
            id = '" . $idventapago . "',
            fecha = '" . $fecha . "',
            total = '" . $total . "',
            numero = ''";
        $this->consulta_sql($sql);
        $this->actualizar_saldo('clientes', $idcliente);
        $this->actualizar_saldo('ventas', $idventa);

        $sql = "SELECT categorias_cajas.idtipocaja, categorias_cajas.idcaja, categorias_cajas.estado,
                cajas.saldocierre
            FROM categorias_cajas
                LEFT JOIN cajas ON categorias_cajas.idcaja = cajas.idcaja
            WHERE categorias_cajas.idtipocaja = '" . $tienda['MP_idtipocaja'] . "'
            LIMIT 1";
        $tipocaja = $this->array_sql($this->consulta_sql($sql));

        if ($tipocaja['estado'] == 0) {
            $sql = "INSERT INTO cajas SET
                idtipocaja = '" . $tipocaja['idtipocaja'] . "',
                fechaapertura = '" . $fecha . "',
                saldoapertura = '" . $tipocaja['saldocierre'] . "'";
            $this->consulta_sql($sql);
            $idcaja = $this->id_sql();

            $sql = "UPDATE categorias_cajas SET
                    estado = '1',
                    idcaja = '$idcaja'
                WHERE idtipocaja = '" . $tipocaja['idtipocaja'] . "'
                LIMIT 1";
            $this->consulta_sql($sql);
            $tipocaja['idcaja'] = $idcaja;
        }

        $sql = "INSERT INTO movimientosxcajas SET
            idcaja = '" . $tipocaja['idcaja'] . "',
            idusuario = '0',
            idconcepto = '" . $tienda['MP_idconcepto'] . "',
            fecha = '" . $fecha . "',
            total = '" . $total . "',
            detalle = 'Recibo de pago RP" . $this->completar_numero($idventapago, 8) . "',
            tiporelacion = 'clientepago',
            idrelacion = '" . $idventapago . "'";
        $this->consulta_sql($sql);
*/
    }

    private function crearVentaPagoTodoPago($datos, $idcliente, $idventa) {
/*
        $tienda = $this->obtenerTienda();
        $fecha = $datos['fecha'];
        $total = str_replace(",", ".", $datos['total']);

        // Si no se a que caja mandarlo, por ahora tiro un error
        if (!$tienda['MP_idtipocaja'])
            throw new Exception("Error: No se a que caja mandar un pago de MercadoPago", 1);

        $observacion = 'Pago cargado automáticamente desde TodoPago: ' . $datos['detalle'];
        $sql = "INSERT INTO ventaspagos SET
            idcliente = '" . $idcliente . "',
            idusuario = '0',
            idventa = '" . $idventa . "',
            idformapago = '10',
            fecha = '" . $fecha . "',
            total = '" . $total . "',
            observacion = '" . $observacion . "'";
        $this->consulta_sql($sql);
        $idventapago = $this->id_sql();

        $sql = "INSERT INTO ventasxclientes SET
            idcliente = '" . $idcliente . "',
            idtipoventa = '0',
            id = '" . $idventapago . "',
            fecha = '" . $fecha . "',
            total = '" . $total . "',
            numero = ''";
        $this->consulta_sql($sql);
        $this->actualizar_saldo('clientes', $idcliente);
        $this->actualizar_saldo('ventas', $idventa);

        $sql = "SELECT categorias_cajas.idtipocaja, categorias_cajas.idcaja, categorias_cajas.estado,
                cajas.saldocierre
            FROM categorias_cajas
                LEFT JOIN cajas ON categorias_cajas.idcaja = cajas.idcaja
            WHERE categorias_cajas.idtipocaja = '" . $tienda['MP_idtipocaja'] . "'
            LIMIT 1";
        $tipocaja = $this->array_sql($this->consulta_sql($sql));

        if ($tipocaja['estado'] == 0) {
            $sql = "INSERT INTO cajas SET
                idtipocaja = '" . $tipocaja['idtipocaja'] . "',
                fechaapertura = '" . $fecha . "',
                saldoapertura = '" . $tipocaja['saldocierre'] . "'";
            $this->consulta_sql($sql);
            $idcaja = $this->id_sql();

            $sql = "UPDATE categorias_cajas SET
                    estado = '1',
                    idcaja = '$idcaja'
                WHERE idtipocaja = '" . $tipocaja['idtipocaja'] . "'
                LIMIT 1";
            $this->consulta_sql($sql);
            $tipocaja['idcaja'] = $idcaja;
        }

        $sql = "INSERT INTO movimientosxcajas SET
            idcaja = '" . $tipocaja['idcaja'] . "',
            idusuario = '0',
            idconcepto = '" . $tienda['MP_idconcepto'] . "',
            fecha = '" . $fecha . "',
            total = '" . $total . "',
            detalle = 'Recibo de pago RP" . $this->completar_numero($idventapago, 8) . "',
            tiporelacion = 'clientepago',
            idrelacion = '" . $idventapago . "'";
        $this->consulta_sql($sql);
        */
    }

    public function listarMovimientosCajas($datos) {

        $filtros = array();
        if (is_numeric($datos['idcaja']) && $datos['idcaja'] > 0)
            $filtros[] = "movimientosxcajas.idcaja = '" . $datos['idcaja'] . "'";
        if (is_numeric($datos['idtipocaja']) && $datos['idtipocaja'] > 0)
            $filtros[] = "cajas.idtipocaja = '" . $datos['idtipocaja'] . "'";
        if (is_numeric($datos['idconcepto']) && $datos['idconcepto'] > 0)
            $filtros[] = "movimientosxcajas.idconcepto = '" . $datos['idconcepto'] . "'";
        if ($datos['desde'] != '')
            $filtros[] = "movimientosxcajas.fecha >= '" . $datos['desde'] . "'";
        if ($datos['hasta'] != '')
            $filtros[] = "movimientosxcajas.fecha <= '" . $datos['hasta'] . "'";

        // Si no hay filtros muestro solamente los últimos 10 días
        if (!count($filtros))
            $filtros[] = "movimientosxcajas.fecha >= '" . date('Y-m-d', time() - 86400 * 10) . "'";

        $resultado_sql = $this->consulta_sql(
                "SELECT movimientosxcajas.*,
                usuarios.nombre AS usuario,
                categorias_conceptos.padres, categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto,
                categorias_cajas.nombre AS caja
            FROM movimientosxcajas
                LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
                LEFT JOIN usuarios ON movimientosxcajas.idusuario = usuarios.idusuario
                LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
                LEFT JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
            WHERE " . implode(' AND ', $filtros) . "
            ORDER BY fecha");

        $movimientos = array();
        while ($movimiento = $this->array_sql($resultado_sql)) {

            switch ($movimiento['tiporelacion']) {
                case 'clientepago':
                    $cliente = $this->array_sql($this->consulta_sql(
                                    "SELECT ventasxclientes.numero,
                            clientes.nombre, clientes.cuit, clientes.dni, clientes.razonsocial
                        FROM ventasxclientes
                            LEFT JOIN clientes ON ventasxclientes.idcliente = clientes.idcliente
                        WHERE idtipoventa = 0
                            AND ventasxclientes.id = " . $movimiento['idrelacion'] . "
                        LIMIT 1"));

                    $movimiento['numerocompleto'] = $movimiento['venta'] = $cliente['numero'];
                    $movimiento['documento'] = $cliente['cuit'] ? $cliente['cuit'] = $cliente['cuit'][0] . $cliente['cuit'][1] . '-' . $cliente['cuit'][2] . $cliente['cuit'][3] . $cliente['cuit'][4] . $cliente['cuit'][5] . $cliente['cuit'][6] . $cliente['cuit'][7] . $cliente['cuit'][8] . $cliente['cuit'][9] . '-' . $cliente['cuit'][10] : ($cliente['dni'] ? $cliente['dni'] : '');
                    $movimiento['razonsocial'] = $cliente['razonsocial'];
                    $movimiento['cliente'] = $cliente['nombre'];
                    $movimiento['compra'] = '';
                    $movimiento['proveedor'] = '';
                    break;

                case 'proveedorpago':
                    $proveedor = $this->array_sql($this->consulta_sql(
                                    "SELECT comprasxproveedores.numero,
                            proveedores.nombre, proveedores.cuit, proveedores.razonsocial
                        FROM comprasxproveedores
                            LEFT JOIN proveedores ON comprasxproveedores.idproveedor = proveedores.idproveedor
                        WHERE idtipocompra = 0
                            AND comprasxproveedores.id = " . $movimiento['idrelacion'] . "
                        LIMIT 1"));

                    $movimiento['numerocompleto'] = $movimiento['compra'] = $proveedor['numero'];
                    $movimiento['documento'] = $proveedor['cuit'] ? $proveedor['cuit'][0] . $proveedor['cuit'][1] . '-' . $proveedor['cuit'][2] . $proveedor['cuit'][3] . $proveedor['cuit'][4] . $proveedor['cuit'][5] . $proveedor['cuit'][6] . $proveedor['cuit'][7] . $proveedor['cuit'][8] . $proveedor['cuit'][9] . '-' . $proveedor['cuit'][10] : '';
                    $movimiento['razonsocial'] = $proveedor['razonsocial'];
                    $movimiento['proveedor'] = $proveedor['nombre'];
                    $movimiento['venta'] = '';
                    $movimiento['cliente'] = '';
                    break;

                default:
                    $movimiento['numerocompleto'] = '';
                    $movimiento['venta'] = '';
                    $movimiento['cliente'] = '';
                    $movimiento['compra'] = '';
                    $movimiento['proveedor'] = '';
                    $movimiento['documento'] = '';
                    $movimiento['razonsocial'] = '';
                    break;
            }


            $movimientos[] = $movimiento;
        }
        return $movimientos;
    }

    public function listarLibroIvaVentas($datos) {
        $filtros = array();
        if (isset($datos['periodo']))
            $filtros['fecha'] = explode('-', $datos['periodo']);

        $sql = "SELECT ventas.*,
            clientes.idcliente, clientes.cuit, clientes.razonsocial, clientes.dni,
            tablas_provincias.nombre AS provincia,
            tablas_condiciones.nombre_corto AS condicion,
            categorias_ventas.letra, categorias_ventas.puntodeventa, categorias_ventas.operacioninversa, categorias_ventas.nombre AS tipoventa,
            tablas_comportamientos.idcomportamiento, tablas_comportamientos.nombre_corto AS comportamiento
        FROM ventas
            LEFT JOIN clientes ON ventas.idcliente = clientes.idcliente
            LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
            LEFT JOIN tablas_provincias ON categorias_localidades.idprovincia = tablas_provincias.idprovincia
            LEFT JOIN tablas_condiciones ON clientes.idtipoiva = tablas_condiciones.idtipoiva
            LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
            LEFT JOIN tablas_comportamientos ON categorias_ventas.idcomportamiento = tablas_comportamientos.idcomportamiento
        WHERE (ventas.estado = 'cerrado' || ventas.estado = 'anulado')
            AND categorias_ventas.tipofacturacion != 'interno'
            AND ventas.fecha >= '" . $filtros['fecha'][1] . "-" . $filtros['fecha'][0] . "-01 00:00:00'
            AND ventas.fecha < '" . $filtros['fecha'][1] . "-" . ($filtros['fecha'][0] + 1) . "-01 00:00:00'
        ORDER BY ventas.fecha";

        $resultado_sql = $this->consulta_sql($sql);
        $resultado = array();
        while ($venta = $this->array_sql($resultado_sql)) {

            $signo = $venta['operacioninversa'] ? -1 : 1;
            $row = array(
                'fecha' => $venta['fecha'],
                'tipoventa' => $venta['tipoventa'],
                'comportamiento' => $venta['comportamiento'],
                'letra' => $venta['letra'],
                'codigo' => $venta['idcomportamiento'],
                'puntodeventa' => $venta['puntodeventa'],
                'numero' => $venta['numero'],
                'numerocompleto' => $venta['letra'] . completar_numero($venta['puntodeventa'], 5) . '-' . completar_numero($venta['numero'], 8),
                'razonsocial' => $venta['razonsocial'],
                'documento' => $venta['cuit'] ? $venta['cuit'] = $venta['cuit'][0] . $venta['cuit'][1] . '-' . $venta['cuit'][2] . $venta['cuit'][3] . $venta['cuit'][4] . $venta['cuit'][5] . $venta['cuit'][6] . $venta['cuit'][7] . $venta['cuit'][8] . $venta['cuit'][9] . '-' . $venta['cuit'][10] : ($venta['dni'] ? $venta['dni'] : ''),
                'condicion' => $venta['condicion'],
                'provincia' => $venta['provincia'],
                'estado' => $venta['estado'],
                'nogravado' => redondeo($signo * ($venta['nogravado'] + $venta['exento'])),
                'neto' => redondeo($signo * $venta['neto'])
            );
            $row['iva'] = redondeo($signo * $venta['iva']);
            $row['total'] = redondeo($signo * $venta['total']);
            $resultado[] = $row;
        }
        return $resultado;
    }

    public function listarLibroIvaCompras($datos) {
        $filtros = array();
        if (isset($datos['periodo']))
            $filtros['fecha'] = explode('-', $datos['periodo']);

        $sql = "SELECT compras.*,
                proveedores.idproveedor, proveedores.cuit, proveedores.razonsocial,
                tablas_condiciones.nombre_corto AS condicion
            FROM compras
                LEFT JOIN proveedores ON compras.idproveedor=proveedores.idproveedor
                LEFT JOIN tablas_condiciones ON proveedores.idtipoiva=tablas_condiciones.idtipoiva
            WHERE compras.estado='cerrado'
                AND esfiscal = '1'
                AND compras.fecha >= '" . $filtros['fecha'][1] . "-" . $filtros['fecha'][0] . "-01 00:00:00'
                AND compras.fecha < '" . $filtros['fecha'][1] . "-" . ($filtros['fecha'][0] + 1) . "-01 00:00:00'
            ORDER BY compras.fecha";

        $resultado_sql = $this->consulta_sql($sql);
        $resultado = array();
        while ($compra = $this->array_sql($resultado_sql)) {

            switch ($compra['idtipocompra']) {
                case '1': $compra['comportamiento'] = 'FC';
                    break; // Factura
                case '2': $compra['comportamiento'] = 'RT';
                    break; // Remito
                case '3': $compra['comportamiento'] = 'NC';
                    break; // Nota de crédito
                case '4': $compra['comportamiento'] = 'ND';
                    break; // Nota de débito
                case '5': $compra['comportamiento'] = 'RD';
                    break; // Remito de devolución
                case '6': $compra['comportamiento'] = 'PT';
                    break; // Presupuesto
                case '7': $compra['comportamiento'] = 'PD';
                    break; // Pedido
            }

            $signo = $compra['operacioninversa'] ? -1 : 1;
            $row = array(
                'fecha' => $compra['fecha'],
                'comportamiento' => $compra['comportamiento'],
                'numero' => $compra['numero'],
                'razonsocial' => $compra['razonsocial'],
                'documento' => $compra['cuit'] = $compra['cuit'][0] . $compra['cuit'][1] . '-' . $compra['cuit'][2] . $compra['cuit'][3] . $compra['cuit'][4] . $compra['cuit'][5] . $compra['cuit'][6] . $compra['cuit'][7] . $compra['cuit'][8] . $compra['cuit'][9] . '-' . $compra['cuit'][10],
                'condicion' => $compra['condicion'],
                'provincia' => $compra['provincia'],
                'estado' => $compra['estado'],
                'nogravado' => redondeo($signo * ($compra['nogravado'] + $compra['exento'])),
                'neto' => redondeo($signo * $compra['neto'])
            );
                $row['iva'] = redondeo($signo * $compra['iva']);
                $row['tributos'] = redondeo($signo * $compra['tributos']);
                $row['total'] = redondeo($signo * $compra['total']);
            $resultado[] = $row;
        }
        return $resultado;
    }

    public function forzarMail($datos) {
        $mail = $datos['mail'];
        $idcliente = $datos['id_cliente'];

        if ($idcliente == 1) {
            return ['No se puede modificar el cliente Consumidor final'];
        }

        $sql = "UPDATE clientes SET mail = '$mail' WHERE idcliente = '$idcliente' LIMIT 1";
        $resultado_sql = $this->consulta_sql($sql);
        if ($resultado_sql) {
            return true;
        } else {
            return false;
        }
    }

    //Copiado de API v0.1 ML
    public function mover_stock_combo($id, $cantidad, $motivo, $iddeposito = 1)
    {
        $sql = "SELECT * FROM productosxcombos WHERE idcombo = '".$id."'";
        $resultado_sql = $this->consulta_sql($sql);

        while ($combo = $this->array_sql($resultado_sql)) {
            $resto = $cantidad * $combo['cantidad'];
            if ($this->contar_sql($this->consulta_sql("SELECT controlarstock FROM productos WHERE idproducto = '".$combo['idproducto']."' LIMIT 1"))) {
                $this->consulta_sql("UPDATE stock SET stockactual = stockactual - '".$resto."' WHERE idproducto = '".$combo['idproducto']."' AND iddeposito = '$iddeposito' LIMIT 1");
                $this->log_productos($combo['idproducto'], 'en-'.$motivo, $id);
            }
        }
    }

    public function log_productos($idproducto, $motivo, $idventa)
    {
        $datos = $this->armar_sqls_sucursales();
        $query =  "SELECT ".$datos['precios']." ".$datos['stock']." productos.costo
            FROM productos
            ".$datos['joinlistas']."
            ".$datos['joinstock']."
            WHERE productos.idproducto = '".$idproducto."'
            LIMIT 1";

        $sql_log_productos = $this->array_sql($this->consulta_sql($query));

        $this->puntero_sql($datos['listas']);
        while ($temp_array = $this->array_sql($datos['listas'])) {
            if ($this->contar_sql($datos['listas']) == 1) {
                $sql_log_productos['precio'] = $sql_log_productos['precio_'.$temp_array['idlista']];
                $sql_log_productos['preciofinal'] = $sql_log_productos['preciofinal_'.$temp_array['idlista']];
            } else {
                $sql_log_productos['precio'] .= $temp_array['idlista'].':'.$sql_log_productos['precio_'.$temp_array['idlista']].'|';
                $sql_log_productos['preciofinal'] .= $temp_array['idlista'].':'.$sql_log_productos['preciofinal_'.$temp_array['idlista']].'|';
            }
        }

        $this->puntero_sql($datos['depositos']);
        while ($temp_array = $this->array_sql($datos['depositos'])) {
            if($this->contar_sql($datos['depositos']) == 1) {
                $sql_log_productos['stockactual'] = $sql_log_productos['stockactual_'.$temp_array['iddeposito']];
            } else {
                $sql_log_productos['stockactual'] .= $temp_array['iddeposito'].':'.$sql_log_productos['stockactual_'.$temp_array['iddeposito']].'|';
            }
        }

        $this->consulta_sql("INSERT INTO historial SET
            fechayhora = '".date("Y-m-d H:i:s")."',
            idproducto = '".$idproducto."',
            usuario_nombre = 'Sin especificar',
            stockactual = '".$sql_log_productos['stockactual']."',
            costo = '".$sql_log_productos['costo']."',
            precio = '".$sql_log_productos['precio']."',
            preciofinal = '".$sql_log_productos['preciofinal']."',
            motivo = '".$motivo."',
            idrelacion = '".$idventa."'
        ");

        $this->consulta_sql("UPDATE productos SET updated_at = NOW() WHERE idproducto = '".$idproducto."'");

    }

    //copiado de funciones_modelo
    public function actualizar_saldo($tiporelacion, $idrelacion)
    {
        if (!$idrelacion){
            $this->mostrar_error('Se llamó a actualizar saldo en api sin $idrelacion');
        } else {
            switch ($tiporelacion) {
                case 'ventas':
                    //preparo datos
                    $tiporelacion_singular = substr($tiporelacion, 0, -1);
                    $sql_saldo = $this->array_sql($this->consulta_sql("SELECT ".$tiporelacion.".total, ".$tiporelacion.".operacioninversa, ".$tiporelacion.".estado,
                                (SELECT COALESCE(SUM(".$tiporelacion."pagos.total), 0) FROM ".$tiporelacion."pagos WHERE ".$tiporelacion."pagos.id".$tiporelacion_singular." = ".$idrelacion.") AS total_pagos
                                FROM ".$tiporelacion."
                                WHERE id".$tiporelacion_singular." = '".$idrelacion."'
                            "));
                    $saldo_actual = 0;
                    if ($sql_saldo['estado'] == 'cerrado' || $sql_saldo['estado'] == 'anulado')
                        $saldo_actual+= $sql_saldo['total'];
                    if ($sql_saldo['total_pagos'])
                        $saldo_actual-= $sql_saldo['total_pagos'];
                    if ($sql_saldo['operacioninversa'])
                        $saldo_actual = -$saldo_actual;
                    break;

                case 'clientes':
                    $tiporelacion_singular = substr($tiporelacion, 0, -1);
                    $tabla = 'ventas';
                    $tabla_singular = substr($tabla, 0, -1);
                    $sql_saldo = $this->array_sql($this->consulta_sql("SELECT
                                    (SELECT COALESCE(SUM(".$tabla."x".$tiporelacion.".total), 0) FROM ".$tabla."x".$tiporelacion." WHERE id".$tiporelacion_singular." = '".$idrelacion."' AND idtipo".$tabla_singular." > '0') AS facturado,
                                    (SELECT COALESCE(SUM(".$tabla."x".$tiporelacion.".total), 0) FROM ".$tabla."x".$tiporelacion." WHERE id".$tiporelacion_singular." = '".$idrelacion."' AND idtipo".$tabla_singular." <= '0') AS pagado
                                "));
                    $saldo_actual = $sql_saldo['facturado'] - $sql_saldo['pagado'];
                    break;

                default:
                    $this->mostrar_error('Se llamó a actualizar saldo en api sin $tiporelacion');
                    break;
            }

            //insert y update saldos
            $this->consulta_sql("INSERT INTO saldos
                (tiporelacion, idrelacion, saldo)
                VALUES
                ('".$tiporelacion."', '".$idrelacion."', '".$saldo_actual."')
                ON DUPLICATE KEY UPDATE saldo = '".$saldo_actual."'
                ");

            // Log para encontrar errores
            global $iue;
            global $url;
            $linea = date("Y-m-d H:i").";".$tiporelacion.";".$idrelacion.";".$saldo_actual.";".$url;
            $linea .= "\r\n";
            file_put_contents(PATH_LOGS.'saldos_'.$iue.'.csv', $linea, FILE_APPEND)
                or $this->mostrar_error('Error abriento el archivo de LOG de saldos Nº '.$iue, true);
        }
    }

    //copiado de funciones_productos.php
    private function stockdinamico($id, $iddeposito)
    {
        //No me funcó con campo_sql
        $resultado = $this->array_sql($this->consulta_sql(
            "SELECT (s.stockactual / pxc.cantidad) AS stockdinamico
            FROM productosxcombos AS pxc
            LEFT JOIN productos AS p ON pxc.idproducto = p.idproducto
            LEFT JOIN stock AS s ON p.idproducto = s.idproducto AND s.iddeposito = '$iddeposito'
            WHERE idcombo = '$id'
            AND p.controlarstock = 1 AND p.stocknegativo = 0
            ORDER BY stockdinamico ASC
            LIMIT 1
            "));
        $stockdinamico = $resultado['stockdinamico'];

        // Si el stock es cero (en string o int) o es un número negativo, fuerzo que hay 0 stock dinámico
        if (is_numeric($stockdinamico) && (float)$stockdinamico <= 0)
            $stockdinamico = 0;

        // Si los productos del combo no controlan stock o tienen stocknegativo devuelvo un false porque si se puede vender el combo pero tengo infinito stock dinámico
        return (is_numeric($stockdinamico))
            ? redondeo($stockdinamico)
            : false;
    }

    //Recontra copiado de APP y V0.1
    private function armar_sqls_sucursales()
    {
        $sucursales = array();
        $listas = $this->consulta_sql("SELECT * FROM listas ORDER BY idlista");
        $depositos = $this->consulta_sql("SELECT * FROM depositos ORDER BY iddeposito");

        $precios = $joinlistas = $stock = $joinstock = "";
        while($temp_array = $this->array_sql($listas)){
        $precios .= ' COALESCE(precios'.$temp_array['idlista'].'.utilidad'.',0) AS utilidad_'.$temp_array['idlista'].',
                        COALESCE(precios'.$temp_array['idlista'].'.precio'.',0) AS precio_'.$temp_array['idlista'].',
                        COALESCE(precios'.$temp_array['idlista'].'.preciofinal'.',0) AS preciofinal_'.$temp_array['idlista'].',
        ';
        $joinlistas .= ' LEFT JOIN precios AS precios'.$temp_array['idlista'].'
            ON productos.idproducto = precios'.$temp_array['idlista'].'.idproducto
            AND precios'.$temp_array['idlista'].'.idlista =  '.$temp_array['idlista'];
        }
        while ($temp_array = $this->array_sql($depositos)) {
            $stock .= ' COALESCE(stock'.$temp_array['iddeposito'].'.stockactual'.',0) AS stockactual_'.$temp_array['iddeposito'].',
                COALESCE(stock'.$temp_array['iddeposito'].'.stockminimo'.',0) AS stockminimo_'.$temp_array['iddeposito'].',
                COALESCE(stock'.$temp_array['iddeposito'].'.stockideal'.',0) AS stockideal_'.$temp_array['iddeposito'].',
                ';
            $joinstock .= ' LEFT JOIN stock AS stock'.$temp_array['iddeposito'].'
                ON productos.idproducto = stock'.$temp_array['iddeposito'].'.idproducto
                AND stock'.$temp_array['iddeposito'].'.iddeposito =  '.$temp_array['iddeposito'];
        }

        $sucursales['listas']       = $listas;
        $sucursales['depositos']    = $depositos;
        $sucursales['precios']      = $precios;
        $sucursales['stock']        = $stock;
        $sucursales['joinlistas']   = $joinlistas;
        $sucursales['joinstock']    = $joinstock;

        return($sucursales);
    }

    private function obtenerIdLocalidad($cliente)
    {
        //Copiado de v0.1 obtenerLocalidad()
        $sql = "SELECT idlocalidad
            FROM categorias_localidades
            WHERE codigopostal = '".$cliente['codigopostal']."'
        ";

        $resultado_sql = $this->consulta_sql($sql);

        if ($this->contar_sql($resultado_sql)) {
            $idlocalidad = $this->campo_sql($resultado_sql, 0, 'idlocalidad');
        } else {
            $cliente['provincia'] = str_replace('Provincia de ', '', $cliente['provincia']);
            if (in_array($cliente['provincia'], ['Capital Federal', 'CABA', 'Ciudad Aut\u00f3noma de Buenos Aires', 'Ciudad Autónoma de Buenos Aires']) || strpos($cliente['provincia'], 'de Buenos Aires') !== false) {
                $idprovincia = '25'; //CABA
            } else {
                $resultado_sql = $this->consulta_sql(
                    "SELECT idprovincia
                    FROM tablas_provincias
                    WHERE nombre LIKE '%{$cliente["provincia"]}%'
                    LIMIT 1");
                if ($this->contar_sql($resultado_sql)) {
                    $idprovincia = $this->campo_sql($resultado_sql, 0, 'idprovincia');
                } else {
                    // No debería entrar nunca a este else
                    $idprovincia = 0;
                    $this->mostrar_error('Provincia no encontrada en API para Tienda con cliente '.json_encode($cliente), true);
                }

            }
            $sql = "INSERT INTO categorias_localidades SET
                estado = 1,
                nombre = '".$this->escape_sql($cliente['localidad'])."',
                idprovincia = '".$idprovincia."',
                codigopostal = '".$cliente['codigopostal']."'
            ";
            $this->consulta_sql($sql);
            $idlocalidad = $this->id_sql();
        }

        return $idlocalidad;

    }

    private function obtenerCliente($idcliente)
    {
        $cliente = $this->array_sql($this->consulta_sql(
            "SELECT cuit, dni, clientes.nombre, mail, idtipoiva, idlocalidad, domicilio,
                categorias_clientes.idlista, categorias_clientes.descuento, categorias_clientes.pagacosto
            FROM clientes
                JOIN categorias_clientes ON categorias_clientes.idtipocliente = clientes.idtipocliente
            WHERE idcliente = '$idcliente'"));

        // Revisamos que tenga idlista
        if (!$cliente['idlista']) {
            $cliente['idlista'] = 1;
            $this->mostrar_error('No se encontró idlista en crearVentaDesdeCarrito con la idempresa '.$this->idempresa.' y el cliente '.json_encode($cliente), true);
        }

        return $cliente;
    }

    private function obtenerIva($idiva)
    {
        return $this->array_sql($this->consulta_sql("SELECT * FROM tablas_ivas WHERE idiva = '$idiva'"));
    }

    private function obtenerImagenes($idProductoList)
    {
        if (!is_array($idProductoList) || !count($idProductoList))
            return [];

        $imagenes = [];
        $resultado_sql = $this->consulta_sql("SELECT id, iua, publico, archivo FROM archivos WHERE modulo = 'productos' AND id IN (".implode(',', $idProductoList).") AND tipo = 'imagen' AND publico = 1 GROUP BY id");
        while ($imagen = $this->array_sql($resultado_sql)) {
            $imagenes[$imagen['id']] = $imagen;
        }

        return $imagenes;
    }

    /***
    **** Clientes:
    ***/
    public function contarClientes($datos) {
        $sql = "SELECT COUNT(*) AS total FROM clientes c";

        if ($datos['mostrarestado'])
            $sql.= " WHERE c.estado IN ('0', '1')";
        else
            $sql.= " WHERE c.estado = '1'";

        if ($datos['idtipocliente']) {
            $sql.= " AND c.idtipocliente = '" . ((int) $datos['idtipocliente']) . "'";
        }

        $resultado_sql = $this->consulta_sql($sql);
        $total = $this->array_sql($resultado_sql);
        return $total['total'];
    }

    public function listarClientes($datos, $idcliente = false) {

        $idClienteList = [];
        //$tienda = $this->obtenerTienda();

        $sql = "SELECT c.idcliente, c.nombre, c.razonsocial, c.cuit, c.dni, c.contacto, c.telefonos,
                    c.domicilio, c.mail, c.observacion, c.obsinterna,
                    categorias_clientes.nombre AS categoria,
                    categorias_localidades.nombre AS localidad, categorias_localidades.codigopostal,
                    tablas_condiciones.nombre AS tipoiva,
                    tablas_provincias.nombre AS provincia,
                    listas.idlista, listas.nombre AS lista,
                    saldos.saldo
                ";

        if ($datos['mostrarestado'])
            $sql.= ", c.estado";

        if ($datos['mostrarmoneda'])
            $sql.= ", monedas.nombre AS moneda, monedas.simbolo";

        $filtrosextras = '';
        if ($datos['datosextras']) {
            $resultado_sql = $this->consulta_sql("SELECT idextraxmodulo, tipo, nombre FROM extrasxmodulos WHERE modulo = 'clientes'");
            while ($extra = $this->array_sql($resultado_sql)) {
                if ($extra['tipo'] == 'texto') {
                    $sql.= ", IFNULL((SELECT texto FROM datosxextras WHERE idextraxmodulo = '" . $extra['idextraxmodulo'] . "'
                                AND datosxextras.idrelacion = c.idcliente LIMIT 1),'') AS '" . $extra['nombre'] . "'";

                } else {
                    $sql.= ", (SELECT nombre FROM listasxextras WHERE idlistaxextra = (
                                SELECT idlistaxextra FROM datosxextras WHERE idextraxmodulo = '" . $extra['idextraxmodulo'] . "'
                                AND idrelacion = c.idcliente LIMIT 1) LIMIT 1) AS '" . $extra['nombre'] . "'";
                }
            }
        }

        $sql .= " FROM clientes c
                    LEFT JOIN categorias_localidades ON c.idlocalidad = categorias_localidades.idlocalidad
                    LEFT JOIN tablas_condiciones ON c.idtipoiva = tablas_condiciones.idtipoiva
                    LEFT JOIN categorias_clientes ON c.idtipocliente = categorias_clientes.idtipocliente
                    LEFT JOIN listas ON categorias_clientes.idlista = listas.idlista
                    LEFT JOIN saldos ON saldos.tiporelacion = 'clientes' AND c.idcliente = saldos.idrelacion
                    LEFT JOIN tablas_provincias ON categorias_localidades.idprovincia = tablas_provincias.idprovincia ";

        if ($datos['mostrarmoneda'])
            $sql .= " LEFT JOIN monedas ON c.idmoneda = monedas.idmoneda ";

        if ($datos['mostrarestado'])
            $sql.= " WHERE c.estado IN ('0', '1')"; //para zafar de los if posteriores
        else
            $sql.= " WHERE c.estado = '1'";

        if ($idcliente) {
            $sql.= " AND c.idcliente = '" . ((int) $idcliente) . "'";
        }

        if ($datos['idtipocliente']) {
            $sql.= " AND c.idtipocliente = '" . ((int) $datos['idtipocliente']) . "'";
        }

        if ($datos['idlista']) {
            $sql.= " AND listas.idlista = '" . ((int) $datos['idlista']) . "'";
        }

        if (strlen($datos['busqueda']) > 3 && !$idcliente) {
            $datos['busqueda'] = str_replace(' ', '%', $datos['busqueda']);
            $sql.= " AND (c.nombre LIKE '%"
                    . $datos['busqueda'] . "%'"
                    . " OR c.razonsocial LIKE '%"
                    . $datos['busqueda'] . "%')";
        }
        if ($datos['modificados']) {
            $sql.= " AND updated_at > '"
                    . date("Y-m-d H:i", time() - $datos['modificados'] * 3600) . "'";
        }
        $sql.= $filtrosextras;

        switch ($datos['orden']) {
            default:
            case 'ultimos':
                $sql.= " ORDER BY c.idcliente DESC";
                break;

            case 'nombre':
                $sql.= ' ORDER BY c.nombre ASC';
                break;

            case 'razonsocial':
                $sql.= ' ORDER BY c.razonsocial ASC';
                break;
        }

        if ($datos['cantidad'] != 'completo' && !$idcliente) {
            $sql.= " LIMIT " . ((int) $datos['cantidad']) . " OFFSET " . ((int) $datos['desde']);
        }

        $resultado_sql = $this->consulta_sql($sql);

        $clientes = array();

        while ($cliente = $this->array_sql($resultado_sql)) {
            $idClienteList[] = $cliente['idcliente'];
            $clientes[] = $cliente;
        }

        return $clientes;
    }

    public function verCliente($datos) {
        $cliente = $this->listarClientes($datos, $datos['idcliente']);
        return $cliente;
    }

    public function agregarCliente($campos) {
        $tienda = $this->obtenerTienda();
        $valores = "";
        $definicion = "";
        $camposObligatoriosExistentes = Array();
        $i = 0;
        foreach ($campos as $campo) {
            $valores .= ($valores != '') ? ", " : "";
            $definicion .= ($valores != '') ? ", " : "";
            if ($campo->campo === 'idtipocliente') {
                $idtipoclienteExists = true;
            }

            if ($campo->campo == 'mail' && !filter_var($campo->valor, FILTER_VALIDATE_EMAIL)) {
                return ['El mail no es válido: '. $campo->valor];
            }

            if ($campo->campo == 'dni' || $campo->campo == 'cuit') {
                $dato_consultar_sql = $campo->campo == 'cuit'
                    ? "cuit = '".$campo->valor."'"
                    : "dni = '".$campo->valor."'";

                $existe_sql = $this->consulta_sql(
                    "SELECT idcliente, nombre
                        FROM clientes
                        WHERE ".$dato_consultar_sql);

                if ($this->contar_sql($existe_sql)) {
                    $existe_cliente = $this->array_sql($existe_sql);
                    return ['El CUIT/DNI ya se encuentra cargado en el cliente: '. $existe_cliente['nombre']];
                }
                //TODO si ponemos razonsocial obligatorio y listo?
                /*if ($datos['cuit'] && !$datos['razonsocial']){
                    mensajes_alta($i18n[323]);
                    return ['response' => 'El cliente no tiene cargada una razón social'];
                }*/
            }
            switch ($campo->tipoDato) {
                case "string":
                    if (in_array($campo->campo, $this->camposObligatoriosClientes) && !in_array($campo->campo, $camposObligatoriosExistentes)) {
                        $camposObligatoriosExistentes[$i] = $campo->campo;
                        $i++;
                    }
                    $valores .= "'" . $campo->valor . "'";
                    $definicion .= $campo->campo;
                    break;

                case "entero":
                    if (in_array($campo->campo, $this->camposObligatoriosClientes) && !in_array($campo->campo, $camposObligatoriosExistentes)) {
                        $camposObligatoriosExistentes[$i] = $campo->campo;
                        $i++;
                    }
                    $valores .= $campo->valor;
                    $definicion .= $campo->campo;
                    break;
            }
        }

        if (!$idtipoclienteExists) {
            $valores .= ($valores != '') ? ", " : "";
            $definicion .= ($definicion != '') ? ", " : "";

            $valores .= $tienda['tienda_idtipocliente'];
            $definicion .= 'idtipocliente';
        }

        $query = "INSERT INTO clientes (" . $definicion . ") VALUES (" . $valores . ")";
        if (count($this->camposObligatoriosClientes) == count($camposObligatoriosExistentes)) {
            $this->consulta_sql($query);
            $idcliente = $this->id_sql();
            $this->insertar_saldo('clientes', $idcliente);
        } else {
            $camposFaltantes = '';
            foreach ($this->camposObligatoriosClientes as $capoObligatorio) {
                if (!in_array($capoObligatorio, $camposObligatoriosExistentes)) {
                    $camposFaltantes .= ($camposFaltantes != '' ? ',' : '');
                    $camposFaltantes .= $capoObligatorio;
                }
            }
            throw new Exception("No se inserto el cliente ya que faltan campos obligatorios: " . $camposFaltantes);
        }

        $datos = [];
        $datos['idcliente'] = $idcliente;
        $datos['mostrarestado'] = 1;

        return $this->verCliente($datos);
    }

    public function modificarCliente($campos, $idcliente) {
        if ($idcliente == 1) {
            return ['No se puede modificar el cliente Consumidor final'];
        }
        $query = "UPDATE clientes SET ";
        $valores = "";

        foreach ($campos as $campo) {
            $valores .= ($valores != '') ? ", " : "";

            if ($campo->campo == 'mail' && !filter_var($campo->valor, FILTER_VALIDATE_EMAIL)) {
                return ['El mail no es válido: '. $campo->valor];
            }

            if ($campo->campo == 'dni' || $campo->campo == 'cuit') {
                $dato_consultar_sql = $campo->campo == 'cuit'
                    ? "cuit = '".$campo->valor."'"
                    : "dni = '".$campo->valor."'";

                $existe_sql = $this->consulta_sql(
                    "SELECT idcliente, nombre
                        FROM clientes
                        WHERE ".$dato_consultar_sql."
                        AND idcliente != '$idcliente'
                        LIMIT 1"
                    );

                if ($this->contar_sql($existe_sql)) {
                    $existe_cliente = $this->array_sql($existe_sql);
                    return ['El CUIT/DNI ya se encuentra cargado en el cliente: '. $existe_cliente['nombre']];
                }
                //TODO si ponemos razonsocial obligatorio y listo?
                /*if ($datos['cuit'] && !$datos['razonsocial']){
                    mensajes_alta($i18n[323]);
                    return ['response' => 'El cliente no tiene cargada una razón social'];
                }*/
            }

            switch ($campo->{'tipoDato'}) {
                case "string":
                    $valores .= $campo->campo . " = " . "'" . $campo->valor . "'";
                    break;

                case "entero":
                    $valores .= $campo->campo . " = " . $campo->valor;
                    break;
            }
        }

        if (!$valores)
            return 'No se modificó ningún campo';

        $query .= $valores . " WHERE idcliente = " . $idcliente;
        $resultado_sql = $this->consulta_sql($query);

        if ($this->afectado_sql($resultado_sql)) {
            $datos = [];
            $datos['idcliente'] = $idcliente;
            $datos['mostrarestado'] = 1;

            return $this->verCliente($datos);
        } else {
            return 'Error en la modificación de cliente: '.$idcliente;
        }
    }

    public function eliminarCliente($idcliente) {
        $response = '';
        if ($this->contar_sql($this->consulta_sql("SELECT idventa FROM ventas WHERE idcliente = '".$idcliente."' LIMIT 1"))) {
            $this->consulta_sql("UPDATE clientes SET estado = '0' WHERE idcliente = '".$idcliente."' LIMIT 1");
            $response = 'No se puede eliminar clientes con ventas. Se deshabilitó el cliente: ';
        } elseif ($this->contar_sql($this->consulta_sql("SELECT idventapago FROM ventaspagos WHERE idcliente = '".$idcliente."' LIMIT 1"))) {
            $this->consulta_sql("UPDATE clientes SET estado = '0' WHERE idcliente = '".$idcliente."' LIMIT 1");
            $response = 'No se puede eliminar clientes con pagos. Se deshabilitó el cliente: ';
        } elseif ($this->contar_sql($this->consulta_sql("SELECT idservicio FROM servicios WHERE idcliente = '".$idcliente."' LIMIT 1"))) {
            $this->consulta_sql("UPDATE clientes SET estado = '0' WHERE idcliente = '".$idcliente."' LIMIT 1");
            $response = 'No se puede eliminar clientes con servicios. Se deshabilitó el cliente: ';
        } elseif ($this->contar_sql($this->consulta_sql("SELECT idbien FROM bienes WHERE idcliente = '".$idcliente."' LIMIT 1"))) {
            $this->consulta_sql("UPDATE clientes SET estado = '0' WHERE idcliente = '".$idcliente."' LIMIT 1");
            $response = 'No se puede eliminar clientes con bienes. Se deshabilitó el cliente: ';
        } elseif ($this->existe_archivo('clientes', $idcliente)) {
            $this->consulta_sql("UPDATE clientes SET estado='0' WHERE idcliente = '".$idcliente."' LIMIT 1");
            $response = 'No pudo eliminar lo solicitado porque no se pudo eliminar los archivos relacionados. Se deshabilitó el cliente: ';
        } else {
            $resultado_sql = $this->consulta_sql("DELETE FROM clientes WHERE idcliente = '".$idcliente."' LIMIT 1");
            if ($this->afectado_sql($resultado_sql)) {
                $this->borrar_saldo('clientes', $idcliente);
                $response = 'Baja exitosa del cliente:';
            } else {
                $response = 'Error en la baja cliente: ';
            }
        }

        return [$response.$idcliente];
    }

    /***
    **** Servicios:
    ***/

    public function contarServicios($datos) {

        $sql = "SELECT COUNT(*) AS total FROM servicios s";

        if ($datos['estado']) {
            $sql.= " WHERE s.estado = '" . ((int) $datos['estado']) . "'";
        } else {
            $sql.= " WHERE s.estado > 0";
        }

        if ($datos['idtiposervicio']) {
            $sql.= " AND s.idtiposervicio = '" . ((int) $datos['idtiposervicio']) . "'";
        }

        if ($datos['idcliente']) {
            $sql.= " AND s.idcliente = '" . ((int) $datos['idcliente']) . "'";
        }

        if ($datos['fechasolicitado'] && strtotime($datos['fechasolicitado']) !== false) {
            $sql .= " AND s.fechasolicitado = '" . $this->escape_sql($datos['fechasolicitado']) . "'";
        }
        if ($datos['fechasolicitado_desde'] && strtotime($datos['fechasolicitado_desde']) !== false) {
            $sql .= " AND s.fechasolicitado >= '" . $this->escape_sql($datos['fechasolicitado_desde']) . "'";
        }
        if ($datos['fechasolicitado_hasta'] && strtotime($datos['fechasolicitado_hasta']) !== false) {
            $sql .= " AND s.fechasolicitado <= '" . $this->escape_sql($datos['fechasolicitado_hasta']) . "'";
        }

        if ($datos['fechainicio'] && strtotime($datos['fechainicio']) !== false) {
            $sql .= " AND s.fechainicio = '" . $this->escape_sql($datos['fechainicio']) . "'";
        }
        if ($datos['fechainicio_desde'] && strtotime($datos['fechainicio_desde']) !== false) {
            $sql .= " AND s.fechainicio >= '" . $this->escape_sql($datos['fechainicio_desde']) . "'";
        }
        if ($datos['fechainicio_hasta'] && strtotime($datos['fechainicio_hasta']) !== false) {
            $sql .= " AND s.fechainicio <= '" . $this->escape_sql($datos['fechainicio_hasta']) . "'";
        }

        if ($datos['fechafin'] && strtotime($datos['fechafin']) !== false) {
            $sql .= " AND s.fechafin = '" . $this->escape_sql($datos['fechafin']) . "'";
        }
        if ($datos['fechafin_desde'] && strtotime($datos['fechafin_desde']) !== false) {
            $sql .= " AND s.fechafin >= '" . $this->escape_sql($datos['fechafin_desde']) . "'";
        }
        if ($datos['fechafin_hasta'] && strtotime($datos['fechafin_hasta']) !== false) {
            $sql .= " AND s.fechafin <= '" . $this->escape_sql($datos['fechafin_hasta']) . "'";
        }

        if ($datos['fechalimite'] && strtotime($datos['fechalimite']) !== false) {
            $sql .= " AND s.fechalimite = '" . $this->escape_sql($datos['fechalimite']) . "'";
        }
        if ($datos['fechalimite_desde'] && strtotime($datos['fechalimite_desde']) !== false) {
            $sql .= " AND s.fechalimite >= '" . $this->escape_sql($datos['fechalimite_desde']) . "'";
        }
        if ($datos['fechalimite_hasta'] && strtotime($datos['fechalimite_hasta']) !== false) {
            $sql .= " AND s.fechalimite <= '" . $this->escape_sql($datos['fechalimite_hasta']) . "'";
        }

        $resultado_sql = $this->consulta_sql($sql);
        $total = $this->array_sql($resultado_sql);
        return $total['total'];
    }

    public function listarServicios($datos, $idservicio = false) {

        $idServiciosList = [];

        $sql = "SELECT s.idservicio, s.titulo, s.estado, s.fechainicio, s.prioridad, s.fechafin, s.tiempodedicado,
                    s.obssolicitado, s.obsrealizado,
                        CASE WHEN s.fechasolicitado = '0000-00-00 00:00:00' THEN NULL ELSE s.fechasolicitado END AS fechasolicitado,
                        CASE WHEN s.fechainicio = '0000-00-00 00:00:00' THEN NULL ELSE s.fechainicio END AS fechainicio,
                        CASE WHEN s.fechafin = '0000-00-00 00:00:00' THEN NULL ELSE s.fechafin END AS fechafin,
                        CASE WHEN s.fechalimite = '0000-00-00 00:00:00' THEN NULL ELSE s.fechalimite END AS fechalimite,
                    categorias_servicios.nombre AS tiposervicio,
                    clientes.idcliente, clientes.nombre AS cliente,
                    (SELECT saldo FROM saldos WHERE tiporelacion = 'ventasxservicios' AND idrelacion = '$idservicio') AS saldo_ventas,
                    (SELECT saldo FROM saldos WHERE tiporelacion = 'comprasxservicios' AND idrelacion = '$idservicio') AS saldo_compras
                ";

        if ($datos['mostrarmoneda'])
            $sql.= ", monedas.nombre AS moneda, monedas.simbolo";

        $filtrosextras = '';
        if ($datos['datosextras']) {
            $resultado_sql = $this->consulta_sql("SELECT idextraxmodulo, tipo, nombre FROM extrasxmodulos WHERE modulo = 'servicios'");
            while ($extra = $this->array_sql($resultado_sql)) {
                if ($extra['tipo'] == 'texto') {
                    $sql.= ", IFNULL((SELECT texto FROM datosxextras WHERE idextraxmodulo = '" . $extra['idextraxmodulo'] . "'
                                AND datosxextras.idrelacion = servicios.idservicio LIMIT 1),'') AS '" . $extra['nombre'] . "'";

                } else {
                    $sql.= ", (SELECT nombre FROM listasxextras WHERE idlistaxextra = (
                                SELECT idlistaxextra FROM datosxextras WHERE idextraxmodulo = '" . $extra['idextraxmodulo'] . "'
                                AND idrelacion = servicios.idservicio LIMIT 1) LIMIT 1) AS '" . $extra['nombre'] . "'";
                }
            }
        }

        $sql .= " FROM servicios s
                    LEFT JOIN categorias_servicios ON s.idtiposervicio = categorias_servicios.idtiposervicio
                    LEFT JOIN clientes ON s.idcliente = clientes.idcliente";

        if ($datos['estado']) {
            $sql.= " WHERE s.estado = '" . ((int) $datos['estado']) . "'";
        } else {
            $sql.= " WHERE s.estado > 0";
        }

        if ($datos['mostrarmoneda'])
            $sql .= " LEFT JOIN monedas ON s.idmoneda = monedas.idmoneda ";


        if ($idservicio) {
            $sql.= " AND s.idservicio = '" . ((int) $idservicio) . "'";
        }

        if ($datos['idcliente']) {
            $sql.= " AND s.idcliente = '" . ((int) $datos['idcliente']) . "'";
        }

        if ($datos['idtiposervicio']) {
            $sql.= " AND s.idtiposervicio = '" . ((int) $datos['idtiposervicio']) . "'";
        }

        if (strlen($datos['busqueda']) > 3 && !$idservicio) {
            $datos['busqueda'] = str_replace(' ', '%', $datos['busqueda']);
            $sql.= " AND (s.titulo LIKE '%"
                    . $datos['busqueda'] . "%')";
        }

        if ($datos['modificados']) {
            $sql.= " AND s.updated_at > '"
                    . date("Y-m-d H:i", time() - $datos['modificados'] * 3600) . "'";
        }

        if ($datos['fechasolicitado'] && strtotime($datos['fechasolicitado']) !== false) {
            $sql .= " AND s.fechasolicitado = '" . $this->escape_sql($datos['fechasolicitado']) . "'";
        }
        if ($datos['fechasolicitado_desde'] && strtotime($datos['fechasolicitado_desde']) !== false) {
            $sql .= " AND s.fechasolicitado >= '" . $this->escape_sql($datos['fechasolicitado_desde']) . "'";
        }
        if ($datos['fechasolicitado_hasta'] && strtotime($datos['fechasolicitado_hasta']) !== false) {
            $sql .= " AND s.fechasolicitado <= '" . $this->escape_sql($datos['fechasolicitado_hasta']) . "'";
        }

        if ($datos['fechainicio'] && strtotime($datos['fechainicio']) !== false) {
            $sql .= " AND s.fechainicio = '" . $this->escape_sql($datos['fechainicio']) . "'";
        }
        if ($datos['fechainicio_desde'] && strtotime($datos['fechainicio_desde']) !== false) {
            $sql .= " AND s.fechainicio >= '" . $this->escape_sql($datos['fechainicio_desde']) . "'";
        }
        if ($datos['fechainicio_hasta'] && strtotime($datos['fechainicio_hasta']) !== false) {
            $sql .= " AND s.fechainicio <= '" . $this->escape_sql($datos['fechainicio_hasta']) . "'";
        }

        if ($datos['fechafin'] && strtotime($datos['fechafin']) !== false) {
            $sql .= " AND s.fechafin = '" . $this->escape_sql($datos['fechafin']) . "'";
        }
        if ($datos['fechafin_desde'] && strtotime($datos['fechafin_desde']) !== false) {
            $sql .= " AND s.fechafin >= '" . $this->escape_sql($datos['fechafin_desde']) . "'";
        }
        if ($datos['fechafin_hasta'] && strtotime($datos['fechafin_hasta']) !== false) {
            $sql .= " AND s.fechafin <= '" . $this->escape_sql($datos['fechafin_hasta']) . "'";
        }

        if ($datos['fechalimite'] && strtotime($datos['fechalimite']) !== false) {
            $sql .= " AND s.fechalimite = '" . $this->escape_sql($datos['fechalimite']) . "'";
        }
        if ($datos['fechalimite_desde'] && strtotime($datos['fechalimite_desde']) !== false) {
            $sql .= " AND s.fechalimite >= '" . $this->escape_sql($datos['fechalimite_desde']) . "'";
        }
        if ($datos['fechalimite_hasta'] && strtotime($datos['fechalimite_hasta']) !== false) {
            $sql .= " AND s.fechalimite <= '" . $this->escape_sql($datos['fechalimite_hasta']) . "'";
        }

        $sql.= $filtrosextras;

        switch ($datos['orden']) {
            default:
            case 'ultimos':
                $sql.= " ORDER BY s.idservicio DESC";
                break;

            case 'titulo':
                $sql.= ' ORDER BY s.titulo ASC';
                break;

        }

        if ($datos['cantidad'] != 'completo' && !$idservicio) {
            $sql.= " LIMIT " . ((int) $datos['cantidad']) . " OFFSET " . ((int) $datos['desde']);
        }

        $resultado_sql = $this->consulta_sql($sql);

        $servicios = array();

        while ($servicio = $this->array_sql($resultado_sql)) {
            $estado = '';
            $idServiciosList[] = $servicio['idservicio'];

            switch ($servicio['estado']) {
                case '1': // Sin comenzar
                    $estado = 'Sin comenzar';
                    break;
                case '2': // Comenzado
                    $estado = 'Comenzado';
                    break;
                case '3': // Terminado
                    $estado = 'Terminado';
                    break;
                case '4': // Pausado
                    $estado = 'Pausado';
                    break;
                case '5': // Anulado
                    $estado =  'Anulado';
                    break;
            }
            $servicio['estado'] = $estado;
            $servicio['saldo'] = $servicio['saldo_ventas'] - $servicio['saldo_compras'];
            $servicios[] = $servicio;
        }

        return $servicios;
    }

    public function verServicio($datos) {
        // Agregar validación de contraseña con el dato pass y el campo pass de la tabla clientes
        if ($datos['pass']) {
            $sql = "SELECT pass FROM clientes WHERE idcliente =
                (SELECT idcliente FROM servicios WHERE idservicio = '" . $datos['idservicio'] . "')";
            $resultado_sql = $this->consulta_sql($sql);
            $pass = $this->campo_sql($resultado_sql, 0, 'pass');
            if ($pass != md5($datos['pass'])) {
                return false;
            }
        }
        $cliente = $this->listarServicios($datos, $datos['idservicio']);
        return $cliente;
    }

    public function agregarServicio($campos) {
        $valores = "";
        $definicion = "";
        $camposObligatoriosExistentes = Array();
        $i = 0;

        foreach ($campos as $campo) {
            $valores .= ($valores != '') ? ", " : "";
            $definicion .= ($valores != '') ? ", " : "";

            /* TODO: validaciones */
            switch ($campo->tipoDato) {
                case "string":
                    /*if (in_array($campo->campo, $this->camposObligatoriosClientes) && !in_array($campo->campo, $camposObligatoriosExistentes)) {
                        $camposObligatoriosExistentes[$i] = $campo->campo;
                        $i++;
                    }*/
                    $valores .= "'" . $campo->valor . "'";
                    $definicion .= $campo->campo;
                    break;

                case "entero":
                    /*if (in_array($campo->campo, $this->camposObligatoriosClientes) && !in_array($campo->campo, $camposObligatoriosExistentes)) {
                        $camposObligatoriosExistentes[$i] = $campo->campo;
                        $i++;
                    }*/
                    $valores .= $campo->valor ? $campo->valor : 0;
                    $definicion .= $campo->campo;
                    break;
            }
        }
        $query = "INSERT INTO servicios (" . $definicion . ") VALUES (" . $valores . ")";
        //if (count($this->camposObligatoriosClientes) == count($camposObligatoriosExistentes)) {
            $this->consulta_sql($query);
            $idservicio = $this->id_sql();
            $this->insertar_saldo('comprasxservicios', $idservicio);
            $this->insertar_saldo('ventasxservicios', $idservicio);
        /*} else {
            $camposFaltantes = '';
            foreach ($this->camposObligatoriosClientes as $capoObligatorio) {
                if (!in_array($capoObligatorio, $camposObligatoriosExistentes)) {
                    $camposFaltantes .= ($camposFaltantes != '' ? ',' : '');
                    $camposFaltantes .= $capoObligatorio;
                }
            }
            throw new Exception("No se inserto el servicio ya que faltan campos obligatorios: " . $camposFaltantes);
        }*/

        $datos = [];
        $datos['idservicio'] = $idservicio;

        return $this->verServicio($datos);
    }

    public function modificarServicio($campos, $idservicio) {
        $query = "UPDATE servicios SET ";
        $valores = "";

        foreach ($campos as $campo) {
            $valores .= ($valores != '') ? ", " : "";

            /* TODO: validaciones */

            switch ($campo->{'tipoDato'}) {
                case "string":
                    $valores .= $campo->campo . " = " . "'" . $campo->valor . "'";
                    break;

                case "entero":
                    $valores .= $campo->campo . " = " . $campo->valor;
                    break;
            }
        }

        $query .= $valores . " WHERE idservicio = " . $idservicio;
        $resultado_sql = $this->consulta_sql($query);

        if ($this->afectado_sql($resultado_sql)) {
            $datos = [];
            $datos['idservicio'] = $idservicio;

            return $this->verServicio($datos);
        } else {
            return 'Error en la modificación de servicio: '.$idservicio;
        }
    }

    public function eliminarServicio($idservicio) {
        $response = '';
        if ($this->contar_sql($this->consulta_sql("SELECT idventa FROM ventas WHERE idrelacion = '".$idservicio."' AND tiporelacion='servicio' LIMIT 1"))) {
            $response = 'No se puede eliminar servicios con ventas relacionados al servicio id: ';
        } elseif ($this->contar_sql($this->consulta_sql("SELECT idbienxservicio FROM bienesxservicios WHERE idservicio = '".$idservicio."' LIMIT 1"))) {
            $response = 'No se puede eliminar servicios con bienes relacionados al servicio id: ';
        } elseif ($this->existe_archivo('servicios', $idservicio)) {
            $response = 'No pudo eliminar lo solicitado porque no se pudo eliminar los archivos relacionados al servicio id: ';
        } else {
            $resultado_sql = $this->consulta_sql("DELETE FROM servicios WHERE idservicio = '".$idservicio."' LIMIT 1");
            if ($this->afectado_sql($resultado_sql)) {
                $this->borrar_saldo('ventasxservicios', $idservicio);
                $this->borrar_saldo('comprasxservicios', $idservicio);
                $response = 'Baja exitosa del servicio:';
            } else {
                $response = 'Error en la baja servicio: ';
            }
        }

        return [$response.$idservicio];
    }

    private function insertar_saldo($tiporelacion, $idrelacion)
    {
        $this->consulta_sql("INSERT INTO saldos SET
            tiporelacion = '".$tiporelacion."',
            idrelacion = '".$idrelacion."',
            saldo = 0");
    }

    private function borrar_saldo($tiporelacion, $idrelacion)
    {
        $this->consulta_sql("DELETE FROM saldos
            WHERE tiporelacion = '".$tiporelacion."'
            AND idrelacion = '".$idrelacion."'");
    }

    private function existe_archivo($modulo, $id)
    {
        return $this->contar_sql($this->consulta_sql("SELECT idarchivo FROM archivos WHERE modulo = '".$modulo."' AND id = '".$id."'"));
    }

}
