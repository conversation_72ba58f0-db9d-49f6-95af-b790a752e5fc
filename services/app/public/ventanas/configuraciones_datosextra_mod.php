<?php
switch ($boton) {

    case $i18n[65]: //Aceptar
    	$datos = recibir_matriz(array(
        array('texto', 'nombre'),
        array('texto', 'modulo'),
        array('texto', 'tipo')
    ));
		if (!$datos['nombre']) {
			mensajes_alta($i18n[270]);
		} else {
	    	consulta_sql("UPDATE extrasxmodulos SET
	    		nombre = '".$datos['nombre']."',
	        	modulo = '".$datos['modulo']."',
	        	tipo = '".$datos['tipo']."'
	        	WHERE idextraxmodulo = '".$id."'
			");

            if ($datos['tipo'] == 'lista' && !contar_sql(consulta_sql(
                "SELECT * FROM listasxextras WHERE
                    idextraxmodulo = '$id'"))) {
                consulta_sql("INSERT INTO listasxextras SET
                    idextraxmodulo = '$id',
                    nombre = 'Sin especificar'");
            }
	    }
	    ir_ahora('configuraciones.php?a=datosextra');
        break;

    case $i18n[66]: //Cancelar
        ir_ahora('configuraciones.php?a=datosextra');
        break;

    default:
    	$modulo = array(
                array('id' => 'bienes', 'valor' => $i18n_funciones['bienes']),
                array('id' => 'clientes', 'valor' => $i18n_funciones['clientes']),
                // array('id' => 'comunicacion', 'valor' => $i18n_funciones['comunicacion']),
                array('id' => 'compras', 'valor' => $i18n_funciones['compras']),
                array('id' => 'conocimientos', 'valor' => $i18n_funciones['conocimientos']),
				array('id' => 'productos', 'valor' => $i18n_funciones['productos']),
                array('id' => 'proveedores', 'valor' => $i18n_funciones['proveedores']),
                array('id' => 'servicios', 'valor' => $i18n_funciones['servicios']),
                array('id' => 'ventas', 'valor' => $i18n_funciones['ventas'])
            );
    	$tipo = array(
    			array('id' => 'texto', 'valor' => $i18n['texto']),
    			array('id' => 'lista', 'valor' => $i18n['lista']),
    		);
    	$listas = array();
    	$resultado_sql = consulta_sql("SELECT * FROM listasxextras WHERE idextraxmodulo = '".$id."' ORDER BY idlistaxextra ASC");
    	while ($temp_array = array_sql($resultado_sql)) {
    		$listas[] = array('id' => $temp_array['idlistaxextra'], 'valor' => $temp_array['nombre']);
    	}
        $datos = array_sql(consulta_sql(
        	"SELECT extrasxmodulos.*
        	FROM extrasxmodulos
        	WHERE idextraxmodulo = '$id'
        	LIMIT 1"));
        break;
}

ventana_inicio($i18n[597]);
{
    contenido_inicio();
    {
        entrada('texto', 'nombre', $i18n[180], $datos['nombre'], '25', '60');
        selector_array('modulo', $i18n[262], $datos['modulo'], '20', $modulo, false);
        selector_array('tipo', $i18n[263], $datos['tipo'], '20', $tipo, false, 'onchange="chequear_lista()"');
        bloque_inicio('listas');
        {
            selector_array('listasxextras', $i18n[596], '', '35', $listas, false, 'id="selectorlistas"', true);
        }
        bloque_fin();
    }
    contenido_fin();

	botones(array(array('valor' => $i18n[65]), array('valor' => $i18n[66])));
}
ventana_fin();

?>

<script>
    let tipo_inicial = $("select[name=tipo]").val();
	chequear_lista();

    function chequear_lista()
    {
    	if ($("select[name=tipo]").val() == 'lista') {
            if (tipo_inicial != $("select[name=tipo]").val())
                alerta('Debe Aceptar el cambio antes de modificar los Datos de la Lista Desplegable');
            else
                $("#listas").show();
        } else {
            $("#listas").hide();
        }
    }
</script>
