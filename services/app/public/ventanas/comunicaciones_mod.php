<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('entero', 'idcliente'),
        array('texto', 'cliente'),
        array('fecha', 'fechafin'),
        array('entero', 'idtipocomunicacion'),
        array('entero', 'idusuario'),
        array('fecha', 'fechainicio'),
        array('entero', 'idtiporespuestaxcomunicacion'),
        array('largo', 'obssolicitado'),
        array('largo', 'obsrealizado')
    ));
} else {
    $datos = array_sql(consulta_sql("SELECT comunicaciones.*, categorias_comunicaciones.nombre AS categoria, usuarios.nombre AS usuario FROM comunicaciones LEFT JOIN categorias_comunicaciones ON comunicaciones.idtipocomunicacion=categorias_comunicaciones.idtipocomunicacion LEFT JOIN usuarios ON comunicaciones.idusuario=usuarios.idusuario WHERE idcomunicacion='".$id."' LIMIT 1"));
}

switch ($boton) {
    case $i18n[29]: // Terminar
        if ($datos['fechafin'] && $datos['fechafin'] != '0000-00-00 00:00:00')
            $datos['fechafin'] = fecha_sql($datos['fechafin']);
        else
            $datos['fechafin'] = date("Y-m-d H:i:00");

    case $i18n[30]: // Aceptar
        consulta_sql("UPDATE comunicaciones SET idtipocomunicacion='".$datos['idtipocomunicacion']."', idusuario='".$datos['idusuario']."', fechainicio='".fecha_sql($datos['fechainicio'])."', fechafin='".$datos['fechafin']."', idtiporespuestaxcomunicacion='".$datos['idtiporespuestaxcomunicacion']."', obssolicitado='".$datos['obssolicitado']."', obsrealizado='".$datos['obsrealizado']."' WHERE idcomunicacion='".$id."' LIMIT 1");
        extras_mod();
        ir_atras();
        break;

    case $i18n[31]: // Cancelar
        ir_atras();
        break;
}

$cliente = array_sql(consulta_sql("SELECT * FROM clientes WHERE idcliente='".$datos['idcliente']."' LIMIT 1"));

ventana_inicio($i18n[27].$id, '100', array(
    array('tipo' => 'imagen', 'url' => 'comunicaciones.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[44]),
    array('tipo' => 'imagen', 'url' => 'comunicaciones.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[15], 'permiso' => 'comunicaciones_baja', 'opciones' => 'onclick="return confirma('."'$i18n[16]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[22])));
{
    //Contenido de datos del cliente
    contenido_inicio($i18n[65].$cliente['nombre'], '100', true, false);
    {
        texto('texto', $i18n[50], $cliente['idcliente'], '33');
        //texto('texto', $i18n[48], $cliente['nombre'], '33');
        texto('texto', $i18n[49], $cliente['contacto'], '33');
        texto('texto', $i18n[51], $cliente['telefonos'], '33');
        texto('mail', $i18n[52], $cliente['mail'], '33');
        texto('texto', $i18n[4], $cliente['domicilio'], '33');

        if ($cliente['observacion']) {
            observacion($i18n[5], $cliente['observacion']);
        }
        if ($cliente['obsinterna']) {
            observacion($i18n[68], $cliente['obsinterna']);
        }
        if ($cliente['obsrecordatorio']) {
            observacion($i18n[6], $cliente['obsrecordatorio']);
        }
    }
    contenido_fin();

    contenido_inicio($i18n[33]);
    {
        selector_array('prioridad', $i18n[70], $datos['prioridad'], '25', array(array('id' => '1', 'valor' => $i18n['prioridad_1']), array('id' => '2', 'valor' => $i18n['prioridad_2']), array('id' => '3', 'valor' => $i18n['prioridad_3']), array('id' => '4', 'valor' => $i18n['prioridad_4']), array('id' => '5', 'valor' => $i18n['prioridad_5'])));
        selector('idusuario', $i18n[23], $datos['idusuario'], '25', 'usuarios', 'nombre', false, false, true);
        selector_familiar('idtipocomunicacion', $i18n[9], $datos['idtipocomunicacion'], '25', 'categorias_comunicaciones', true, true, true);
        selector_familiar('idtiporespuestaxcomunicacion', $i18n[20], $datos['idtiporespuestaxcomunicacion'], '25', 'categorias_respuestasxcomunicaciones', true, true, true);
        entrada('fechayhora', 'fechainicio', $i18n[7], $datos['fechainicio'], '25');
        entrada('fechayhora', 'fechafin', $i18n[8], $datos['fechafin'], '25');
    }
    contenido_fin();

    contenido_inicio($i18n[5]);
    {
        area('obssolicitado', $i18n[24], $datos['obssolicitado']);
        area('obsrealizado', $i18n[25], $datos['obsrealizado']);
    }
    contenido_fin();

    if ($datos['fechafin'] == '0000-00-00 00:00:00') {
        botones(array(array('valor' => $i18n[29]), array('valor' => $i18n[30]), array('valor' => $i18n[31])));
    } else {
        botones(array(array('valor' => $i18n[30]), array('valor' => $i18n[31])));
    }
}
ventana_fin();
