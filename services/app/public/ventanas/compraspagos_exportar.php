<?php
$multiples_pagos = $pagos ? $pagos : recibir_variable('texto', 'pagos', true);
if ($multiples_pagos) {
    $resultado_sql = consulta_sql(
        "SELECT compraspagos.*,
            usuarios.nombre AS usuario,
            tablas_formasdepago.nombre AS formapago,
            movimientosxcajas.idcaja,
            compras.numero AS numero, compras.numerocompleto,
            proveedores.nombre, proveedores.contacto, proveedores.razonsocial, proveedores.cuit,
            cheques.*,
            categorias_tributos.nombre AS tributo,
            retenciones.observacion AS retencionesobservacion,
            tablas_bancos.nombre AS banco,
            monedas.simbolo
        FROM compraspagos
            LEFT JOIN usuarios ON compraspagos.idusuario = usuarios.idusuario
            LEFT JOIN tablas_formasdepago ON compraspagos.idformapago = tablas_formasdepago.idformapago
            LEFT JOIN movimientosxcajas ON (compraspagos.idcomprapago = movimientosxcajas.idrelacion AND movimientosxcajas.tiporelacion = 'proveedorpago')
            LEFT JOIN compras ON compraspagos.idcompra = compras.idcompra
            LEFT JOIN proveedores ON compraspagos.idproveedor = proveedores.idproveedor
            LEFT JOIN cheques ON compraspagos.idrelacion = cheques.idcheque
            LEFT JOIN tablas_bancos ON cheques.idbanco = tablas_bancos.idbanco
            LEFT JOIN retenciones ON compraspagos.idrelacion = retenciones.idretencion
            LEFT JOIN categorias_tributos ON retenciones.idtributo = categorias_tributos.idtributo
            LEFT JOIN monedas ON compraspagos.idmoneda = monedas.idmoneda
        WHERE idnumerocomprapago = (
            SELECT idnumerocomprapago FROM compraspagos
            WHERE idcomprapago = '$id'
            LIMIT 1
        )
        AND movimientosxcajas.tiporelacion = 'proveedorpago'
        ORDER BY compraspagos.fecha ASC");

    $tmp_data = array_all_sql($resultado_sql);
    $comprapago = $tmp_data[0];

} else {

    $comprapago = array_sql(consulta_sql(
        "SELECT compraspagos.*,
            usuarios.nombre AS usuario,
            tablas_formasdepago.nombre AS formapago,
            compras.numero AS numero, compras.numerocompleto,
            proveedores.nombre, proveedores.contacto, proveedores.razonsocial, proveedores.cuit,
            cheques.*,
            categorias_tributos.nombre AS tributo,
            retenciones.observacion AS retencionesobservacion,
            tablas_bancos.nombre AS banco,
            monedas.simbolo
        FROM compraspagos
            LEFT JOIN usuarios ON compraspagos.idusuario = usuarios.idusuario
            LEFT JOIN tablas_formasdepago ON compraspagos.idformapago = tablas_formasdepago.idformapago
            LEFT JOIN compras ON compraspagos.idcompra = compras.idcompra
            LEFT JOIN proveedores ON compraspagos.idproveedor = proveedores.idproveedor
            LEFT JOIN cheques ON compraspagos.idrelacion = cheques.idcheque
            LEFT JOIN tablas_bancos ON cheques.idbanco = tablas_bancos.idbanco
            LEFT JOIN retenciones ON compraspagos.idrelacion = retenciones.idretencion
            LEFT JOIN categorias_tributos ON retenciones.idtributo = categorias_tributos.idtributo
            LEFT JOIN monedas ON compraspagos.idmoneda = monedas.idmoneda
        WHERE idcomprapago = '$id'
        LIMIT 1"));
}

if ($comprapago['total'] < 0) {
    $temp_recibo = 'Devolución de pago Nº DP';
} else {
    $temp_recibo = 'Orden de pago Nº OP';
}

exportar('
    <div id="prn_ventana_cuerpo">
        <div id="prn_ventana_cuerpo_inicio">
            <p class="prn_p_titulo_ventana_cuerpo">'.$temp_recibo.completar_numero($comprapago['idnumerocomprapago'], 8).'</p>
    </div> <!--Cierro el div id="ventana_cuerpo_inicio-->

        <table class="prn_t_ventana_cuerpo" border="0" width="100%">
            <tr>
                <td class="prn_td_subtitulos" width="50%">Datos básicos</td>
                <td class="prn_td_subtitulos" width="50%">Proveedor</td>
            </tr>
            <tr>
                <td class="prn_td_datos">');
if ($comprapago['idcompra'] != '0') {
    exportar('
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Compra relacionada: </span>'.$comprapago['numerocompleto'].'</p>');
}
exportar('
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Total: </span>'.convertir_numero($multiples_pagos ? array_sum(array_column($tmp_data, 'total')) : $comprapago['total'], 'moneda', $comprapago['simbolo']).'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Fecha: </span>'.$comprapago['fecha'].'</p>');
if (!$multiples_pagos) {
    exportar('
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Forma de pago: </span>'.$comprapago['formapago'].'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Usuario: </span>'.$comprapago['usuario'].'</p>');
}
exportar('
                </td>
                <td class="prn_td_datos">
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Nombre: </span>'.$comprapago['nombre'].'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Proveedor Nº: </span>'.$comprapago['idproveedor'].'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Contacto: </span>'.$comprapago['contacto'].'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Razón social: </span>'.$comprapago['razonsocial'].'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">CUIT: </span>'.($comprapago['cuit'] ? $comprapago['cuit']['0'].$comprapago['cuit']['1'].'-'.$comprapago['cuit']['2'].$comprapago['cuit']['3'].$comprapago['cuit']['4'].$comprapago['cuit']['5'].$comprapago['cuit']['6'].$comprapago['cuit']['7'].$comprapago['cuit']['8'].$comprapago['cuit']['9'].'-'.$comprapago['cuit']['10'] : '').'</p>
                </td>
            </tr>');

if (!$multiples_pagos && $comprapago['tiporelacion'] == 'cheque') {
    if ($comprapago['tipo'] == 'propio') {
        $comprapago['tipo'] = 'Propio';
        $comprapago['cobropago'] = 'pago: ';
    } else {
        $comprapago['tipo'] = 'De tercero';
        $comprapago['cobropago'] = 'cobro: ';
    }
    exportar('
        <tr>
            <td class="prn_td_datos">
                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Tipo de cheque: </span>'.$comprapago['tipo'].'</p>
                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Fecha de '.$comprapago["cobropago"].'</span>'.$comprapago['fechacobro'].'</p>
                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Banco: </span>'.$comprapago['banco'].'</p>
                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Titular: </span>'.$comprapago['titular'].'</p>
                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Número de cheque: </span>'.$comprapago['numero'].'</p>
            </td>
        </tr>
    ');
}
if (!$multiples_pagos && $comprapago['tiporelacion'] == 'retencion') {
    exportar('
    <tr>
        <td class="prn_td_datos">
            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Impuesto de la retención: </span>'.$comprapago['tributo'].'</p>
            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Observación de la retención: </span>'.$comprapago['retencionesobservacion'].'</p>
        </td>
    </tr>
    ');
}

if ($multiples_pagos) {
    puntero_sql($resultado_sql);

    if (contar_sql($resultado_sql)) {
        exportar('</table>');
        exportar('<table class="prn_t_ventana_cuerpo" border="0" width="100%">');
        exportar('<tr class="prn_tr_titulo_lista">
                    <th>Fecha</th>
                    <th>Forma de pago</th>
                    <th>Detalle</th>
                    <th>Importe</th>');

        exportar('</tr>');
    }

    while ($compra_pago = array_sql($resultado_sql)) {
        if ($compra_pago['tiporelacion'] == 'cheque') {
            $detalle = 'Nro. de cheque: ' . $compra_pago['numero'] . ' | Banco: ' . $compra_pago['banco'] . ' | Titular: ' . $compra_pago['titular'] . ($compra_pago['tipo'] == 'tercero' ? ' | Fecha cobro: ' : ' | Fecha pago: ') . mostrar_fecha('fecha', $compra_pago['fechacobro']);
        } else if ($compra_pago['tiporelacion'] == 'retencion') {
            $detalle = 'Impuesto de la retención: ' . $compra_pago['tributo'] . ' | Obs. de la retención: ' . $compra_pago['retencionesobservacion'];
        } else {
            $detalle = '';
        }
        exportar('
                    <tr>
                        <td class="prn_td_texto">'.mostrar_fecha('fechayhora', $compra_pago['fecha']).'</td>
                        <td class="prn_td_texto">'.$compra_pago['formapago'].'</td>
                        <td class="prn_td_texto">'.$detalle.'</td>
                        <td class="prn_td_texto">'.convertir_numero($compra_pago['total'], 'moneda', $comprapago['simbolo']).'</td>');
        exportar('
                    </tr>');
    }

    exportar('</table>');
    exportar('<table class="prn_t_ventana_cuerpo" border="0" width="50%">');
}
extras_exportar();
exportar('
            <tr>
                <td class="prn_td_datos" colspan="2">
                    <p class="prn_p_nombre_campo">Observaciones</p>
                    <p class="prn_p_texto_campo">'.$comprapago['observacion'].'</p>
                </td>
            </tr>
            <tr>
                <td></td>
                <td class="prn_td_datos"><br><br>
                    <p class="prn_p_nombre_campo" style="border-top: dotted;" id="compraspagos_firma"><br>Firma y aclaración</p>
                </td>
            </tr>
        </table>

        <div id="prn_ventana_cuerpo_fin"> </div>
    </div>  <!--Cierro el <div id="prn_ventana_cuerpo">-->
');
