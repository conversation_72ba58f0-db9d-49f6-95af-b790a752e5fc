<?php

$tablas = [
    'mensajes', 'tareas', 'bienes', 'servicios', 'conocimientos', 'tickets', // Tablas personales y generales
    'precios', 'productos', 'stock', 'traslados', 'listas', 'depositos', // Tablas de productos y comprobantes
    'clientes', 'ventas', 'ventaspagos', 'proveedores', 'compras', 'compraspagos', // Tablas de clientes y proveedores
    'cajas', 'cheques', 'retenciones', // Tablas de cajas
    // Tablas de categorias
    'categorias_bienes', 'categorias_cajas', 'categorias_clientes', 'categorias_comunicaciones',
    'categorias_conceptos', 'categorias_conocimientos', 'categorias_localidades', 'categorias_proveedores',
    'categorias_respuestasxcomunicaciones', 'categorias_rubros', 'categorias_servicios', 'categorias_tributos',
    'categorias_ventas', 'perfiles', 'plantillas', 'smtps', 'tienda', 'extrasxmodulos',
];
$idempresa = recibir_variable('entero', 'idempresa', false);

switch ($boton) {
    case $i18n_funciones[156]: // Vaciar
        $filtros = recibir_matriz(array(
        array('fecha', 'fecha_desde'),
        array('fecha', 'fecha_hasta')
    ));
        $tablas_seleccionadas = recibir_matriz($tablas);

        // Preparo los where de filtros
        if ($filtros['fecha_desde'] && $filtros['fecha_hasta'])
            $where = "fecha >= '".fecha_sql($filtros['fecha_desde'], true)."' AND fecha <= '".fecha_sql($filtros['fecha_hasta'], true)."'";
        else if ($filtros['fecha_desde'])
            $where = "fecha >= '".fecha_sql($filtros['fecha_desde'], true)."'";
        else if ($filtros['fecha_hasta'])
            $where = "fecha <= '".fecha_sql($filtros['fecha_hasta'], true)."'";
        else
            $where = "";

        $consultas = [];

        foreach ($tablas_seleccionadas as $tabla => $seleccionada) {

            if (!$seleccionada)
                continue;

            switch ($tabla) {
                // Tablas personales y generales
                case 'mensajes':
                    $consultas[] = $where ? "DELETE FROM $tabla WHERE $where" : "TRUNCATE $tabla";
                    $consultas[] = "DELETE FROM mails_enviados WHERE tiporelacion = '$tabla'";
                    break;

                case 'tareas':
                    $consultas[] = "DELETE FROM mails_enviados WHERE tiporelacion = '$tabla'";
                case 'conocimientos':
                    $consultas[] = "TRUNCATE $tabla";
                    break;

                case 'bienes':
                    $consultas[] = "TRUNCATE $tabla";
                    $consultas[] = "TRUNCATE bienesxservicios";
                    break;

                case 'tickets':
                    $consultas[] = "TRUNCATE $tabla";
                    $consultas[] = "TRUNCATE textosxtickets";
                    break;

                case 'servicios':
                    $consultas[] = $where
                        ? "DELETE FROM bienesxservicios WHERE idservicio IN
                            (SELECT idservicio FROM servicios WHERE ".str_replace('fecha', 'fechasolicitado', $where).")"
                        : "TRUNCATE bienesxservicios";
                    $consultas[] = "UPDATE compras SET tiporelacion = '', idrelacion = 0 WHERE tiporelacion = 'servicio'";
                    $consultas[] = "UPDATE ventas SET tiporelacion = '', idrelacion = 0 WHERE tiporelacion = 'servicio'";
                    $consultas[] = $where ? "DELETE FROM $tabla WHERE $where" : "TRUNCATE $tabla";
                    $consultas[] = "DELETE FROM mails_enviados WHERE tiporelacion IN ('ventasxservicios', 'comprasxservicios', 'servicios')";
                    break;

                // Tablas de productos y comprobantes
                case 'productos':
                    $consultas[] = "TRUNCATE $tabla";
                    $consultas[] = "TRUNCATE precios";
                    $consultas[] = "TRUNCATE stock";
                    $consultas[] = "TRUNCATE productosxcombos";
                    $consultas[] = "TRUNCATE traslados";
                    $consultas[] = "TRUNCATE productosxtraslados";
                    $consultas[] = "TRUNCATE historial";
                    $consultas[] = "UPDATE productosxcompras SET idproducto = 0";
                    $consultas[] = "UPDATE productosxventas SET idproducto = 0";
                    break;

                case 'traslados':
                    $consultas[] = "TRUNCATE $tabla";
                    $consultas[] = $where
                        ? "DELETE FROM $tabla WHERE ".str_replace('fecha', 'fechainicio', $where)
                        : "TRUNCATE $tabla";
                    $consultas[] = "TRUNCATE productosxtraslados";
                    $consultas[] = "DELETE FROM mails_enviados WHERE tiporelacion = '$tabla'";
                    break;

                case 'precios':
                case 'stock':
                    $consultas[] = "TRUNCATE $tabla";
                    break;

                case 'listas':
                    $consultas[] = "DELETE FROM listas WHERE idlista > 1";
                    $consultas[] = "TRUNCATE precios";
                    break;

                case 'depositos':
                    $consultas[] = "DELETE FROM depositos WHERE iddeposito > 1";
                    $consultas[] = "TRUNCATE stock";
                    break;

                // Tablas de clientes y proveedores
                case 'clientes':
                    $consultas[] = "DELETE FROM clientes WHERE idcliente > 1";
                    $consultas[] = "ALTER TABLE `clientes` AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE ventas SET idcliente = 1";
                    $consultas[] = "UPDATE ventaspagos SET idcliente = 1";
                    $consultas[] = "DELETE FROM mails_enviados WHERE tiporelacion = '$tabla'";
                    break;

                case 'ventas':
                    $consultas[] = $where
                        ? "DELETE FROM productosxventas WHERE idventa IN
                            (SELECT idventa FROM ventas WHERE $where)"
                        : "TRUNCATE productosxventas";
                    $consultas[] = $where
                        ? "DELETE FROM ivasxventas WHERE idventa IN
                            (SELECT idventa FROM ventas WHERE $where)"
                        : "TRUNCATE ivasxventas";
                    $consultas[] = $where
                        ? "DELETE FROM tributosxventas WHERE idventa IN
                            (SELECT idventa FROM ventas WHERE $where)"
                        : "TRUNCATE tributosxventas";
                    $consultas[] = $where
                        ? "DELETE FROM ventasxventas WHERE idventa IN (SELECT idventa FROM ventas WHERE $where)
                            OR idrelacion IN (SELECT idventa FROM ventas WHERE $where)"
                        : "TRUNCATE ventasxventas";
                    $consultas[] = $where
                        ? "DELETE FROM ventas_ml WHERE idventa IN
                            (SELECT idventa FROM ventas WHERE $where)"
                        : "TRUNCATE ventas_ml";
                    $consultas[] = $where
                        ? "DELETE FROM ventasxclientes WHERE idtipoventa > 0
                            AND id IN (SELECT idventa FROM ventas WHERE $where)"
                        : "DELETE FROM ventasxclientes WHERE idtipoventa > 0";
                    $consultas[] = "UPDATE categorias_ventas SET ultimonumero = 0";
                    $consultas[] = "UPDATE ventaspagos SET idventa = 0";
                    $consultas[] = $where ? "DELETE FROM $tabla WHERE $where" : "TRUNCATE $tabla";
                    $consultas[] = "DELETE FROM mails_enviados WHERE tiporelacion = '$tabla'";
                    break;

                case 'ventaspagos':
                    $consultas[] = $where
                        ? "DELETE FROM movimientosxcajas WHERE tiporelacion = 'clientepago'
                            AND idrelacion IN (SELECT idventapago FROM ventaspagos WHERE $where)"
                        : "DELETE FROM movimientosxcajas WHERE tiporelacion = 'clientepago'";
                    $consultas[] = $where
                        ? "DELETE FROM ventasxclientes WHERE idtipoventa <= 0
                            AND id IN (SELECT idventapago FROM ventas WHERE $where)"
                        : "DELETE FROM ventasxclientes WHERE idtipoventa > 0";
                    $consultas[] = $where ? "DELETE FROM $tabla WHERE $where" : "TRUNCATE $tabla";
                    $consultas[] = "DELETE FROM mails_enviados WHERE tiporelacion = '$tabla'";
                    break;

                case 'proveedores':
                    $consultas[] = "DELETE FROM proveedores WHERE idproveedor > 1";
                    $consultas[] = "ALTER TABLE `proveedores` AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE compras SET idproveedor = 1";
                    $consultas[] = "UPDATE compraspagos SET idproveedor = 1";
                    $consultas[] = "DELETE FROM mails_enviados WHERE tiporelacion = '$tabla'";
                    break;

                case 'compras':
                    $consultas[] = $where ? "DELETE FROM $tabla WHERE $where" : "TRUNCATE $tabla";
                    $consultas[] = $where
                        ? "DELETE FROM productosxcompras WHERE idcompra IN
                            (SELECT idcompra FROM compras WHERE $where)"
                        : "TRUNCATE productosxcompras";
                    $consultas[] = $where
                        ? "DELETE FROM ivasxcompras WHERE idcompra IN
                            (SELECT idcompra FROM compras WHERE $where)"
                        : "TRUNCATE ivasxcompras";
                    $consultas[] = $where
                        ? "DELETE FROM tributosxcompras WHERE idcompra IN
                            (SELECT idcompra FROM compras WHERE $where)"
                        : "TRUNCATE tributosxcompras";
                    $consultas[] = $where
                        ? "DELETE FROM comprasxcompras WHERE idcompra IN (SELECT idcompra FROM compras WHERE $where)
                            OR idrelacion IN (SELECT idcompra FROM compras WHERE $where)"
                        : "TRUNCATE comprasxcompras";
                    $consultas[] = $where
                        ? "DELETE FROM comprasxproveedores WHERE idtipocompra > 0
                            AND id IN (SELECT idcompra FROM compras WHERE $where)"
                        : "DELETE FROM comprasxproveedores WHERE idtipocompra > 0";
                    $consultas[] = "UPDATE compraspagos SET idcompra = 0";
                    $consultas[] = "DELETE FROM mails_enviados WHERE tiporelacion = '$tabla'";
                    break;

                case 'compraspagos':
                    $consultas[] = $where
                        ? "DELETE FROM movimientosxcajas WHERE tiporelacion = 'proveedorpago'
                            AND idrelacion IN (SELECT idcomprapago FROM compraspagos WHERE $where)"
                        : "DELETE FROM movimientosxcajas WHERE tiporelacion = 'proveedorpago'";
                    $consultas[] = $where
                        ? "DELETE FROM comprasxproveedores WHERE idtipocompra <= 0
                            AND id IN (SELECT idcomprapago FROM compras WHERE $where)"
                        : "DELETE FROM comprasxproveedores WHERE idtipocompra > 0";
                    $consultas[] = $where ? "DELETE FROM $tabla WHERE $where" : "TRUNCATE $tabla";
                    $consultas[] = "DELETE FROM mails_enviados WHERE tiporelacion = '$tabla'";
                    break;

                // Tablas de cajas
                case 'cajas':
                    $consultas[] = $where
                        ? "DELETE FROM movimientosxcajas WHERE $where"
                        : "TRUNCATE movimientosxcajas";
                    $consultas[] = $where
                        ? "DELETE FROM cajas AS c WHERE NOT EXISTS (SELECT movimientosxcajas AS mc WHERE mc.idcaja = c.idcaja)
                            AND ".str_replace('fecha', 'fechaapertura', $where)
                        : "TRUNCATE cajas";
                    $categorias_cajas_sql = consulta_sql("SELECT * FROM categorias_cajas", $idempresa);
                    while ($categoria_caja = array_sql($categorias_cajas_sql)) {
                        $consultas[] = "INSERT INTO cajas SET idtipocaja = {$categoria_caja['idtipocaja']}, fechaapertura = NOW()";
                    }
                    break;

                case 'cheques':
                    $consultas[] = $where
                        ? "DELETE FROM movimientosxcajas WHERE idcaja IN (SELECT idcaja FROM categorias_cajas WHERE tipo = 'cheque')
                            AND $where"
                        : "DELETE FROM movimientosxcajas WHERE idcaja IN (SELECT idcaja FROM categorias_cajas WHERE tipo = 'cheque')";
                    $consultas[] = "UPDATE compraspagos SET tiporelacion = '', idrelacion = 0 WHERE tiporelacion = 'cheque'";
                    $consultas[] = "UPDATE ventaspagos SET tiporelacion = '', idrelacion = 0 WHERE tiporelacion = 'cheque'";
                    $consultas[] = "TRUNCATE $tabla";
                    break;

                case 'retenciones':
                    $consultas[] = $where
                        ? "DELETE FROM movimientosxcajas WHERE idcaja IN (SELECT idtipocaja FROM categorias_cajas WHERE tipo = 'retencion')
                            AND $where"
                        : "DELETE FROM movimientosxcajas WHERE idcaja IN (SELECT idtipocaja FROM categorias_cajas WHERE tipo = 'retencion')";
                    $consultas[] = "UPDATE compraspagos SET tiporelacion = '', idrelacion = 0 WHERE tiporelacion = 'retencion'";
                    $consultas[] = "UPDATE ventaspagos SET tiporelacion = '', idrelacion = 0 WHERE tiporelacion = 'retencion'";
                    $consultas[] = "TRUNCATE $tabla";
                    break;

            // Tablas de categorías
                case 'categorias_bienes':
                    $consultas[] = "DELETE FROM $tabla WHERE ".key_tabla($tabla)." > 0";
                    $consultas[] = "ALTER TABLE $tabla AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE bienes SET idbien = 0";
                    break;

                case 'categorias_cajas':
                    $consultas[] = "TRUNCATE cajas";
                    $consultas[] = "INSERT INTO `cajas` (`idcaja`, `idtipocaja`, `fechaapertura`) VALUES
                        (1, 1, NOW()), (2, 2, NOW()), (3, 3, NOW()), (4, 4, NOW())";
                    $consultas[] = "TRUNCATE categorias_cajas";
                    $consultas[] = "INSERT INTO `categorias_cajas` (`idtipocaja`, `nombre`, `compartida`, `estado`, `idcaja`, `tipo`) VALUES
                        (1, 'Caja principal', 1, 1, 1, 'efectivo'), (2, 'Banco', 1, 1, 2, 'banco'),
                        (3, 'Cartera de cheques', 1, 1, 3, 'cheque'), (4, 'Retenciones', 1, 1, 4, 'retencion')";
                   break;

                case 'categorias_clientes':
                    $consultas[] = "DELETE FROM $tabla WHERE ".key_tabla($tabla)." > 0";
                    $consultas[] = "ALTER TABLE $tabla AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE clientes SET idtipocliente = 0";
                    break;

                case 'categorias_conceptos':
                    $consultas[] = "DELETE FROM $tabla WHERE ".key_tabla($tabla)." > 0";
                    $consultas[] = "ALTER TABLE $tabla AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE movimientosxcajas SET idconcepto = 0";
                    break;

                case 'categorias_conocimientos':
                    $consultas[] = "DELETE FROM $tabla WHERE ".key_tabla($tabla)." > 0";
                    $consultas[] = "ALTER TABLE $tabla AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE conocimientos SET idtipoconocimiento = 0";
                    break;

                case 'categorias_localidades':
                    $consultas[] = "DELETE FROM $tabla WHERE ".key_tabla($tabla)." > 0";
                    $consultas[] = "ALTER TABLE $tabla AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE clientes SET idlocalidad = 0";
                    $consultas[] = "UPDATE proveedores SET idlocalidad = 0";
                    $consultas[] = "UPDATE configuraciones SET idlocalidad = 0";
                    $consultas[] = "UPDATE categorias_clientes SET idlocalidad = 0";
                    $consultas[] = "UPDATE ventas SET idlocalidad = 0";
                    $consultas[] = "UPDATE compras SET idlocalidad = 0";
                    $consultas[] = "UPDATE depositos SET idlocalidad = 0";
                    break;

                case 'categorias_proveedores':
                    $consultas[] = "DELETE FROM $tabla WHERE ".key_tabla($tabla)." > 0";
                    $consultas[] = "ALTER TABLE $tabla AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE proveedores SET idtipoproveedor = 0";
                    break;

                case 'categorias_rubros':
                    $consultas[] = "DELETE FROM categorias_rubros WHERE idrubro > 0";
                    $consultas[] = "ALTER TABLE categorias_rubros AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE productos SET idrubro = 0";
                    break;

                case 'categorias_servicios':
                    $consultas[] = "DELETE FROM $tabla WHERE ".key_tabla($tabla)." > 0";
                    $consultas[] = "ALTER TABLE $tabla AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE servicios SET idtiposervicio = 0";
                    break;

                case 'categorias_tributos':
                    $consultas[] = "DELETE FROM $tabla WHERE ".key_tabla($tabla)." > 0";
                    $consultas[] = "ALTER TABLE $tabla AUTO_INCREMENT = 1";
                    $consultas[] = "UPDATE tributosxventas SET idtributo = 0";
                    $consultas[] = "UPDATE tributosxcompras SET idtributo = 0";
                    break;

                case 'categorias_ventas':
                    $consultas[] = "DELETE FROM $tabla WHERE ".key_tabla($tabla)." > 0";
                    $consultas[] = "ALTER TABLE $tabla AUTO_INCREMENT = 1";
                    break;

                case 'perfiles':
                    $consultas[] = "DELETE FROM $tabla WHERE ".key_tabla($tabla)." > 1";
                    $consultas[] = "ALTER TABLE $tabla AUTO_INCREMENT = 2";
                    $consultas[] = "TRUNCATE perfilesxcajas";

                case 'plantillas':
                case 'smtps':
                    $consultas[] = "TRUNCATE $tabla";
                    break;

                case 'tienda':
                    $consultas[] = "TRUNCATE $tabla";
                    $consultas[] = "INSERT INTO `tienda` (`idtienda`, `tienda_estado`, `tienda_nombre`, `estilo_tienda`, `tienda_idtipoventa`, `tienda_sinstock`, `API_estado`, `ML_estado`, `ML_user_id`, `ML_access_token`, `ML_refresh_token`, `ML_expires_in`, `ML_idtipoventa`, `ML_idiva`, `MP_estado`, `MP_access_token`, `MP_refresh_token`, `MP_expires_in`, `MP_grant_type`, `MP_client_id`, `MP_client_secret`, `MP_idtipocaja`, `MP_idconcepto`, `idplantilla_venta_tienda`, `idplantilla_venta_ML`, `idplantilla_venta_MP`, `idplantilla_alta_usuario`, `idplantilla_resetear_pass`) VALUES (1, 0, 'Cuenta Principal', '', 0, 0, 0, 1, 0, '', '', 0, 0, 2, 0, '', '', 0, '', '', '', 0, 0, 0, 0, 0, 0, 0)";
                    break;

                case 'extrasxmodulos':
                    $consultas[] = "TRUNCATE $tabla";
                    $consultas[] = "TRUNCATE listasxextras";
                    $consultas[] = "TRUNCATE datosxextras";
                    break;
            }

        }

        echo '<code style="background: white">';
        foreach($consultas as $consulta) {
            // consulta_sql($consulta, $idempresa);
            echo $consulta.";<br>";
        }
        echo '</code>';
        break;

    default:
        $idempresa = recibir_variable('entero', 'idempresa', true);
        $datos = array();
        break;
}

ventana_inicio($i18n[166].$idempresa);
{
    contenido_inicio($i18n[191]);
    {
        texto('italica', false, $i18n[199]);
        entrada('fechayhora', 'fecha_desde', $i18n[192], $datos['fecha_desde'], '20');
        entrada('fechayhora', 'fecha_hasta', $i18n[193], $datos['fecha_hasta'], '20');
    }
    contenido_fin();

    contenido_inicio($i18n[190]);
    {
        // Tablas personales y generales
        marcas($i18n[194], '50', array(
            array('nombre' => 'mensajes', 'titulo' => $i18n_funciones['mensajes']),
            array('nombre' => 'tareas', 'titulo' => $i18n_funciones['tareas']),
            array('nombre' => 'bienes', 'titulo' => $i18n_funciones['bienes']),
            array('nombre' => 'servicios', 'titulo' => $i18n_funciones['servicios']),
            array('nombre' => 'conocimientos', 'titulo' => $i18n_funciones['conocimientos']),
            array('nombre' => 'tickets', 'titulo' => $i18n_funciones['tickets']),
        ));

        // Tablas de productos
        marcas($i18n[195], '50', array(
            array('nombre' => 'productos', 'titulo' => $i18n_funciones['productos']),
            array('nombre' => 'listas', 'titulo' => $i18n_funciones['listas']),
            array('nombre' => 'depositos', 'titulo' => $i18n_funciones['depositos']),
            array('nombre' => 'traslados', 'titulo' => $i18n_funciones['traslados']),
        ));
        salto_linea();
        bajo_linea();

        // Tablas de clientes y proveedores
        marcas($i18n[196], '50', array(
            array('nombre' => 'clientes', 'titulo' => $i18n_funciones['clientes'], 'ayuda_puntual' => $i18n[203]),
            array('nombre' => 'ventas', 'titulo' => $i18n_funciones['ventas'], 'ayuda_puntual' => $i18n[202]),
            array('nombre' => 'ventaspagos', 'titulo' => $i18n_funciones['ventaspagos'], 'ayuda_puntual' => $i18n[201].$i18n[202]),
            array('nombre' => 'proveedores', 'titulo' => $i18n_funciones['proveedores'], 'ayuda_puntual' => $i18n[203]),
            array('nombre' => 'compras', 'titulo' => $i18n_funciones['compras'], 'ayuda_puntual' => $i18n[202]),
            array('nombre' => 'compraspagos', 'titulo' => $i18n_funciones['compraspagos'], 'ayuda_puntual' => $i18n[201].$i18n[202]),
        ));

        // Tablas de cajas
        marcas($i18n[200], '50', array(
            array('nombre' => 'cajas', 'titulo' => $i18n_funciones['cajas'], 'ayuda_puntual' => $i18n[204]),
            array('nombre' => 'cheques', 'titulo' => $i18n_funciones['cheques']),
            array('nombre' => 'retenciones', 'titulo' => $i18n_funciones['retenciones']),
        ));
        salto_linea();
        bajo_linea();

        // Tablas de categorias
        marcas($i18n[198], '50', array(
            array('nombre' => 'categorias_bienes', 'titulo' => $i18n_funciones['categorias_bienes']),
            array('nombre' => 'categorias_cajas', 'titulo' => $i18n_funciones['categorias_cajas']),
            array('nombre' => 'categorias_clientes', 'titulo' => $i18n_funciones['categorias_clientes']),
            array('nombre' => 'categorias_comunicaciones', 'titulo' => $i18n_funciones['categorias_comunicaciones']),
            array('nombre' => 'categorias_conceptos', 'titulo' => $i18n_funciones['categorias_conceptos']),
            array('nombre' => 'categorias_conocimientos', 'titulo' => $i18n_funciones['categorias_conocimientos']),
            array('nombre' => 'categorias_localidades', 'titulo' => $i18n_funciones['categorias_localidades']),
            array('nombre' => 'categorias_proveedores', 'titulo' => $i18n_funciones['categorias_proveedores']),
            array('nombre' => 'categorias_rubros', 'titulo' => $i18n_funciones['categorias_rubros']),
            array('nombre' => 'categorias_servicios', 'titulo' => $i18n_funciones['categorias_servicios']),
            array('nombre' => 'categorias_tributos', 'titulo' => $i18n_funciones['categorias_tributos']),
            array('nombre' => 'categorias_ventas', 'titulo' => $i18n_funciones['categorias_ventas']),
            array('nombre' => 'perfiles', 'titulo' => $i18n_funciones['perfiles']),
            array('nombre' => 'plantillas', 'titulo' => $i18n_funciones['plantillas']),
            array('nombre' => 'smtps', 'titulo' => $i18n_funciones['smtps']),
            array('nombre' => 'tienda', 'titulo' => $i18n_funciones['tienda']),
            array('nombre' => 'extrasxmodulos', 'titulo' => $i18n_funciones['extrasxmodulos']),
        ));
    }
    contenido_fin();

    entrada('hidden', 'idempresa', false, $idempresa);
    botones(array(array('valor' => $i18n_funciones[156])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">

    function validacion_saas_vaciar()
    {
        return confirma('<?=$i18n[197]?>');
    };
</script>
