<?php

switch ($boton) {

    case $i18n[1]: // Aceptar
        $ticket = array_sql(consulta_sql("SELECT idempresa, idusuario, idusuario_saas, empresa_nombre, usuario_nombre, titulo FROM saas_tickets WHERE idticket = '$id'"));
        $datos = recibir_matriz(array(
        array('largo', 'texto'),
        array('texto', 'titulo'),
        array('entero', 'estado'),
        array('entero', 'idusuario'),
        array('texto', 'mensaje'),
        array('texto', 'destacado')
    ));

        consulta_sql("UPDATE saas_tickets SET
            titulo = '{$datos['titulo']}',
            estado = '{$datos['estado']}',
            idusuario_saas = '{$datos['idusuario']}'
            WHERE idticket = '$id'");
        consulta_sql("UPDATE tickets SET
            titulo = '{$datos['titulo']}',
            estado = '{$datos['estado']}'
            WHERE idticket = '{$id}'", $ticket['idempresa']);

        if (strip_tags(str_replace(array('&nbsp;', ' ', '\n', '\r'), '', $datos['texto']))) {
            $fecha = date("Y-m-d H:i:s");
            consulta_sql("INSERT INTO saas_textosxtickets (idticket, idusuario, fecha, texto) VALUES ('".$id."', '".$_SESSION['usuario_idusuario']."', '".$fecha."', '".$datos['texto']."')");
            consulta_sql("INSERT INTO textosxtickets (idticket, idusuario, fecha, texto) VALUES ('".$id."', '".$_SESSION['usuario_idusuario']."', '".$fecha."', '".$datos['texto']."')", $ticket['idempresa']);

            if ($datos['mensaje'])
                consulta_sql("INSERT INTO mensajes SET
                    idusuario = '{$ticket['idusuario']}',
                    tipo = 'Consulta',
                    texto = '".$datos['mensaje']."',
                    idremitente = '$id',
                    destacado = '".$datos['destacado']."',
                    fecha = NOW()",
                    $ticket['idempresa']);
        }

        // Aviso por mail si no es el mismo usuario que está respondiendo
        if ($datos['idusuario'] != $_SESSION['usuario_idusuario']) {
            switch ($datos['idusuario']) {
                case 3: $mail_soporte = MAIL_DESARROLLO; break;
                case 196: $mail_soporte = MAIL_ADMIN; break;
                default: $mail_soporte = MAIL_INFO; break;
            }
            $mail = 'Consulta de ayuda modificada por soporte interno:<br>
                N° empresa: '.$ticket['idempresa'].'<br>
                Empresa: '.$ticket['empresa_nombre'].'<br>
                N° usuario: '.$ticket['idusuario'].'<br>
                Usuario: '.$ticket['usuario_nombre'].'<br>
                Fecha: '.$fecha.'<br>
                Asunto: '.$datos['titulo'].'<br>
                Texto:<br>
                '.$datos['texto'].'<br>
                <br>
                <a href="'.URL_SAAS.'/saas.php?a=modticket&id='.$id.'">Ver consulta</a><br>';
            email_queue(MAIL_SERVIDOR, $mail_soporte, 'Consulta de ayuda Nº '.$id.' modificada', $mail);
        }

        ir_atras();
        break;

    case $i18n[3]: // Cancelar
        ir_atras();
        break;

    default:
        $ticket = array_sql(consulta_sql("SELECT * FROM saas_tickets WHERE idticket='".$id."' LIMIT 1"));
        $datos['mensaje'] = $i18n[25];
        break;

}

$resultado_sql = consulta_sql("SELECT saas_textosxtickets.*, usuarios.nombre AS usuario FROM saas_textosxtickets LEFT JOIN usuarios ON saas_textosxtickets.idusuario=usuarios.idusuario WHERE idticket='".$id."' ORDER BY fecha");

ventana_inicio($i18n[7].$id, '100', array(
    array('url' => 'saas.php?a=verticket&id='.$id, 'a' => 'ver', 'title' => $i18n[11]),
    array('tipo' => 'ajax', 'url' => 'saas.php?a=bajaticket&id='.$id, 'a' => 'baja', 'title' => $i18n[14], 'opciones' => 'onclick="return confirma('."'$i18n[15]'".')"')));
{
    contenido_inicio();
    {
        entrada('texto', 'titulo', $i18n[10], $ticket['titulo'], '50', '200');
        $temp_estado = array(
            array('id' => 'abierto', 'valor' => $i18n['abierto']),
            array('id' => 'cerrado', 'valor' => $i18n['cerrado']),
            array('id' => 'pausado', 'valor' => $i18n['pausado']),
            array('id' => 'pendiente', 'valor' => $i18n['pendiente']),
            array('id' => 'desarrollo', 'valor' => $i18n['desarrollo']),
            // array('id' => 'entendido', 'valor' => $i18n['entendido']),
            );
        selector_array('estado', $i18n[13], $ticket['estado'], '25', $temp_estado);
        selector('idusuario', $i18n[98], $ticket['idusuario_saas'], '25', 'usuarios', 'nombre', false, true, true);
        salto_linea();bajo_linea();
        texto('texto', $i18n[44], $ticket['idempresa'], '50');
        texto('texto', $i18n[84], $ticket['idusuario'], '50');
        texto('texto', $i18n[18], $ticket['empresa_nombre'], '50');
        texto('texto', $i18n[21], $ticket['usuario_nombre'], '50');
        texto('texto', $i18n[19], $ticket['empresa_telefonos'], '50');
        texto('texto', $i18n[22], $ticket['usuario_telefonos'], '50');
        texto('texto', $i18n[20], $ticket['empresa_mail'], '50');
        texto('texto', $i18n[23], $ticket['usuario_mail'], '50');
    }
    contenido_fin();

    contenido_inicio($i18n[107]);
    {
        while ($textoxticket = array_sql($resultado_sql)) {
            $temp_titulo = '['.mostrar_fecha('fechayhora', $textoxticket['fecha']).']&nbsp;&nbsp;';
            if ($textoxticket['usuario'])
                $temp_titulo.= $textoxticket['usuario'];
            else
                $temp_titulo.= $ticket['usuario_nombre'];
            observacion($temp_titulo, $textoxticket['texto']);
        }
    }
    contenido_fin();

    contenido_inicio($i18n[215]);
    {
        $resultado_sql = consulta_sql("SELECT * FROM plantillas");
        if (contar_sql($resultado_sql)) {
            echo "<script>var plantillas = [];";
            $array_selector_opciones = array(
                array('id' => '', 'valor' => 'Seleccione una plantilla para el texto de la respuesta'),
            );
            while ($plantilla = array_sql($resultado_sql)) {
                $array_selector_opciones[] = array('id' => $plantilla['idplantilla'], 'valor' => $plantilla['asunto']);
                echo "plantillas[" . $plantilla['idplantilla'] . "] = '" . str_replace(array("\r", "\n", "'"), "", $plantilla['texto']) . "';";
            }
            echo "</script>";
            selector_array('idplantilla', 'Plantillas', false, '50', $array_selector_opciones);
            entrada('texto', 'mensaje', $i18n[55], $datos['mensaje'], '40', '250');
            marcas($i18n[214], '10', array(array('nombre' => 'destacado', 'titulo' => '', 'valor' => $datos['destacado'])));
        }
        area('texto', '['.mostrar_fecha('fechayhora', date("Y-m-d H:i")).']&nbsp;&nbsp;'.$_SESSION['usuario_nombre'].':', $datos['texto']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[1]), array('valor' => $i18n[3])));
}
ventana_fin();
?>
<script type="text/javascript" charset="utf-8">
$("select[name=idplantilla]").change(function () {
    var idplantilla = $("select[name=idplantilla] option:selected").val();
    console.log("idplantilla:"+idplantilla);
    console.log("texto:"+plantillas[idplantilla]);
    if (idplantilla)
        $("textarea[name='texto']").setCode(plantillas[idplantilla]);
    else
        $("textarea[name='texto']").setCode("");
});
</script>
