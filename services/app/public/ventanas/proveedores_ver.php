<?php

switch ($boton) {
    case $i18n[304]: // Procesar atenciones
        $datos = recibir_matriz(array(
        array('texto', 'eliminar_compras'),
        array('texto', 'atencion_compras')
    ));

        if ($_SESSION['perfil_compras_mod'] && $datos['atencion_compras']) {
            consulta_sql("UPDATE compras SET estado='anulado' WHERE idproveedor = '$id' AND estado='abierto'");
        }

        if ($_SESSION['perfil_compras_mod'] && $datos['eliminar_compras']) {
            $array_sql = array_all_sql(consulta_sql(
                "SELECT idcompra
                FROM compras
                WHERE estado = 'abierto' AND idproveedor = '$id'"));
            foreach ($array_sql as $temp_array_sql) {
                $idcompras[] = $temp_array_sql['idcompra'];
            }
            $compras = implode(', ', $idcompras);
            if ($compras) {
                consulta_sql("DELETE FROM productosxcompras WHERE idcompra IN (".$compras.")");
                consulta_sql("DELETE FROM tributosxcompras WHERE idcompra IN (".$compras.")");
                consulta_sql("DELETE FROM comprasxcompras WHERE idcompra IN (".$compras.")");
                consulta_sql("DELETE FROM comprasxcompras WHERE idrelacion IN (".$compras.")");
                consulta_sql("DELETE FROM compras WHERE idcompra IN (".$compras.")");
                $resultado_sql = consulta_sql("SELECT idcomprapago FROM compraspagos WHERE idcompra IN (".$compras.")");
                while ($pago = array_sql($resultado_sql)) {
                    consulta_sql("UPDATE compraspagos SET idcompra = '0' WHERE idcomprapago = '".$pago['idcomprapago']."' LIMIT 1");
                    consulta_sql("UPDATE comprasxproveedores SET numero = '' WHERE idtipocompra = '0' AND id = '".$pago['idcomprapago']."' LIMIT 1");
                }
            }
        }
        break;
}

controlar_saldo('proveedores', $id);

$proveedor = array_sql(consulta_sql(
    "SELECT proveedores.*,
        categorias_localidades.nombre AS localidad, categorias_localidades.codigopostal,
        tablas_provincias.nombre AS provincia,
        tablas_condiciones.nombre AS tipoiva,
        categorias_proveedores.nombre AS tipoproveedor,
        categorias_proveedores.iddeposito, depositos.nombre AS deposito,
        saldos.saldo,
        monedas.nombre AS moneda, monedas.simbolo
    FROM proveedores
        LEFT JOIN categorias_localidades ON proveedores.idlocalidad = categorias_localidades.idlocalidad
        LEFT JOIN tablas_provincias ON categorias_localidades.idprovincia = tablas_provincias.idprovincia
        LEFT JOIN tablas_condiciones ON proveedores.idtipoiva = tablas_condiciones.idtipoiva
        LEFT JOIN categorias_proveedores ON proveedores.idtipoproveedor = categorias_proveedores.idtipoproveedor
        LEFT JOIN depositos ON categorias_proveedores.iddeposito = depositos.iddeposito
        LEFT JOIN saldos ON saldos.tiporelacion = 'proveedores' AND proveedores.idproveedor = saldos.idrelacion
        LEFT JOIN monedas ON proveedores.idmoneda = monedas.idmoneda
    WHERE idproveedor = '".$id."' LIMIT 1"));

$proveedor = controlar_moneda('proveedores', $proveedor);

mensajes_efimeros();

ventana_inicio($i18n[35].$proveedor['nombre'], '100', array(
    array('tipo' => 'imagen', 'url' => 'proveedores.php?a=mod&id='.$id, 'a' => 'mod', 'title' => $i18n[165], 'permiso' => 'proveedores_mod'),
    array('tipo' => 'imagen', 'url' => 'proveedores.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[166], 'permiso' => 'proveedores_baja', 'opciones' => 'onclick="return confirma('."'$i18n[167]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[168])));
{
    // Datos de contacto
    contenido_inicio($i18n[36], '50');
    {
        texto('texto', $i18n[41], $proveedor['nombre']);
        texto('texto', $i18n[42], $proveedor['contacto']);
        texto('mail', $i18n[44], $proveedor['mail']);
        texto('texto', $i18n[43], $proveedor['telefonos']);
        texto('texto', $i18n[45], $proveedor['domicilio']);
        texto('texto', $i18n[46], $proveedor['localidad']);
        texto('texto', $i18n[322], $proveedor['provincia']);
        texto('texto', $i18n[323], ($proveedor['codigopostal'] ? $proveedor['codigopostal'] : $i18n[108]));
        texto('texto', $i18n[30], $proveedor['tipoproveedor']);
        texto('texto', $i18n[318], $proveedor['deposito']);
        enlaces('', array(array('valor' => $i18n[92], 'url' => 'ventanas/proveedores_rotulo_exportar.php?id='.$id, 'opciones' => 'target="_blank"')));
    }
    contenido_fin();

    // Datos de facturación
    contenido_inicio($i18n[37], '33');
    {
        texto('texto', $i18n[47], $proveedor['tipoiva']);
        texto('texto', $i18n[48], $proveedor['razonsocial']);
        texto('cuit', $i18n[49], $proveedor['cuit'], 'auto', false, false, false,
            ($proveedor['cuit'] ? ['clipboard' => $proveedor['cuit']] : []));


        if (strlen($proveedor["cuit"]) == 11) {
            texto('url', false, $i18n[110], 'auto', 'https://seti.afip.gob.ar/padron-puc-constancia-internet/ConsultaConstanciaAction.do');
        }
    }
    contenido_fin();

    contenido_inicio($i18n[325], '33');
    {
        texto('titulo', $i18n[330], $proveedor['moneda'].' ('.$proveedor['simbolo'].')');
    }
    contenido_fin();

    // Situación con el proveedor
    contenido_inicio($i18n[297], '33');
    {
        $temp_situacion = array_sql(consulta_sql(" SELECT
            (SELECT SUM(comprasxproveedores.total) FROM comprasxproveedores WHERE idproveedor = '$id' AND idtipocompra > '0') AS facturado,
            (SELECT fecha FROM compras WHERE idproveedor = '$id' ORDER BY fecha ASC LIMIT 1) AS desde,
            (SELECT fecha FROM compras WHERE idproveedor = '$id' ORDER BY fecha DESC LIMIT 1) AS hasta")
        );
        if ($temp_situacion['desde']) {
            texto('fecha', $i18n[300], $temp_situacion['desde']);
            texto('fecha', $i18n[301], $temp_situacion['hasta']);
        }

        texto('moneda', $i18n[299], ($temp_situacion['facturado'] ? $temp_situacion['facturado'] : 0), false, false, false, false, false, $proveedor['simbolo']);
        texto('moneda', $i18n[298], $proveedor['saldo'], false, false, false, false, false, $proveedor['simbolo']);
    }
    contenido_fin();

    // Atención rápida
    // TODO: Convertir toda la atención rápida con ajax
    contenido_inicio($i18n[234]);
    {
        enlaces('', array(array('valor' => $i18n[324], 'url' => 'compras.php?a=alta_auto&idproveedor='.$id.'&idtipocompra=7&pedido_faltantes=1', 'opciones' => '')));
        $mostrar_boton = false;
        if ($_SESSION['modulo_compras'] && $_SESSION['perfil_compras_ver']) {
            $resultado_sql = consulta_sql(
                "SELECT idcompra, numerocompleto
                FROM compras
                WHERE idproveedor = '$id'
                    AND compras.estado='abierto'");
            if (contar_sql($resultado_sql)) {
                $mostrar_boton = true;
                $temp_enlaces = array();
                while ($temp_array = array_sql($resultado_sql)) {
                    $temp_enlaces[] = array('valor' => $temp_array['numerocompleto'], 'url' => 'compras.php?a=ver&id='.$temp_array['idcompra']);
                }
                enlaces($i18n[233], $temp_enlaces);
                if ($_SESSION['perfil_compras_mod']) {
                    marcas('', 'auto', array(array('nombre' => 'atencion_compras', 'titulo' => $i18n[302])));
                    marcas('', 'auto', array(array('nombre' => 'eliminar_compras', 'titulo' => $i18n[303])));
                }
            }
        }
        if ($mostrar_boton)
            botones(array(array('valor' => $i18n[304])));

    }
    contenido_fin();

    extras_ver();

    // Observaciones internas
    if ($proveedor['obsinterna']) {
        contenido_inicio($i18n[60], '100', true, false);
        {
            observacion('', $proveedor['obsinterna']);
        }
        contenido_fin();
    }

}
ventana_fin();
