<?php

$traslado = array_sql(consulta_sql(
    "SELECT *,
        (SELECT nombre FROM depositos WHERE depositos.iddeposito = traslados.iddepositoinicio) AS deposito_inicio,
        (SELECT nombre FROM depositos WHERE depositos.iddeposito = traslados.iddepositofin) AS deposito_fin,
        (SELECT nombre FROM usuarios WHERE usuarios.idusuario = traslados.idusuarioinicio) AS usuario_inicio,
        (SELECT nombre FROM usuarios WHERE usuarios.idusuario = traslados.idusuariofin) AS usuario_fin
    FROM traslados WHERE idtraslado = '$id'"));

switch ($boton) {
    case $i18n_funciones['enviar']:
        if ($traslado['estado'] != 'abierto')
            ir_inicio($i18n[230]);

        $datos = recibir_matriz(array(
        array('entero', 'iddepositoinicio'),
        array('fecha', 'fechainicio'),
        array('largo', 'obsinicio'),
        array('entero', 'iddepositofin')
    ));
        $traslado['iddepositoinicio'] = $datos['iddepositoinicio'];
        $traslado['fechainicio'] = $datos['fechainicio'];
        $traslado['obsinicio'] = $datos['obsinicio'];
        $traslado['iddepositofin'] = $datos['iddepositofin'];

        if (!$datos['fechainicio'] || $datos['fechainicio'] == '0000-00-00 00:00') {
            mensajes_alta($i18n[235]);
            break;
        }
        if (!$datos['iddepositoinicio'] || !$datos['iddepositofin']) {
            mensajes_alta($i18n[236]);
            break;
        }
        if ($datos['iddepositoinicio'] == $datos['iddepositofin']) {
            mensajes_alta($i18n[237]);
            break;
        }

        $productosxtraslados = array_all_sql(consulta_sql(
            "SELECT pxt.*, p.controlarstock, p.stocknegativo, p.nombre,
                stock.stockactual, tablas_unidades.nombre AS unidad,
                (SELECT stock.stockactual FROM stock WHERE idproducto = p.idproducto AND iddeposito = '".$datos['iddepositofin']."') AS stockdepositofin
            FROM productosxtraslados AS pxt
                LEFT JOIN productos AS p ON pxt.idproducto = p.idproducto
                LEFT JOIN tablas_unidades ON p.idunidad = tablas_unidades.idunidad
                LEFT JOIN stock ON stock.idproducto = p.idproducto
                    AND stock.iddeposito =
                    (SELECT iddepositoinicio FROM traslados WHERE idtraslado = '$id')
            WHERE idtraslado = '$id'"));

        // Controlo stock en cada producto
        foreach ($productosxtraslados as $productoxtraslado) {
            //Inicializo stock de ese producto en ese depósito
            if ($productoxtraslado['controlarstock'] && $productoxtraslado['stockdepositofin'] === NULL){
                inicializar_deposito($productoxtraslado['idproducto'], $datos['iddepositofin']);
            }
            //Si es NULL trae cero esta query cuando es iddepositoinicio... lo dejo abierto a feedback
            if ($productoxtraslado['controlarstock'] && $productoxtraslado['iddepositoinicio'] == 0){
                inicializar_deposito($productoxtraslado['idproducto'], $datos['iddepositoinicio']);
            }

            if ($productoxtraslado['controlarstock']
                && !$productoxtraslado['stocknegativo']
                && $productoxtraslado['stockactual'] < $productoxtraslado['cantidad']) {
                mensajes_alta(htmlentities(str_replace('{stockactual}', $productoxtraslado['stockactual'].' '.$productoxtraslado['unidad'], $i18n[241]).': '.$productoxtraslado['nombre'], ENT_QUOTES));
                break(2);
            }
        }

        // Guardar movimiento en el historial de productos y descontar stock en depósito origen
        foreach ($productosxtraslados as $productoxtraslado) {
            consulta_sql("UPDATE stock
                SET stockactual = stockactual - '".$productoxtraslado['cantidad']."'
                WHERE idproducto = '".$productoxtraslado['idproducto']."'
                AND iddeposito = '".$datos['iddepositoinicio']."'
            ");
            log_productos($productoxtraslado['idproducto'], 'traslado', $id);
        }

        // Actualizar datos en el traslado
        guardar_sql('traslados', array(
            'estado' => 'enviado',
            'iddepositoinicio' => $datos['iddepositoinicio'],
            'fechainicio' => fecha_sql($datos['fechainicio']),
            'idusuarioinicio' => $_SESSION['usuario_idusuario'],
            'obsinicio' => escape_sql($datos['obsinicio']),
            'iddepositofin' => $datos['iddepositofin'],
        ), $id);

        actualizar_controles(array(
            'ultimodepositoorigen' => $datos['iddepositoinicio'],
        ));

        ir_ahora('productos.php?a=traslados_ver&id='.$id);
        break;

    case $i18n_funciones['recibir']:
        if ($traslado['estado'] != 'enviado')
            ir_inicio($i18n[230]);

        $datos = recibir_matriz(array(
        array('fecha', 'fechafin'),
        array('largo', 'obsfin')
    ));
        $traslado['fechafin'] = $datos['fechafin'];
        $traslado['obsfin'] = $datos['obsfin'];

        if (!$datos['fechafin'] || $datos['fechafin'] == '0000-00-00 00:00') {
            mensajes_alta($i18n[235]);
            continue;
        }

        // Actualizar datos
        guardar_sql('traslados', array(
            'estado' => 'recibido',
            'fechafin' => ($datos['fechafin'] ? fecha_sql($datos['fechafin']) : fecha_sql('ahora')),
            'idusuariofin' => $_SESSION['usuario_idusuario'],
            'obsfin' => escape_sql($datos['obsfin']),
        ), $id);

        // Guardar movimiento en el historial de productos y umar stock en depósito destino
        $productosxtraslados = array_all_sql(consulta_sql("SELECT * FROM productosxtraslados WHERE idtraslado = '$id'"));
        foreach ($productosxtraslados as $productoxtraslado) {
            consulta_sql("UPDATE stock
                SET stockactual = stockactual + '".$productoxtraslado['cantidad']."'
                WHERE idproducto = '".$productoxtraslado['idproducto']."'
                AND iddeposito = (SELECT iddepositofin FROM traslados WHERE idtraslado = '".$id."')
            ");
            log_productos($productoxtraslado['idproducto'], 'traslado', $id);
        }

        ir_ahora('productos.php?a=traslados_ver&id='.$id);
        break;

    case $i18n_funciones['dejar_abierto']:
        if ($traslado['estado'] != 'abierto')
            ir_inicio($i18n[230]);

        $datos = recibir_matriz(array(
        array('entero', 'iddepositoinicio'),
        array('fecha', 'fechainicio'),
        array('largo', 'obsinicio'),
        array('entero', 'iddepositofin')
    ));
        // Actualizar datos
        guardar_sql('traslados', array(
            'iddepositoinicio' => $datos['iddepositoinicio'],
            'fechainicio' => fecha_sql($datos['fechainicio']),
            'idusuarioinicio' => $_SESSION['usuario_idusuario'],
            'obsinicio' => escape_sql($datos['obsinicio']),
            'iddepositofin' => $datos['iddepositofin'],
        ), $id);

        actualizar_controles(array(
            'ultimodepositoorigen' => $datos['iddepositoinicio'],
        ));

        ir_atras();
        break;

    case $i18n_funciones['volver']:
        ir_atras();
        break;

    default:
        switch ($traslado['estado']) {
            case 'abierto': $traslado['imagen_estado'] = 'prioridad_1'; break;
            case 'enviado': $traslado['imagen_estado'] = 'prioridad_2'; break;
            case 'recibido': $traslado['imagen_estado'] = 'prioridad_3'; break;
            case 'anulado': $traslado['imagen_estado'] = 'no'; break;
        }
        break;
}

$discrimina = 'traslado';
$numero_comprobante = numero_comprobante('R', 1, $traslado['idtraslado']);
$idlista = campo_sql(consulta_sql("SELECT idlista FROM listas ORDER BY idlista LIMIT 1"));
$depositos_array = array_all_sql(consulta_sql(
    "SELECT iddeposito, nombre FROM depositos ORDER BY iddeposito"), 'iddeposito');
foreach ($depositos_array as $deposito) {
    $depositos_inicio[] = ['id' => $deposito['iddeposito'], 'valor' => $deposito['nombre']];
    if ($deposito['iddeposito'] != $traslado['iddepositoinicio'])
        $depositos_fin[] = ['id' => $deposito['iddeposito'], 'valor' => $deposito['nombre']];
}

ventana_inicio($i18n[219].$numero_comprobante, '100', array(
    array('tipo' => 'imagen', 'url' => 'productos.php?a=traslados_ver&id='.$traslado['idtraslado'], 'a' => 'ver', 'title' => $i18n[231]),
    array('tipo' => 'imagen', 'url' => 'productos.php?a=traslados_baja&id='.$traslado['idtraslado'], 'a' => 'anulado', 'title' => $i18n[223], 'opciones' => 'onclick="return confirma('."'".$i18n[245]."'".')"'),
    array('tipo' => 'exportar', 'url' => 'traslados.php?a=exportar&id='.$traslado['idtraslado'], 'a' => 'exportar', 'title' => $i18n[224])
));
{
    // Datos básicos
    contenido_inicio($i18n[18], 33);
    {
        texto('texto', $i18n[220], $numero_comprobante);
        texto('texto', $i18n[214], $i18n[$traslado['estado']], 'auto', false, $traslado['imagen_estado']);
    }
    contenido_fin();

    // Datos de envío
    contenido_inicio($i18n[225], 33);
    {
        entrada('hidden', 'iddeposito_origen', false, $traslado['iddepositoinicio']);
        if ($traslado['estado'] == 'abierto') {
            selector_array('iddepositoinicio', $i18n[217], $traslado['iddepositoinicio'], 'auto', $depositos_inicio, false, 'onchange="guardar_sucursal('."'iddepositoinicio', 'traslados', '$id', $(this).val()".')"');
            entrada('fechayhora', 'fechainicio', $i18n[215], $traslado['fechainicio']);
        } else {
            texto('texto', $i18n[217], $depositos_array[$traslado['iddepositoinicio']]['nombre']);
            texto('fechayhora', $i18n[215], $traslado['fechainicio']);
            texto('texto', $i18n[232], $traslado['usuario_inicio']);
        }
    }
    contenido_fin();

    // Datos de recepción
    contenido_inicio($i18n[226], 33);
    {
        if ($traslado['estado'] == 'abierto') {
            selector_array('iddepositofin', $i18n[218], $traslado['iddepositofin'], 'auto', $depositos_fin);

        } else if ($traslado['estado'] == 'enviado') {
            texto('iddepositofin', $i18n[218], $depositos_array[$traslado['iddepositofin']]['nombre']);
            entrada('fechayhora', 'fechafin', $i18n[216], ($traslado['fechafin'] != '0000-00-00 00:00:00'
                ? $traslado['fechafin']
                : 'ahora'));

        } else {
            texto('iddepositofin', $i18n[218], $depositos_array[$traslado['iddepositofin']]['nombre']);
            texto('fechayhora', $i18n[216], $traslado['fechafin']);
            texto('texto', $i18n[233], $traslado['usuario_fin']);
        }
    }
    contenido_fin();

    // Productos
    contenido_inicio($i18n[80], '100', false, false, false, ($_SESSION['mobile'] ? 'style="overflow-x: scroll;"' : ''));
    {
        if ($traslado['estado'] == 'abierto') {
            seleccionador_productos('productos', $discrimina, $idlista, $traslado['iddepositoinicio']);

        } else {
            comprobantes_mostrar_titulo_productosxcomprobantes($discrimina);

            $resultado_sql = consulta_sql(
                "SELECT pxt.*, p.codigo, p.nombre,
                    tablas_unidades.idunidad, tablas_unidades.nombre AS unidad,
                    monedas.simbolo
                FROM productosxtraslados AS pxt
                    LEFT JOIN productos AS p ON pxt.idproducto = p.idproducto
                    LEFT JOIN tablas_unidades ON p.idunidad = tablas_unidades.idunidad
                    LEFT JOIN monedas ON p.idmoneda = monedas.idmoneda
                WHERE idtraslado = '$id'
                ORDER BY idproductoxtraslado");

            if (contar_sql($resultado_sql)) {
                while ($productoxtraslado = array_sql($resultado_sql)) {
                    comprobantes_ver_productoxcomprobante($productoxtraslado, $discrimina);
                }

            } else {
                linea_inicio();
                {
                    texto('texto', false, $i18n[229]);
                }
                linea_fin();
            }

        }
    }
    contenido_fin();

    // Observaciones
    contenido_inicio($i18n[86]);
    {
        if ($traslado['estado'] == 'abierto') {
            area('obsinicio', $i18n[227], $traslado['obsinicio']);
        } else if ($traslado['obsinicio']) {
            observacion($i18n[227], $traslado['obsinicio']);
        }

        if ($traslado['estado'] == 'enviado') {
            area('obsfin', $i18n[228], $traslado['obsfin']);
        } else if ($traslado['estado'] != 'abierto' && $traslado['obsfin']) {
            observacion($i18n[228], $traslado['obsfin']);
        }
    }
    contenido_fin();

    switch ($traslado['estado']) {
        case 'abierto':
            botones(array(
                array('valor' => $i18n_funciones['enviar']),
                array('valor' => $i18n_funciones['dejar_abierto']),
            ));
            break;

        case 'enviado':
            $temp_botones = array();
            if ($_SESSION['perfil_productos_traslados']) {
                $temp_botones[] = array('valor' => $i18n_funciones['recibir']);
            }
            $temp_botones[] = array('valor' => $i18n_funciones['volver']);

            botones($temp_botones);
            break;

        case 'recibido':
        case 'anulado':
            botones(array(
                array('valor' => $i18n_funciones['volver']),
            ));
            break;
    }
}
ventana_fin();
