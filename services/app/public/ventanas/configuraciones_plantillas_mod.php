<?php

switch ($boton) {

    case $i18n_funciones[28]: // Modificar
        $datos = recibir_matriz(array(
        array('texto', 'asunto_nombre'),
        array('texto', 'asunto'),
        array('largo', 'texto')
    ));

        consulta_sql("UPDATE plantillas SET asunto_nombre = '".$datos['asunto_nombre']."',  asunto = '".$datos['asunto']."',texto = '".$datos['texto']."' WHERE idplantilla='".$id."' LIMIT 1");
        extras_alta();
        ir_ahora('configuraciones.php?a=plantillas');
        break;

    case $i18n_funciones[23]: // Cancelar
        ir_atras();
        break;

    default:
        $datos = array_sql(consulta_sql("SELECT asunto_nombre, asunto, texto FROM plantillas WHERE idplantilla = '".$id."' LIMIT 1"));
        $mp = array_sql(consulta_sql("SELECT MP_estado, MP_client_id, MP_client_secret FROM tienda WHERE idtienda = 1 LIMIT 1"));
        break;

}

ventana_inicio($i18n[451] . " " . $datos['asunto'], '100', array(
    array('url' => 'configuraciones.php?a=verplantilla&id='.$id, 'a' => 'ver', 'title' => $i18n[455]),
    array('url' => 'configuraciones.php?a=bajaplantilla&id='.$id, 'a' => 'baja', 'title' => $i18n[449], 'opciones' => 'onclick="return confirma('."'$i18n[457]'".')"')));
{
    contenido_inicio();
    {
        entrada('hidden', 'mp_estado', '', $mp['MP_estado']);
        entrada('hidden', 'mp_client_id', '', ($mp['MP_client_id'] ? '1' : ''));
        entrada('hidden', 'mp_client_secret', '', ($mp['MP_client_secret'] ? '1' : ''));
        entrada('texto', 'asunto', $i18n[24], $datos['asunto'], '100', '200');
        area('texto', $i18n[444], $datos['texto'], 'auto', false, '', true, ($mp['MP_estado'] && $mp['MP_client_id'] && $mp['MP_client_secret'] ? true : false));
        marcas('', 'auto', array(array('nombre' => 'asunto_nombre', 'titulo' => $i18n[555], 'valor' => $datos['asunto_nombre'])));
    }
    contenido_fin();

    if (!$mp['MP_estado'] || !$mp['MP_client_secret']) {
        contenido_inicio();
        {
            texto('italica', false, $i18n[617], 'auto', false, 'pago');
            enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_MP_autorizar', 'id' => $id, 'valor' => $i18n[618])));
        }
        contenido_fin();
    }

    botones(array(array('valor' => $i18n_funciones[28]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();

$resultado_sql = consulta_sql("SELECT * FROM tablas_tags ORDER BY FIELD(modulo, 'ventas', 'clientes', 'servicios', 'compras'), idtag ASC");
$opciones = "";
while ($tag = array_sql($resultado_sql)) {
    $opciones .= "<option value='{{".$tag['tag']."}}'>En ".$tag['modulo'].': '.$tag['nombre']."</option>";
}

?>
<script type="text/javascript">
function agregar_tags()
{
    $("#redactor_tag").html("<?=$opciones?>");
};
</script>