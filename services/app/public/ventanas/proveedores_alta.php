<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('texto', 'nombre'),
        array('texto', 'contacto'),
        array('texto', 'telefonos'),
        array('texto', 'domicilio'),
        array('mail', 'mail'),
        array('entero', 'idtipoproveedor'),
        array('entero', 'idlocalidad'),
        array('entero', 'idtipoiva'),
        array('texto', 'razonsocial'),
        array('largo', 'obsinterna'),
        array('cuit', 'cuit'),
        array('entero', 'idmoneda')
    ));
    // TODO: Eliminar esta línea al arreglar el validador de cuit
    $datos['cuit'] = str_replace('-', '', $datos['cuit']);
    $tipo = recibir_variable('texto', 'tipo', false);
} else {
    $datos = array('idtipoiva' => 0);
    if(recibir_variable('texto', 'proveedores_input', false)){
        $tipo = 'modal';
        $datos['nombre'] = recibir_variable('texto', 'proveedores_input', false);
    }
}

switch ($boton) {
    case $i18n[22]: //Agregar
        if ($datos['mail'] && !filter_var($datos['mail'], FILTER_VALIDATE_EMAIL)) {
            mensajes_alta($i18n[65]);
        }

        if ($datos['cuit'] &&
            contar_sql(consulta_sql(
                "SELECT idproveedor, nombre FROM proveedores WHERE cuit = '{$datos['cuit']}' LIMIT 1"))) {

            $existe_proveedor = array_sql($existe_sql);
            mensajes_alta($i18n[308].'<a href=proveedores.php?a=ver&id='.$existe_proveedor['idproveedor'].' target=_blank>'.$existe_proveedor['nombre'].'</a>');
        }
        if($datos['cuit'] && !$datos['razonsocial']){
            mensajes_alta($i18n[321]);
        }
        consulta_sql("INSERT INTO proveedores SET
            nombre = '".$datos['nombre']."',
            contacto = '".$datos['contacto']."',
            telefonos = '".$datos['telefonos']."',
            domicilio = '".$datos['domicilio']."',
            mail = '".$datos['mail']."',
            idtipoproveedor = '".$datos['idtipoproveedor']."',
            idmoneda = '".$datos['idmoneda']."',
            idlocalidad = '".$datos['idlocalidad']."',
            idtipoiva = '".$datos['idtipoiva']."',
            razonsocial = '".$datos['razonsocial']."',
            cuit = '".$datos['cuit']."',
            obsinterna = '".$datos['obsinterna']."'
            ");

        $id = id_sql();
        insertar_saldo('proveedores', $id);
        fullsearch_insert('proveedores', $id);
        extras_alta();

        if ($tipo == 'modal') {
            $script = '
                $("#proveedores_seleccionado").html(' . "'" . '<div class="buscar_botones"><a href="#" id="proveedores_cambiar"><img style="margin-top: -5px;" src="' . $_SESSION['servidor_url'] . 'estilos/estilo_' . $_SESSION['usuario_idestilo'] . '/images/cambiar.png" title="' . $i18n_funciones[19] . '"></a></div>' . $datos['nombre'] . "'" . ');
                $("#proveedores_id").val("' . $id . '");
            ';
            modal_cerrar($script);
        } else {
            ir_ahora('proveedores.php?a=ver&id='.$id);
        }
        break;

    case $i18n[205]: // Cancelar
        if ($tipo == 'modal') {
            modal_cerrar('$("#proveedores_cambiar").click();');
        } else {
            ir_atras();
        }
        break;
}

ventana_inicio($i18n[26]);
{
    // Datos de contacto
    contenido_inicio($i18n[68]);
    {
        entrada('hidden', 'tipo', '', $tipo); //si es modal o no
        entrada('texto', 'nombre', $i18n[28], $datos['nombre'], '50', '60');
        entrada('texto', 'contacto', $i18n[29], $datos['contacto'], '25', '150');
        selector('idtipoproveedor', $i18n[30], $datos['idtipoproveedor'], '25', 'categorias_proveedores', 'nombre', true, true, true);
        entrada('email', 'mail', $i18n[33], $datos['mail'], '25', '320');
        entrada('texto', 'telefonos', $i18n[31], $datos['telefonos'], '25', '150');
        entrada('texto', 'domicilio', $i18n[32], $datos['domicilio'], '25', '150');
        entrada('hidden', 'idlocalidad', false, $datos['idlocalidad']);
        entrada('texto', 'localidad', $i18n[78], $datos['localidad'], '25', '150', false, 'onFocus="seleccionarLocalidades();"', 'categorias_localidades');
    }
    contenido_fin();

    // Datos de facturación
    contenido_inicio($i18n[69]);
    {
        entrada('cuit', 'cuit', $i18n[49], $datos['cuit'], '50', false, false, 'autocomplete="off"');
        texto('texto', $i18n[86], '<br>'.$datos['razonsocial'].' ', '25', false, false, false, 'id="razonsocial_texto"'); // Pongo un espacio para que aparezca siempre
        texto('texto', $i18n[79], '<br>'.$datos['tipoiva'].' ', '25', false, false, false, 'id="tipoiva_texto"'); // Pongo un espacio para que aparezca siempre

        entrada('hidden', 'idtipoiva', false, $datos['idtipoiva']);
        entrada('hidden', 'razonsocial', false, $datos['razonsocial']);

        salto_linea();

        bloque_inicio('afip_responde');
        {
            texto('italica', false, $i18n[309], '50', false, 'info');
            enlaces(false, array(
                array('tipo' => 'modal', 'modulo' => 'clientes',
                    'url' => 'afip_no_responde', 'id' => $id, 'valor' => $i18n[310])
                ), '50');
        }
        bloque_fin();
        bloque_inicio('afip_validando', 'style="display: none;"');
        {
            texto('italica', false, $i18n[312], '100', false, 'ajax');
        }
        bloque_fin();
        bloque_inicio('afip_no_responde', 'style="display: none;"');
        {
            texto('italica', false, $i18n[311], '50', false, 'alerta');
            enlaces(false, array(
                array('tipo' => 'modal', 'modulo' => 'clientes',
                    'url' => 'afip_no_responde', 'id' => $id, 'valor' => $i18n[310])
                ), '50');
        }
        bloque_fin();
    }
    contenido_fin();

    contenido_inicio($i18n[325]);
    {
        if ($_SESSION['modulo_multimoneda']) {
            selector('idmoneda', $i18n[325], 1, '25', 'monedas', 'idmoneda', false, false);
            texto('italica', false, $i18n[327], false, false, 'info');
        } else {
            texto('titulo', $i18n[330], $i18n[326], '30');
            entrada('hidden', 'idmoneda', '', 1);
        }
    }
    contenido_fin();

    extras();

    // Observaciones
    contenido_inicio($i18n[221], '100', true, false);
    {
        area('obsinterna', $i18n[90], $datos['obsinterna']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[22]), array('valor' => $i18n[205])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    // Necesito la tabla condiciones en JS
    var tablas_condiciones = <?php echo tabla_json('tablas_condiciones'); ?>;

    function validacion_proveedores_alta(boton)
    {
        var mail = $("#marco_proveedores_alta input[name='mail']").val();

        if (boton == "<?php echo $i18n[22]; ?>" && $("#marco_proveedores_alta input[name='nombre']").val() == '') {
            <?php script_validacion_flotante("alerta", $i18n[24], "input[name='nombre']"); ?>
            if (mail == '') {
                <?php script_validacion_flotante("informacion", $i18n[25], "input[name='mail']"); ?>
            }
            return false;

        } else if (boton == '<?php echo $i18n[22]; ?>' && mail != '' && !$("input[name='mail']").valid()) {
            <?php script_validacion_flotante("alerta", $i18n[65], "input[name='mail']"); ?>
            return false;

        } else {
            return true;
        }
    };
</script>
