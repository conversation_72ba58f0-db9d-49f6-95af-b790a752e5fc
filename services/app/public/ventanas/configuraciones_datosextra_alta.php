<?php
switch ($boton) {

    case $i18n[78]: //Agregar
        $datos = recibir_matriz(array(
        array('texto', 'nombre'),
        array('texto', 'modulo'),
        array('texto', 'tipo')
    ));

        if (!$datos['nombre']) {
            mensajes_alta($i18n[270]);
        } else {
            consulta_sql("INSERT INTO extrasxmodulos SET
            	nombre = '".$datos['nombre']."',
            	modulo = '".$datos['modulo']."',
            	tipo = '".$datos['tipo']."',
            	buscado = 0,
            	mostrar_en_comprobantes = 0
            ");
            $idextraxmodulo = id_sql();

            if ($datos['tipo'] == 'lista'){
                consulta_sql("INSERT INTO listasxextras SET
                    idextraxmodulo = '".$idextraxmodulo."',
                    nombre = 'Sin especificar'
                ");
                ir_ahora('configuraciones.php?a=datosextramod&id='.$idextraxmodulo);
            }
        }
        ir_atras();

        break;

    case $i18n[66]: //Cancelar
        ir_atras();
        break;

    default:
    	//Ver permisos
    	$modulo = array(
                array('id' => 'bienes', 'valor' => $i18n_funciones['bienes']),
                array('id' => 'clientes', 'valor' => $i18n_funciones['clientes']),
                // array('id' => 'comunicacion', 'valor' => $i18n_funciones['comunicacion']),
                array('id' => 'compras', 'valor' => $i18n_funciones['compras']),
                array('id' => 'conocimientos', 'valor' => $i18n_funciones['conocimientos']),
				array('id' => 'productos', 'valor' => $i18n_funciones['productos']),
                array('id' => 'proveedores', 'valor' => $i18n_funciones['proveedores']),
                array('id' => 'servicios', 'valor' => $i18n_funciones['servicios']),
                array('id' => 'ventas', 'valor' => $i18n_funciones['ventas'])
            );
    	$tipo = array(
    			array('id' => 'texto', 'valor' => $i18n['texto']),
    			array('id' => 'lista', 'valor' => $i18n['lista']),
    		);
        break;

}

ventana_inicio($i18n[597]);
{
    contenido_inicio();
    {
        entrada('texto', 'nombre', $i18n[180], '', '25', '60');
        selector_array('modulo', $i18n[262], '', '20', $modulo);
        selector_array('tipo', $i18n[263], '', '20', $tipo, false, 'onchange="listasxextras()"');
    }
    contenido_fin();

	botones(array(array('valor' => $i18n[78]), array('valor' => $i18n[66])));
}
ventana_fin();

?>

<script>

    function listasxextras()
    {
        if ($("select[name=tipo]").val() == 'lista'){
            if ($("#marco_configuraciones_datosextra_alta input[name='nombre']").val() == '') {
                <?php script_validacion_flotante("alerta", $i18n[119], "input[name='nombre']");?>
                return false;
            } else {
                $('input[value="<?php echo $i18n[78];?>"]').trigger( "click" );
            }
        }
    }

</script>