<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('texto', 'nombrereal'),
        array('texto', 'nombre'),
        array('mail', 'mail'),
        array('texto', 'passi'),
        array('texto', 'passi2'),
        array('mail', 'mail_original'),
        array('texto', 'telefonos'),
        array('entero', 'idperfil'),
        array('entero', 'idestilo'),
        array('largo', 'obsinterna'),
        array('largo', 'texto_rapido_1'),
        array('largo', 'texto_rapido_2'),
        array('largo', 'texto_rapido_3'),
        array('largo', 'texto_rapido_4'),
        array('largo', 'texto_rapido_5')
    ));
} else {
    $datos = array();
}

switch ($boton) {
    case $i18n[28]: // Agregar
        if ($datos['nombrereal'] == '')
            mensajes_alta($i18n[16], 'Alerta');
        elseif ($datos['nombre'] == '')
            mensajes_alta($i18n[17], 'Alerta');
        elseif ($datos['mail'] == '')
            mensajes_alta($i18n[18], 'Alerta');
        elseif ($datos['passi'] == '')
            mensajes_alta($i18n[19], 'Alerta');
        elseif ($datos['passi'] != $datos['passi2'])
            mensajes_alta($i18n[39], 'Alerta');
        elseif (contar_sql(consulta_sql("SELECT idusuario FROM usuarios WHERE mail='".$datos['mail']."' LIMIT 1", 'saasargentina')))
            // No utilizo mensajes_alta porque da error porque se pisan las conexiones
            // mensajes_alta($i18n[32], 'Alerta');
            guardar_sql('mensajes', array(
                'idusuario' => $_SESSION['usuario_idusuario'],
                'tipo' => 'Alerta',
                'texto' => $i18n[32],
            ));

        else {
            $datos['pass'] = $datos['passi'];
            $chars = "abcdefghijkmnopqrstuvwxyz023456789ABCDEFGHIJKMNOPQRSTUVWXYZ";
            $iuu = '';
            for($i = 0; $i < 20; $i++) {
                $iuu .= $chars[rand(0, 58)];
            }

            consulta_sql("INSERT INTO usuarios SET
                idempresa = '".$_SESSION['empresa_idempresa']."',
                mail = '".$datos['mail']."',
                pass = '".md5($datos['pass'])."',
                iuu = '".$iuu."'", 'saasargentina');

            $idusuario = campo_sql(consulta_sql("SELECT idusuario FROM usuarios WHERE mail='".$_POST['mail']."' LIMIT 1", 'saasargentina'), 0);
            guardar_sql('usuarios', array(
                'idusuario' => $idusuario,
                'nombrereal' => $datos['nombrereal'],
                'nombre' => $datos['nombre'],
                'iuu' => $iuu,
                'telefonos' => $datos['telefonos'],
                'mail' => $datos['mail'],
                'idperfil' => $datos['idperfil'],
                'idestilo' => $datos['idestilo'],
                'obsinterna' => $datos['obsinterna'],
            ));
            consulta_sql("INSERT INTO controles (idusuario) VALUES ('".$idusuario."')");

            $mail = 'El usuario '.$_SESSION['usuario_nombre'].' (Nº '.$_SESSION['usuario_idusuario'].') ha agregado al nuevo usuario '.$datos['nombrereal'].' (Nº '.$idusuario.') en la empresa '.$_SESSION['empresa_nombre'].' (Nº '.$_SESSION['empresa_idempresa'].')';
            email_queue(MAIL_SERVIDOR, MAIL_ADMIN, 'Nuevo usuario en SaaS Argentina', $mail);
            ir_atras();
        }
        break;

    case $i18n[31]: // Cancelar
        ir_atras();
        break;
}

mensajes_efimeros();

ventana_inicio($i18n[29]);
{
    //Datos de acceso
    contenido_inicio($i18n[3]);
    {
        entrada('email', 'mail', $i18n[12], $datos['mail'], '33', '320', $i18n[37]);
        entrada('hidden', 'mail_original', '', $datos['mail']);
        entrada('password', 'passi', $i18n[10], '', '33', false, $i18n[38], 'autocomplete="off"');
        entrada('password', 'passi2', $i18n[9], '', '34', false, false, 'autocomplete="off"');
    }
    contenido_fin();

    // Datos básicos
    contenido_inicio($i18n[4], '66');
    {
        entrada('texto', 'nombrereal', $i18n[5], $datos['nombrereal'], '50', '60', $i18n[35]);
        entrada('texto', 'nombre', $i18n[8], $datos['nombre'], '50', '60', $i18n[36]);
        entrada('texto', 'telefonos', $i18n[11], $datos['telefonos'], '50', '50');
        selector('idperfil', $i18n[6], $datos['idperfil'], '50', 'perfiles', 'nombre', false, false, false,
            $_SESSION['perfil_idperfil'] == 1 ? 'disabled="disabled"' : '');
    }
    contenido_fin();

    // Estilo
    contenido_inicio($i18n[13], '33');
    {
        selector('idestilo', $i18n[13], $datos['idestilo'], 'auto', 'tablas_estilos', 'nombre', false, false);
    }
    contenido_fin();

    // Observación interna
    contenido_inicio($i18n[15]);
    {
        area('obsinterna', $i18n[15], $datos['obsinterna']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[28]), array('valor' => $i18n[31])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    function validacion_usuarios_alta(boton)
    {
        var mail = $("#marco_usuarios_alta input[name='mail']").val();
        if (boton == '<?php echo $i18n[28]; ?>' && (mail == '' || !$("input[name='mail']").valid())) {
            <?php script_validacion_flotante("alerta", $i18n[18], "input[name='mail']"); ?>
            return false;

        } else
            return true;
    };
</script>
