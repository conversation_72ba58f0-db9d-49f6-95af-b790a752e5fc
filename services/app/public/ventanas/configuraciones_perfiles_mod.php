<?php

switch ($boton) {
    case $i18n_funciones[22]: //Aceptar
        $datos = recibir_matriz(array(
        array('texto', 'nombre'),
        array('entero', 'configuraciones_empresa'),
        array('entero', 'configuraciones_tablas'),
        array('entero', 'configuraciones_cuentas'),
        array('entero', 'usuarios_mod'),
        array('entero', 'usuarios_todos'),
        array('entero', 'tareas_alta_todos'),
        array('entero', 'tareas_ver_todos'),
        array('entero', 'clientes_ver'),
        array('entero', 'clientes_alta'),
        array('entero', 'clientes_mod'),
        array('entero', 'clientes_mod_todos'),
        array('entero', 'clientes_baja'),
        array('entero', 'clientes_informes'),
        array('entero', 'clientes_herramientas'),
        array('entero', 'productos_ver'),
        array('entero', 'productos_alta'),
        array('entero', 'productos_mod'),
        array('entero', 'productos_costo'),
        array('entero', 'productos_traslados'),
        array('entero', 'productos_baja'),
        array('entero', 'productos_informes'),
        array('entero', 'productos_herramientas'),
        array('entero', 'servicios_tiene'),
        array('entero', 'servicios_ver'),
        array('entero', 'servicios_alta'),
        array('entero', 'servicios_mod'),
        array('entero', 'servicios_baja'),
        array('entero', 'servicios_informes'),
        array('entero', 'servicios_herramientas'),
        array('entero', 'servicios_alta_todos'),
        array('entero', 'servicios_mod_todos'),
        array('entero', 'proveedores_ver'),
        array('entero', 'proveedores_alta'),
        array('entero', 'proveedores_mod'),
        array('entero', 'proveedores_baja'),
        array('entero', 'proveedores_informes'),
        array('entero', 'proveedores_herramientas'),
        array('entero', 'ventas_ver'),
        array('entero', 'ventas_alta'),
        array('entero', 'ventas_mod'),
        array('entero', 'ventas_baja'),
        array('entero', 'ventas_informes'),
        array('entero', 'ventas_herramientas'),
        array('entero', 'ventas_mod_todos'),
        array('entero', 'ventas_nuevo'),
        array('entero', 'ventas_mod_precios'),
        array('entero', 'ventas_mod_stock'),
        array('entero', 'ventaspagos_ver'),
        array('entero', 'ventaspagos_alta'),
        array('entero', 'ventaspagos_mod'),
        array('entero', 'ventaspagos_baja'),
        array('entero', 'compras_ver'),
        array('entero', 'compras_alta'),
        array('entero', 'compras_mod'),
        array('entero', 'compras_baja'),
        array('entero', 'compras_informes'),
        array('entero', 'compras_herramientas'),
        array('entero', 'compraspagos_ver'),
        array('entero', 'compraspagos_alta'),
        array('entero', 'compraspagos_mod'),
        array('entero', 'compraspagos_baja'),
        array('entero', 'cajas_ver'),
        array('entero', 'cajas_informes'),
        array('entero', 'cajas_herramientas'),
        array('entero', 'conocimientos_ver'),
        array('entero', 'conocimientos_herramientas'),
        array('entero', 'bienes_ver'),
        array('entero', 'bienes_alta'),
        array('entero', 'bienes_mod'),
        array('entero', 'bienes_baja'),
        array('entero', 'bienes_informes'),
        array('entero', 'bienes_herramientas'),
        array('entero', 'comunicaciones_ver'),
        array('entero', 'comunicaciones_alta'),
        array('entero', 'comunicaciones_mod'),
        array('entero', 'comunicaciones_baja'),
        array('entero', 'comunicaciones_informes'),
        array('entero', 'comunicaciones_herramientas'),
        array('entero', 'comunicaciones_tiene'),
        array('entero', 'comunicaciones_alta_todos'),
        array('entero', 'comunicaciones_mod_todos'),
        array('entero', 'archivos_ver'),
        array('entero', 'archivos_alta'),
        array('entero', 'archivos_baja'),
        array('entero', 'archivos_baja_todos')
    ));

        consulta_sql("UPDATE perfiles SET
            nombre='".$datos['nombre']."',
            configuraciones_empresa='".$datos['configuraciones_empresa']."',
            configuraciones_tablas='".$datos['configuraciones_tablas']."',
            configuraciones_cuentas='".$datos['configuraciones_cuentas']."',
            usuarios_mod='".$datos['usuarios_mod']."',
            usuarios_todos='".$datos['usuarios_todos']."',
            tareas_alta_todos='".$datos['tareas_alta_todos']."',
            tareas_ver_todos='".$datos['tareas_ver_todos']."',
            clientes_ver='".$datos['clientes_ver']."',
            clientes_alta='".$datos['clientes_alta']."',
            clientes_mod='".$datos['clientes_mod']."',
            clientes_mod_todos='".$datos['clientes_mod_todos']."',
            clientes_baja='".$datos['clientes_baja']."',
            clientes_informes='".$datos['clientes_informes']."',
            clientes_herramientas='".$datos['clientes_herramientas']."',
            productos_ver='".$datos['productos_ver']."',
            productos_alta='".$datos['productos_alta']."',
            productos_mod='".$datos['productos_mod']."',
            productos_costo='".$datos['productos_costo']."',
            productos_traslados='".$datos['productos_traslados']."',
            productos_baja='".$datos['productos_baja']."',
            productos_informes='".$datos['productos_informes']."',
            productos_herramientas='".$datos['productos_herramientas']."',
            servicios_tiene='".$datos['servicios_tiene']."',
            servicios_ver='".$datos['servicios_ver']."',
            servicios_alta='".$datos['servicios_alta']."',
            servicios_mod='".$datos['servicios_mod']."',
            servicios_baja='".$datos['servicios_baja']."',
            servicios_informes='".$datos['servicios_informes']."',
            servicios_herramientas='".$datos['servicios_herramientas']."',
            servicios_alta_todos='".$datos['servicios_alta_todos']."',
            servicios_mod_todos='".$datos['servicios_mod_todos']."',
            proveedores_ver='".$datos['proveedores_ver']."',
            proveedores_alta='".$datos['proveedores_alta']."',
            proveedores_mod='".$datos['proveedores_mod']."',
            proveedores_baja='".$datos['proveedores_baja']."',
            proveedores_informes='".$datos['proveedores_informes']."',
            proveedores_herramientas='".$datos['proveedores_herramientas']."',
            ventas_ver='".$datos['ventas_ver']."',
            ventas_alta='".$datos['ventas_alta']."',
            ventas_mod='".$datos['ventas_mod']."',
            ventas_baja='".$datos['ventas_baja']."',
            ventas_informes='".$datos['ventas_informes']."',
            ventas_herramientas='".$datos['ventas_herramientas']."',
            ventas_mod_todos='".$datos['ventas_mod_todos']."',
            ventas_nuevo='".$datos['ventas_nuevo']."',
            ventas_mod_precios='".$datos['ventas_mod_precios']."',
            ventas_mod_stock='".$datos['ventas_mod_stock']."',
            ventaspagos_ver='".$datos['ventaspagos_ver']."',
            ventaspagos_alta='".$datos['ventaspagos_alta']."',
            ventaspagos_mod='".$datos['ventaspagos_mod']."',
            ventaspagos_baja='".$datos['ventaspagos_baja']."',
            compras_ver='".$datos['compras_ver']."',
            compras_alta='".$datos['compras_alta']."',
            compras_mod='".$datos['compras_mod']."',
            compras_baja='".$datos['compras_baja']."',
            compras_informes='".$datos['compras_informes']."',
            compras_herramientas='".$datos['compras_herramientas']."',
            compraspagos_ver='".$datos['compraspagos_ver']."',
            compraspagos_alta='".$datos['compraspagos_alta']."',
            compraspagos_mod='".$datos['compraspagos_mod']."',
            compraspagos_baja='".$datos['compraspagos_baja']."',
            cajas_ver='".$datos['cajas_ver']."',
            cajas_informes='".$datos['cajas_informes']."',
            cajas_herramientas='".$datos['cajas_herramientas']."',
            conocimientos_ver='".$datos['conocimientos_ver']."',
            conocimientos_herramientas='".$datos['conocimientos_herramientas']."',
            bienes_ver='".$datos['bienes_ver']."',
            bienes_alta='".$datos['bienes_alta']."',
            bienes_mod='".$datos['bienes_mod']."',
            bienes_baja='".$datos['bienes_baja']."',
            bienes_informes='".$datos['bienes_informes']."',
            bienes_herramientas='".$datos['bienes_herramientas']."',
            comunicaciones_ver='".$datos['comunicaciones_ver']."',
            comunicaciones_alta='".$datos['comunicaciones_alta']."',
            comunicaciones_mod='".$datos['comunicaciones_mod']."',
            comunicaciones_baja='".$datos['comunicaciones_baja']."',
            comunicaciones_informes='".$datos['comunicaciones_informes']."',
            comunicaciones_herramientas='".$datos['comunicaciones_herramientas']."',
            comunicaciones_tiene='".$datos['comunicaciones_tiene']."',
            comunicaciones_alta_todos='".$datos['comunicaciones_alta_todos']."',
            comunicaciones_mod_todos='".$datos['comunicaciones_mod_todos']."',
            archivos_ver='".$datos['archivos_ver']."',
            archivos_alta='".$datos['archivos_alta']."',
            archivos_baja='".$datos['archivos_baja']."',
            archivos_baja_todos='".$datos['archivos_baja_todos']."'
            WHERE idperfil='".$id."' LIMIT 1");
        $resultado_sql = consulta_sql("SELECT idtipocaja FROM categorias_cajas WHERE compartida='0'");
        if (contar_sql($resultado_sql)) {
            $sql = 'INSERT INTO perfilesxcajas (idtipocaja, idperfil, abrir, ver, alta, mod_, baja, cerrar, mod_cerrada) VALUES ';
            while($tipocaja = array_sql($resultado_sql)) {
                $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_abrir'] = recibir_variable('idtipocaja_'.$tipocaja['idtipocaja'].'_abrir');
                $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_ver'] = recibir_variable('idtipocaja_'.$tipocaja['idtipocaja'].'_ver');
                $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_alta'] = recibir_variable('idtipocaja_'.$tipocaja['idtipocaja'].'_alta');
                $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_mod_'] = recibir_variable('idtipocaja_'.$tipocaja['idtipocaja'].'_mod_');
                $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_baja'] = recibir_variable('idtipocaja_'.$tipocaja['idtipocaja'].'_baja');
                $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_cerrar'] = recibir_variable('idtipocaja_'.$tipocaja['idtipocaja'].'_cerrar');
                $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_mod_cerrada'] = recibir_variable('idtipocaja_'.$tipocaja['idtipocaja'].'_mod_cerrada');

                $sql.= "('".$tipocaja['idtipocaja']."', '".$id."', '".$datos['idtipocaja_'.$tipocaja['idtipocaja'].'_abrir']."', '".$datos['idtipocaja_'.$tipocaja['idtipocaja'].'_ver']."', '".$datos['idtipocaja_'.$tipocaja['idtipocaja'].'_alta']."', '".$datos['idtipocaja_'.$tipocaja['idtipocaja'].'_mod_']."', '".$datos['idtipocaja_'.$tipocaja['idtipocaja'].'_baja']."', '".$datos['idtipocaja_'.$tipocaja['idtipocaja'].'_cerrar']."', '".$datos['idtipocaja_'.$tipocaja['idtipocaja'].'_mod_cerrada']."'),";
            }
            $sql[mb_strlen($sql)-1] = ' ';
            $sql.= "ON DUPLICATE KEY UPDATE abrir=VALUES(abrir), ver=VALUES(ver), alta=VALUES(alta), mod_=VALUES(mod_), baja=VALUES(baja), cerrar=VALUES(cerrar), mod_cerrada=VALUES(mod_cerrada)";
            consulta_sql($sql);
        }

        // Me fijo si hay usuarios afectados
        $usuarios_en_perfil_sql = consulta_sql(
            "SELECT idusuario
            FROM usuarios
            WHERE idperfil = '$id'");
        if (contar_sql($usuarios_en_perfil_sql)) {
        // Si los hay
            $patear = array();
            while ($usuario = array_sql($usuarios_en_perfil_sql)) {
                // Le agrego un mensaje, no me gusta el sql dentro de un while, pero no pueden ser muchos
                guardar_sql('mensajes', array(
                    'idusuario' => $usuario['idusuario'],
                    'tipo' => 'Confirmacion',
                    'texto' => $i18n[501].$_SESSION['usuario_nombre']
                ));
                $patear[] = $usuario['idusuario'];
            }
            // Lo pateo del sistema
            consulta_sql("UPDATE controles SET ultimosid = '' WHERE idusuario IN (".implode(', ', $patear).")");
        }
        ir_atras();
        break;

    case $i18n_funciones[23]: //Cancelar
        ir_atras();
        break;

    default:
        $datos = array_sql(consulta_sql("SELECT * FROM perfiles WHERE idperfil = $id LIMIT 1"));
        break;
}

mensajes_efimeros();

// Llevo la cuenta de la cantidad de módulos que muestro y fuerzo el salto porque sino hay algunos que son muy cortos y se apilan.
$saltar = 0;

ventana_inicio($i18n[76].$datos['nombre']);
{
    // Datos básicos
    contenido_inicio($i18n[89]);
    {
        entrada('texto', 'nombre', $i18n[84], $datos['nombre'], '50', '60');
    }
    contenido_fin();

    //Permisos
    contenido_inicio($i18n[100]);
    {
        enlaces('', array(array('tipo' => 'javascript', 'valor' => $i18n[157], 'opciones' => 'id="seleccionar_todos"'), array('tipo' => 'javascript', 'valor' => $i18n[158], 'opciones' => 'id="des-seleccionar_todos"')));
        salto_linea();
        bajo_linea();
        $ancho_marcas = '25';

        //Clientes
        $nombre_modulo = 'clientes';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110]),
                array('nombre' => $nombre_modulo.'_mod', 'valor' => $datos[$nombre_modulo.'_mod'], 'titulo' => $i18n[111]),
                array('nombre' => $nombre_modulo.'_mod_todos', 'valor' => $datos[$nombre_modulo.'_mod_todos'], 'titulo' => $i18n[114], 'ayuda_puntual' => $i18n[688]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112]),
                array('nombre' => $nombre_modulo.'_informes', 'valor' => $datos[$nombre_modulo.'_informes'], 'titulo' => $i18n[90], 'ayuda_puntual' => $i18n[320]),
                array('nombre' => $nombre_modulo.'_herramientas', 'valor' => $datos[$nombre_modulo.'_herramientas'], 'titulo' => $i18n[91], 'ayuda_puntual' => $i18n[335])
            ));
            $saltar++;
        }

        //Ventas
        $nombre_modulo = 'ventas';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110], 'ayuda_puntual' => $i18n[233]),
                array('nombre' => $nombre_modulo.'_mod', 'valor' => $datos[$nombre_modulo.'_mod'], 'titulo' => $i18n[111], 'ayuda_puntual' => $i18n[234]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112]),
                array('nombre' => $nombre_modulo.'_nuevo', 'valor' => $datos[$nombre_modulo.'_nuevo'], 'titulo' => $i18n[115], 'ayuda_puntual' => $i18n[305]),
                array('nombre' => $nombre_modulo.'_mod_precios', 'valor' => $datos[$nombre_modulo.'_mod_precios'], 'titulo' => $i18n[116], 'ayuda_puntual' => $i18n[306]),
                array('nombre' => $nombre_modulo.'_mod_stock', 'valor' => $datos[$nombre_modulo.'_mod_stock'], 'titulo' => $i18n[550], 'ayuda_puntual' => $i18n[551]),
                array('nombre' => $nombre_modulo.'_mod_todos', 'valor' => $datos[$nombre_modulo.'_mod_todos'], 'titulo' => $i18n[114], 'ayuda_puntual' => $i18n[301]),
                array('nombre' => $nombre_modulo.'_informes', 'valor' => $datos[$nombre_modulo.'_informes'], 'titulo' => $i18n[90], 'ayuda_puntual' => $i18n[320]),
                array('nombre' => $nombre_modulo.'_herramientas', 'valor' => $datos[$nombre_modulo.'_herramientas'], 'titulo' => $i18n[91], 'ayuda_puntual' => $i18n[335])
            ));
            $saltar++;

            //Recibos de pagos
            $nombre_modulo = 'ventaspagos';
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110]),
                array('nombre' => $nombre_modulo.'_mod', 'valor' => $datos[$nombre_modulo.'_mod'], 'titulo' => $i18n[111]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112])
            ));
            $saltar++;
        }

        //Servicios
        $nombre_modulo = 'servicios';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110]),
                array('nombre' => $nombre_modulo.'_mod', 'valor' => $datos[$nombre_modulo.'_mod'], 'titulo' => $i18n[111]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112]),
                array('nombre' => $nombre_modulo.'_tiene', 'valor' => $datos[$nombre_modulo.'_tiene'], 'titulo' => $i18n[113], 'ayuda_puntual' => $i18n[302]),
                array('nombre' => $nombre_modulo.'_alta_todos', 'valor' => $datos[$nombre_modulo.'_alta_todos'], 'titulo' => $i18n[108], 'ayuda_puntual' => $i18n[303]),
                array('nombre' => $nombre_modulo.'_mod_todos', 'valor' => $datos[$nombre_modulo.'_mod_todos'], 'titulo' => $i18n[114], 'ayuda_puntual' => $i18n[304]),
                array('nombre' => $nombre_modulo.'_informes', 'valor' => $datos[$nombre_modulo.'_informes'], 'titulo' => $i18n[90], 'ayuda_puntual' => $i18n[320]),
                array('nombre' => $nombre_modulo.'_herramientas', 'valor' => $datos[$nombre_modulo.'_herramientas'], 'titulo' => $i18n[91], 'ayuda_puntual' => $i18n[335])
            ));
            $saltar++;
        }

        if ($saltar >= 4) {
            salto_linea();
            bajo_linea();
            $saltar = 0;
        }

        //Proveedores
        $nombre_modulo = 'proveedores';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110]),
                array('nombre' => $nombre_modulo.'_mod', 'valor' => $datos[$nombre_modulo.'_mod'], 'titulo' => $i18n[111]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112]),
                array('nombre' => $nombre_modulo.'_informes', 'valor' => $datos[$nombre_modulo.'_informes'], 'titulo' => $i18n[90], 'ayuda_puntual' => $i18n[320]),
                array('nombre' => $nombre_modulo.'_herramientas', 'valor' => $datos[$nombre_modulo.'_herramientas'], 'titulo' => $i18n[91], 'ayuda_puntual' => $i18n[335])
            ));
            $saltar++;
        }

        if ($saltar >= 4) {
            salto_linea();
            bajo_linea();
            $saltar = 0;
        }

        //Compras
        $nombre_modulo = 'compras';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110]),
                array('nombre' => $nombre_modulo.'_mod', 'valor' => $datos[$nombre_modulo.'_mod'], 'titulo' => $i18n[111]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112]),
                array('nombre' => $nombre_modulo.'_informes', 'valor' => $datos[$nombre_modulo.'_informes'], 'titulo' => $i18n[90], 'ayuda_puntual' => $i18n[320]),
                array('nombre' => $nombre_modulo.'_herramientas', 'valor' => $datos[$nombre_modulo.'_herramientas'], 'titulo' => $i18n[91], 'ayuda_puntual' => $i18n[335])
            ));
            $saltar++;

            //Órdenes de pagos
            $nombre_modulo = 'compraspagos';
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110]),
                array('nombre' => $nombre_modulo.'_mod', 'valor' => $datos[$nombre_modulo.'_mod'], 'titulo' => $i18n[111]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112])
            ));
            $saltar++;
        }

        if ($saltar >= 4) {
            salto_linea();
            bajo_linea();
            $saltar = 0;
        }

        //Productos
        $nombre_modulo = 'productos';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110]),
                array('nombre' => $nombre_modulo.'_mod', 'valor' => $datos[$nombre_modulo.'_mod'], 'titulo' => $i18n[111]),
                array('nombre' => $nombre_modulo.'_costo', 'valor' => $datos[$nombre_modulo.'_costo'], 'titulo' => $i18n[624], 'ayuda_puntual' => $i18n[625]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112]),
                array('nombre' => $nombre_modulo.'_informes', 'valor' => $datos[$nombre_modulo.'_informes'], 'titulo' => $i18n[90], 'ayuda_puntual' => $i18n[320]),
                array('nombre' => $nombre_modulo.'_herramientas', 'valor' => $datos[$nombre_modulo.'_herramientas'], 'titulo' => $i18n[91], 'ayuda_puntual' => $i18n[335]),
                array('nombre' => $nombre_modulo.'_traslados', 'valor' => $datos[$nombre_modulo.'_traslados'], 'titulo' => $i18n[626])
            ));
            $saltar++;
        }

        if ($saltar >= 4) {
            salto_linea();
            bajo_linea();
            $saltar = 0;
        }

        //Comunicaciones
        $nombre_modulo = 'comunicaciones';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110]),
                array('nombre' => $nombre_modulo.'_mod', 'valor' => $datos[$nombre_modulo.'_mod'], 'titulo' => $i18n[111]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112]),
                array('nombre' => $nombre_modulo.'_tiene', 'valor' => $datos[$nombre_modulo.'_tiene'], 'titulo' => $i18n[113], 'ayuda_puntual' => $i18n[307]),
                array('nombre' => $nombre_modulo.'_alta_todos', 'valor' => $datos[$nombre_modulo.'_alta_todos'], 'titulo' => $i18n[108], 'ayuda_puntual' => $i18n[308]),
                array('nombre' => $nombre_modulo.'_mod_todos', 'valor' => $datos[$nombre_modulo.'_mod_todos'], 'titulo' => $i18n[114], 'ayuda_puntual' => $i18n[309]),
                array('nombre' => $nombre_modulo.'_informes', 'valor' => $datos[$nombre_modulo.'_informes'], 'titulo' => $i18n[90], 'ayuda_puntual' => $i18n[320]),
                array('nombre' => $nombre_modulo.'_herramientas', 'valor' => $datos[$nombre_modulo.'_herramientas'], 'titulo' => $i18n[91], 'ayuda_puntual' => $i18n[335])
            ));
            $saltar++;
        }

        if ($saltar >= 4) {
            salto_linea();
            bajo_linea();
            $saltar = 0;
        }

        //Bienes
        $nombre_modulo = 'bienes';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110]),
                array('nombre' => $nombre_modulo.'_mod', 'valor' => $datos[$nombre_modulo.'_mod'], 'titulo' => $i18n[111]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112]),
                array('nombre' => $nombre_modulo.'_informes', 'valor' => $datos[$nombre_modulo.'_informes'], 'titulo' => $i18n[90], 'ayuda_puntual' => $i18n[320]),
                array('nombre' => $nombre_modulo.'_herramientas', 'valor' => $datos[$nombre_modulo.'_herramientas'], 'titulo' => $i18n[91], 'ayuda_puntual' => $i18n[335])
            ));
            $saltar++;
        }

        if ($saltar >= 4) {
            salto_linea();
            bajo_linea();
            $saltar = 0;
        }

        //Tareas
        $nombre_modulo = 'tareas';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver_todos', 'valor' => $datos[$nombre_modulo.'_ver_todos'], 'titulo' => $i18n[107], 'ayuda_puntual' => $i18n[317]),
                array('nombre' => $nombre_modulo.'_alta_todos', 'valor' => $datos[$nombre_modulo.'_alta_todos'], 'titulo' => $i18n[108], 'ayuda_puntual' => $i18n[318])
            ));
            $saltar++;
        }

        if ($saltar >= 4) {
            salto_linea();
            bajo_linea();
            $saltar = 0;
        }

        //Conocimientos
        $nombre_modulo = 'conocimientos';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109], 'ayuda_puntual' => $i18n[319]),
                array('nombre' => $nombre_modulo.'_herramientas', 'valor' => $datos[$nombre_modulo.'_herramientas'], 'titulo' => $i18n[91], 'ayuda_puntual' => $i18n[335])
            ));
            $saltar++;
        }

        if ($saltar >= 4) {
            salto_linea();
            bajo_linea();
            $saltar = 0;
        }

        //Archivos
        $nombre_modulo = 'archivos';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109]),
                array('nombre' => $nombre_modulo.'_alta', 'valor' => $datos[$nombre_modulo.'_alta'], 'titulo' => $i18n[110]),
                array('nombre' => $nombre_modulo.'_baja', 'valor' => $datos[$nombre_modulo.'_baja'], 'titulo' => $i18n[112]),
                array('nombre' => $nombre_modulo.'_baja_todos', 'valor' => $datos[$nombre_modulo.'_baja_todos'], 'titulo' => $i18n[118], 'ayuda_puntual' => $i18n[310])
            ));
        $saltar++;
        }

        if ($saltar >= 4) {
            salto_linea();
            bajo_linea();
            $saltar = 0;
        }

        //Configuraciones
        $nombre_modulo = 'configuraciones';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$i18n_funciones[$nombre_modulo], $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_empresa', 'valor' => $datos[$nombre_modulo.'_empresa'], 'titulo' => $i18n[102], 'ayuda_puntual' => $i18n[311]),
                array('nombre' => $nombre_modulo.'_tablas', 'valor' => $datos[$nombre_modulo.'_tablas'], 'titulo' => $i18n[103], 'ayuda_puntual' => $i18n[312]),
                array('nombre' => $nombre_modulo.'_cuentas', 'valor' => $datos[$nombre_modulo.'_cuentas'], 'titulo' => $i18n[104], 'ayuda_puntual' => $i18n[313]),
                array('nombre' => 'usuarios_mod', 'valor' => $datos['usuarios_mod'], 'titulo' => $i18n[105], 'ayuda_puntual' => $i18n[314]),
                array('nombre' => 'usuarios_todos', 'valor' => $datos['usuarios_todos'], 'titulo' => $i18n[106], 'ayuda_puntual' => $i18n[315])
            ));
        }

        salto_linea();
        bajo_linea();

        //Cajas
        $nombre_modulo = 'cajas';
        if ($_SESSION['modulo_'.$nombre_modulo]) {
            marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.ucfirst($nombre_modulo), $ancho_marcas, array(
                array('nombre' => $nombre_modulo.'_ver', 'valor' => $datos[$nombre_modulo.'_ver'], 'titulo' => $i18n[109], 'ayuda_puntual' => $i18n[316]),
                array('nombre' => $nombre_modulo.'_informes', 'valor' => $datos[$nombre_modulo.'_informes'], 'titulo' => $i18n[90], 'ayuda_puntual' => $i18n[320]),
                array('nombre' => $nombre_modulo.'_herramientas', 'valor' => $datos[$nombre_modulo.'_herramientas'], 'titulo' => $i18n[91], 'ayuda_puntual' => $i18n[335])
            ));

            bloque_inicio('cajas');
            {
                $i = 1;
                $resultado_sql = consulta_sql("SELECT idtipocaja, nombre, compartida FROM categorias_cajas ORDER BY compartida, nombre");
                while($tipocaja = array_sql($resultado_sql)) {
                    $resultado_sql2 = consulta_sql("SELECT abrir, ver, alta, mod_, baja, cerrar, mod_cerrada FROM perfilesxcajas WHERE idtipocaja='".$tipocaja['idtipocaja']."' AND idperfil='".$id."' LIMIT 1");
                    if (contar_sql($resultado_sql2)) {
                        $temp_array = array_sql($resultado_sql2);
                        foreach ($temp_array as $key => $value) {
                            $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_'.$key] = $value;
                        }
                    }
                    if (!$tipocaja['compartida']) {
                        marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$tipocaja['nombre'], $ancho_marcas, array(
                            array('nombre' => 'idtipocaja_'.$tipocaja['idtipocaja'].'_ver', 'valor' => $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_ver'], 'titulo' => $i18n[176], 'ayuda_puntual' => $i18n[321]),
                            array('nombre' => 'idtipocaja_'.$tipocaja['idtipocaja'].'_abrir', 'valor' => $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_abrir'], 'titulo' => $i18n[175], 'ayuda_puntual' => $i18n[322]),
                            array('nombre' => 'idtipocaja_'.$tipocaja['idtipocaja'].'_cerrar', 'valor' => $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_cerrar'], 'titulo' => $i18n[177], 'ayuda_puntual' => $i18n[323]),
                            array('nombre' => 'idtipocaja_'.$tipocaja['idtipocaja'].'_alta', 'valor' => $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_alta'], 'titulo' => $i18n[171], 'ayuda_puntual' => $i18n[324]),
                            array('nombre' => 'idtipocaja_'.$tipocaja['idtipocaja'].'_mod_', 'valor' => $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_mod_'], 'titulo' => $i18n[172], 'ayuda_puntual' => $i18n[325]),
                            array('nombre' => 'idtipocaja_'.$tipocaja['idtipocaja'].'_baja', 'valor' => $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_baja'], 'titulo' => $i18n[173], 'ayuda_puntual' => $i18n[326]),
                            array('nombre' => 'idtipocaja_'.$tipocaja['idtipocaja'].'_mod_cerrada', 'valor' => $datos['idtipocaja_'.$tipocaja['idtipocaja'].'_mod_cerrada'], 'titulo' => $i18n[174], 'ayuda_puntual' => $i18n[327])
                        ));
                    } else {
                        marcas('&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$nombre_modulo.'.png" class="ventana_iconito"/> '.$tipocaja['nombre'], $ancho_marcas, array(
                            array('nombre' => 'idtipocaja_'.$tipocaja['idtipocaja'].'_compartida', 'titulo' => $i18n[168], 'valor' => 1, 'ayuda_puntual' => $i18n[328], 'opciones' => 'disabled="disabled"'),
                        ));
                    }
                    $i++;
                    if ($i > 3) {
                        salto_linea();
                        bajo_linea();
                        $i = 0;
                    }
                }
            }
            bloque_fin();
        }
    }
    contenido_fin();


    botones(array(
        array('valor' => $i18n_funciones[22]),
        array('valor' => $i18n_funciones[23]),
    ));
}
ventana_fin();

// Busco si hay usuarios que tienen este perfil para mostrar el confirmar en JS
$usuarios_en_perfil = campo_sql(consulta_sql(
    "SELECT GROUP_CONCAT(DISTINCT usuarios.nombre ORDER BY usuarios.nombre SEPARATOR ', ') AS usuarios
        FROM usuarios
        WHERE idperfil = '$id'"));

?>
<script type="text/javascript" charset="utf-8">
function validacion_configuraciones_perfiles_mod(boton)
{
    if (boton == '<?php echo $i18n_funciones[22]; ?>' && $("#marco_configuraciones_perfiles_mod input[name='nombre']").val() == '') {
<?php script_validacion_flotante('alerta', $i18n[119], "input[name='nombre']"); ?>

        document.body.scrollTop = document.documentElement.scrollTop = 0;
        return false;

    } else if (boton == '<?php echo $i18n_funciones[22]; ?>' && '<?php echo $usuarios_en_perfil; ?>') {
        if (confirma('<?php echo $i18n[487].$usuarios_en_perfil; ?>')) {
            $("input[name='productos_costo']").prop('disabled', false);
            $("input[name='productos_traslados']").prop('disabled', false);
            return true;
        }
        return false;

    } else {
        return true;
    }
};
</script>
