<?php
// Genero archivo temporal para bloquear otra importación
touch(PATH_ARCHIVOS.$_SESSION['empresa_idempresa'].'.importando');

// Aviso al usuario que inició el proceso
mensajes_alta($i18n_funciones[259], 'Confirmacion');

// Recibo datos desde post
$recibir = array();
$random = recibir_variable('texto', 'random', false);
$campo_clave = recibir_variable('texto', 'campo_clave', false);
$array_precios_stock = ['stockactual', 'Stock actual', 'stockminimo', 'Stock mínimo', 'stockideal', 'Stock ideal', 'precio', 'Precio', 'preciofinal', 'Precio final', 'utilidad', 'Utilidad'];
$array_lineas_actualizadas = array();

foreach ($opciones as $key => $value) {
    $recibir[] = $value['nombre'];
}
for ($i = 0; $i < $columnas_fuente_total; $i++) {
    $recibir[] = $prefijo_select.$i;
}
$datos = recibir_matriz($recibir);

//Recibo idlista y iddeposito
if ($modulo == 'productos'){
    $idlista = recibir_variable('entero', 'idlista', false);
    $iddeposito = recibir_variable('entero', 'iddeposito', false);
}

//datosextras
$extras = array();
$listasxextras = array();

$resultado_sql = consulta_sql("SELECT * FROM extrasxmodulos WHERE modulo = '$modulo'");
while ($temp_array = array_sql($resultado_sql)) {
    $extras[] = $temp_array;
}

if ($extras) {
    $resultado_sql = consulta_sql("SELECT listasxextras.*
        FROM listasxextras
        LEFT JOIN extrasxmodulos ON listasxextras.idextraxmodulo = extrasxmodulos.idextraxmodulo
        WHERE extrasxmodulos.modulo =  '$modulo'
    ");
    while($temp_array = array_sql($resultado_sql)) {
        $listasxextras[] = $temp_array;
    }
}
// fin datos extras con listas

//genera el sql de la tabla principal
$csv_procesado = fopen(herramientas_nombre_archivo($modulo.'_procesado', $random), 'r');
$sql_tabla = fopen(herramientas_nombre_archivo($modulo.'_sql', $random,'.sql'), 'w');

$sql_tabla_datos_extras = fopen(herramientas_nombre_archivo($modulo.'_datos_extras_sql', $random,'.sql'), 'w');

$nombres_columnas = fgetcsv($csv_procesado, 0, SEPARADOR_CSV);
$total_registros = 0;
$tmp_array_idproductos = [];
$tmp_array_codigo_productos = [];
$total_registros_importados = 0;
$total_registros_actualizados = 0;
$total_registros_descartados = 0;
$array_valores_categoria_relacionada = array();
$pattern_where = '/WHERE '.$campo_clave.' = (.*?) /';
$pattern_set = '/SET '.$campo_clave.' = (.*?),/';

$clave_primaria = true;
if ($campo_clave != $idmodulo)
    $clave_primaria = false;

while ($linea = fgetcsv($csv_procesado, 0, SEPARADOR_CSV)) {
    $sql = "";
    $where = "";
    $primer_campo = true;
    $campos_sin_pk ="";
    //Armo siempre en insert con la salvedad de que si es pk duplicada hace el update de los demas campos que no son pk
    $editable = true;
    $valor_campo_clave = "";

    for ($i = 0; $i < count($linea); ++$i) {
        if ($sql == "") {
            $sql = "INSERT IGNORE INTO ".$modulo." SET ";
        }
        if ($campo == 'moneda_costo') {
            $sql .= "idmoneda = '".escape_sql($linea[$i])."', ";
        }
        if ($campo_clave != $nombres_columnas[$i]) {
            if (array_search($nombres_columnas[$i], array_column($extras, 'nombre')) === false) {
                if (array_search($nombres_columnas[$i], $array_precios_stock) === false) {
                    if ($campos_sin_pk != "")
                        $campos_sin_pk .= ', ';
                    $campos_sin_pk .= $nombres_columnas[$i]." = '".escape_sql($linea[$i])."'";
                }

            }
        } else {
            $valor_campo_clave = escape_sql(ltrim($linea[$i], "'"));
        }
        if ($columnas[$nombres_columnas[$i]]["no_editables"] && in_array($linea[$i], explode(",", $columnas[$nombres_columnas[$i]]["no_editables"]))) {
            $editable = false;
        }
        //Agrego a array valores de categoría relacionada
        if ($procesos[$nombres_columnas[$i]]['tipo'] == 'categoria_relacionada'){
            if(!buscar_array_multiple($linea[$i], $array_valores_categoria_relacionada[$procesos[$nombres_columnas[$i]]['nombre_amigable']])) {
                $array_valores_categoria_relacionada[$procesos[$nombres_columnas[$i]]['nombre_amigable']][] = $linea[$i];
            }
        }
        //Busco datos extra y los quito del sql
        if (array_search($nombres_columnas[$i], array_column($extras, 'nombre')) === false) {
            if (array_search($nombres_columnas[$i], $array_precios_stock) === false){
                if ($sql != "INSERT IGNORE INTO ".$modulo." SET ")
                    $sql .=', ';
                    if ($campo_clave == $nombres_columnas[$i] && $campo_clave == 'codigo') {
                        $sql .= $nombres_columnas[$i]." = '".$valor_campo_clave."'";
                    } else {
                        $sql .= $nombres_columnas[$i]." = '".escape_sql($linea[$i])."'";
                    }
            }
        } else {
            //Insert y Update de datos extras
            foreach ($extras as $key => $extra) {
                if ($nombres_columnas[$i] == $extra['nombre'] && $linea[$i] != ' ' && $linea[$i] != '') {
                    if ($extra['tipo'] == 'lista'){
                        //Busco existencia de dato en la lista, al final no va por ahora, para agregar
                        foreach ($listasxextras as $key => $listaxextra) {
                            if ($linea[$i] == $listaxextra['nombre']){
                                $sql_datos_extras = "INSERT INTO datosxextras (idextraxmodulo, idrelacion, idlistaxextra, texto) VALUES('".$extra['idextraxmodulo']."', '".$valor_campo_clave."', '".$listaxextra['idlistaxextra']."', '') ON DUPLICATE KEY UPDATE idlistaxextra = '".$listaxextra['idlistaxextra']."'";
                                fwrite($sql_tabla_datos_extras, $sql_datos_extras.PHP_EOL);
                            }
                        }
                    } elseif ($extra['tipo'] == 'texto') {
                        $sql_datos_extras = "INSERT INTO datosxextras (idextraxmodulo, idrelacion, idlistaxextra, texto) VALUES('".$extra['idextraxmodulo']."', '".$valor_campo_clave."', '', '".escape_sql($linea[$i])."') ON DUPLICATE KEY UPDATE texto = '".escape_sql($linea[$i])."'";
                        fwrite($sql_tabla_datos_extras, $sql_datos_extras.PHP_EOL);
                    }
                }
            }
        }
    }

    /*TODO si mysql inventa un insert if not exists de un campo no clave y que no sea uni cambiar eso por favor.*/
    if ($clave_primaria) {
        if ($campos_sin_pk) {
            $sql .= " ON DUPLICATE KEY UPDATE ".$campos_sin_pk;
        } else {
            $temp_sql = explode('INSERT IGNORE', $sql);
            $sql = 'INSERT IGNORE'.$temp_sql[1];
        }
    } else if ($campos_sin_pk) { // No se en que caso entra en este if con $campos_sin_pk vacío
        if ($modulo != 'productos' || strpos('idproducto', $campos_sin_pk) === false) {
            if (contar_sql(array_sql(consulta_sql("SELECT * FROM ".$modulo." WHERE TRIM(".$campo_clave.") = TRIM('".$valor_campo_clave."')")))) {
                $sql = "UPDATE ".$modulo." SET ".$campos_sin_pk." WHERE TRIM(".$campo_clave.") = TRIM('".$valor_campo_clave."')";
            } else {
                $sql .= " ON DUPLICATE KEY UPDATE ".$campos_sin_pk;
            }
        } else {
            mensajes_alta($i18n_funciones[257]);
        }
    }

    if ($editable && $sql) {
        fwrite($sql_tabla, $sql.$where.PHP_EOL);
        ++$total_registros;
    }
}

fclose($sql_tabla);
fclose($csv_procesado);
fclose($sql_tabla_datos_extras);

$sql = "";
$idx = "";
$sql_precios_stock = "";
$tabla_actual = "";
$array_categorias = array();
//Genera el sql de las tablas relacionadas
$array_total_registros_tablas_relacionadas = array();
foreach ($procesos as $campo => $parametros) {
    if ($parametros["tipo"] == 'categoria_relacionada' || $parametros["tipo"] = 'tabla_relacionada'){
        $array_total_registros_tablas_relacionadas[$parametros["nombre_amigable"]]["total"] = 0;
        $array_total_registros_tablas_relacionadas[$parametros["nombre_amigable"]]["total_importados"] = 0;
        if (file_exists(herramientas_nombre_archivo($campo.'_tabla_procesada', $random))) {
            $csv_tabla = fopen(herramientas_nombre_archivo($campo.'_tabla_procesada', $random), 'r');
            $sql_tabla = fopen(herramientas_nombre_archivo($parametros["tabla"].'_sql', $random,'.sql'), 'w');
            $nombres_columnas = fgetcsv($csv_tabla, 0, SEPARADOR_CSV);
            while ($linea = fgetcsv($csv_tabla, 0, SEPARADOR_CSV)) {
                //quito la suma total, ya que sino, suma todos los datos de las columnas
                if ($parametros["tabla"] != $tabla_actual)
                    $sql_precios_stock = "";
                $sql = "";
                $where = "";
                $primer_campo = true;
                for ($i = 0; $i < count($linea); ++$i) {
                    if ($sql == ""){
                        if (array_search($campo, $array_precios_stock) === false){
                            //validación para sumar
                            if (buscar_array_multiple($linea[$i], $array_valores_categoria_relacionada[$parametros["nombre_amigable"]])){
                                if (!buscar_array_multiple($linea[$i], $array_categorias[$parametros["tabla"]])){
                                    $array_categorias[$parametros["tabla"]] = $linea[$i];
                                    $array_total_registros_tablas_relacionadas[$parametros["nombre_amigable"]]["total"]++;
                                }
                            }

                            $sql = "INSERT INTO ".$parametros["tabla"]." (".$campo.", nombre) SELECT * FROM ( SELECT ";
                            $pk_valor = $linea[$i];
                        } else {
                            if ($i == 0){ //comparo con el primero, sino me lo duplica
                                if ($parametros["tabla"] == 'precios'){
                                    $idx = 'idlista';
                                    $tabla_actual = 'precios';
                                    $idtabla = $idlista;
                                } elseif ($parametros["tabla"] == 'stock') {
                                    $idx = 'iddeposito';
                                    $tabla_actual = 'stock';
                                    $idtabla = $iddeposito;
                                }
                                if ($linea[$i] && $campo_clave == 'idproducto'){ // filtro por si viene !idproducto o similar
                                    $sql_precios_stock .= "INSERT INTO ".$parametros["tabla"]." SET ".$campo_clave." = ".$linea[$i].", ".$idx." = ".$idtabla.", ";
                                    $sql_precios_stock .= $campo." = '".floatval(str_replace(',', '.', $linea[$i+1]))."'";
                                    $sql_precios_stock .= " ON DUPLICATE KEY UPDATE ".$campo." = '".floatval(str_replace(',', '.', $linea[$i+1]))."'";
                                    $sql_precios_stock .= "\n";
                                } else {
                                    if ($campo_clave == 'codigo' || $campo_clave == 'codigoproveedor') {
                                        $valor_campo_clave = escape_sql(ltrim($linea[$i], "'"));
                                        $sql_precios_stock .= "INSERT IGNORE INTO ".$parametros["tabla"]." SET idproducto = (SELECT idproducto FROM productos WHERE ".$campo_clave." = '".$valor_campo_clave."'), ".$idx." = ".$idtabla." ";
                                        $sql_precios_stock .= "\n";
                                        $sql_precios_stock .= "UPDATE ".$parametros["tabla"]." LEFT JOIN productos ON ".$parametros["tabla"].".idproducto = productos.idproducto SET ";
                                        $sql_precios_stock .= $campo." = '".floatval(str_replace(',', '.', $linea[$i+1]))."' ";
                                        $sql_precios_stock .= "WHERE ".$campo_clave." = '".$valor_campo_clave."' AND ".$idx." = ".$idtabla."";
                                        $sql_precios_stock .= "\n";
                                    } else {
                                        //$sql .= " ON DUPLICATE KEY UPDATE ".$campos_sin_pk;
                                    }

                                }
                            }
                        }
                    } else {
                        if (array_search($campo, $array_precios_stock) === false)
                            $sql .=',';
                    }
                    if (array_search($campo, $array_precios_stock) === false)
                        $sql.="'".escape_sql($linea[$i])."'";
                }
                if (array_search($campo, $array_precios_stock) === false)
                    $sql .= ") AS tmp WHERE NOT EXISTS (SELECT nombre FROM ".$parametros["tabla"]." WHERE ".$campo." = '".$pk_valor."' )";

                if (array_search($campo, $array_precios_stock) === false)
                    fwrite($sql_tabla, $sql.$where.PHP_EOL);
            }
            //Otro fwrite para stock y precios, con el choclo
            if (array_search($campo, $array_precios_stock) !== false){
                fwrite($sql_tabla, $sql_precios_stock);
            }
            fclose($sql_tabla);
            fclose($csv_tabla);
        }
    }
}

//Ejecuto el sql principal
$array_idproductos = array(); //Verificador para logs
$sql_tabla = fopen(herramientas_nombre_archivo($modulo.'_sql', $random,'.sql'), 'r');
$j = 0;
while ($linea = fgets($sql_tabla)) {
    $j++;
    try {
        if (consulta_sql($linea)) {
            //From Mysql manual: "With ON DUPLICATE KEY UPDATE, the affected-rows value per row is 1 if the row is inserted as a new row and 2 if an existing row is updated."
            if (afectado_sql()) {
                $match = matchear_importacion($pattern_set, $linea);
                if ($match && array_search($match, $tmp_array_idproductos) === false) {
                    if (afectado_sql() === 2) { //UPDATE
                        $total_registros_actualizados++;
                    }
                    if ($campo_clave === 'codigo') {
                        $tmp_array_codigo_productos[] = $match;
                    }
                    $tmp_array_idproductos[] = $match;
                }

                if (afectado_sql() === 1) { //INSERT
                    $total_registros_importados++;
                    if ($modulo == 'productos') {
                        $idproducto = id_sql();
                        if ($idproducto) {
                            $array_idproductos[] = $idproducto;
                        }
                    }
                }
            }
        } else {
            $total_registros_descartados++;
        }
    } catch (Exception $e) {
        mensajes_alta('El importador falló con el producto línea: '.$j, 'Notificacion', true);
        mostrar_error($i18n[41] . '<br />El importador falló con el producto: <br />' .$linea. $e->getMessage(), true);
    }
}
fclose($sql_tabla);

//ejecuto el sql de las tablas relacionadas
foreach ($procesos as $campo => $parametros) {
    if ($parametros["tipo"] == 'categoria_relacionada' || $parametros["tipo"] == 'sucursales') {
        $array_total_registros_tablas_relacionadas[$parametros["nombre_amigable"]]["total_importados"] = 0;
        if (file_exists(herramientas_nombre_archivo($parametros["tabla"].'_sql', $random,'.sql'))) {
            $sql_tabla = fopen(herramientas_nombre_archivo($parametros["tabla"].'_sql', $random,'.sql'), 'r');
            $j = 0;
            while ($linea = fgets($sql_tabla)) {
                $j++;
                try {
                    consulta_sql($linea);
                } catch (Exception $e) {
                    mensajes_alta('El importador falló con el producto línea: '.$j, 'Notificacion', true);
                    mostrar_error($i18n[41] . '<br />El importador falló en tablas relacionadas con el producto: <br />' .$linea. $e->getMessage(), true);
                }

                if (afectado_sql()) {
                    if (array_search($parametros["nombre_amigable"], $array_precios_stock) !== false) {
                        $temp_linea = explode(' ', $linea);
                        $idproducto = str_replace(',', '', $temp_linea[6]); // INSERT INTO precios SET idproducto = 30(6)
                        if (!buscar_array_multiple($idproducto, $array_idproductos) && is_numeric($tmp_idproducto)) {
                            $array_idproductos[] = $idproducto;
                        }

                        $match = matchear_importacion($pattern_where, $linea);
                        if (!$match) {
                            $match = matchear_importacion($pattern_set, $linea);
                        }
                        if ($match && array_search($match, $tmp_array_idproductos) === false) {
                            if ($campo_clave === 'codigo') {
                                $tmp_array_codigo_productos[] = $match;
                            }
                            $tmp_array_idproductos[] = $match;
                            $total_registros_actualizados++; //Segunda suma, tablas relacionadas
                        } else {
                            continue;
                        }
                        $array_total_registros_tablas_relacionadas[$parametros["nombre_amigable"]]["total_importados"]++;
                    }
                }
            }
            fclose($sql_tabla);
        }
    }
}

if ($extras) {
    if (file_exists(herramientas_nombre_archivo($parametros["tabla"].$modulo.'_datos_extras_sql', $random,'.sql'))){
        $sql_tabla = fopen(herramientas_nombre_archivo($parametros["tabla"].$modulo.'_datos_extras_sql', $random,'.sql'), 'r');
        $total_datos_extras = 0;
        $array_total_registros_tablas_relacionadas["Datos Extras"]["total_importados"] = 0;
        $j = 0;
        while ($linea = fgets($sql_tabla)) {
            $array_total_registros_tablas_relacionadas["Datos Extras"]["total_importados"]++;
            $j++;
            try {
                consulta_sql($linea);
            } catch (Exception $e) {
                mensajes_alta('El importador falló en datos extra con el producto línea: '.$j, 'Notificacion', true);
                mostrar_error($i18n[41] . '<br />El importador falló en datos extra con el producto: <br />' .$linea. $e->getMessage(), true);
            }
            if (afectado_sql()){
                $total_datos_extras++;
            }
        }
        fclose($sql_tabla);
    }
}

// Si es productos agrego registro al log y actualizo fullsearch
if ($modulo == 'productos') {
    if (!empty($tmp_array_codigo_productos)) {
        $chunks = array_chunk($tmp_array_codigo_productos, 100);

        foreach ($chunks as $chunk) {
            $codigos = "'" . implode("','", $chunk) . "'";
            $idproductos_by_codigo = campo_sql(consulta_sql("SELECT GROUP_CONCAT(idproducto) FROM productos WHERE TRIM(codigo) IN ($codigos)"), 0);

            if ($idproductos_by_codigo) {
                $array_idproductos_by_codigo = explode(',', $idproductos_by_codigo);
                foreach ($array_idproductos_by_codigo as $tmp_idproducto) {
                    if (!in_array($tmp_idproducto, $array_idproductos)) {
                        $array_idproductos[] = $tmp_idproducto;
                    }
                }
            }
        }
    }

    if (!empty($array_idproductos)) {
        log_productos_bulk(array_filter($array_idproductos), 'Modificación con importador/actualizador');
        fullsearch_full_update($modulo);
    }

} else {
    // Re-armo la tabla completa del fullsearch si no es productos
    fullsearch_full_update($modulo);
}


//limpio los archivos generados
$archivos_generados  = scandir(PATH_ARCHIVOS);
$patron = $_SESSION['empresa_idempresa'].'_'
        .$random.'_';
foreach ($archivos_generados as $archivo_generado) {
    if (substr($archivo_generado,0,strlen($patron)) == $patron){
        unlink(PATH_ARCHIVOS.$archivo_generado);
    }
}

// Aviso al usuario que se terminó la importación
mensajes_alta($i18n_funciones[255], 'Confirmacion');

$mensaje = $i18n_funciones[256].'<br><br>'
    . $i18n_funciones[146].'<br>'
    . $i18n_funciones[147].$nombre_amigable.': '.$total_registros.'<br>'
    . $i18n_funciones[148].$nombre_amigable.': '.$total_registros_importados.'<br>'
    . $i18n_funciones[149].$nombre_amigable.': '.$total_registros_actualizados.'<br>'
    . $i18n_funciones[264].$nombre_amigable.': '.$total_registros_descartados.'<br>';
foreach ($array_total_registros_tablas_relacionadas as $key => $value) {
    if ($key != 'Datos Extras'){
        if ($key && array_search($key, $array_precios_stock) === false){
            $mensaje.= $i18n_funciones[147].$key.': '.$value["total"].'<br>';
            $mensaje.= $i18n_funciones[148].$key.': '.$value["total_importados"].'<br>';
            $mensaje.= $i18n_funciones[150].$key.': '.($value["total"] - $value["total_importados"]).'<br>';
        }
    } else {
        $mensaje.= $i18n_funciones[147].$key.': '.$array_total_registros_tablas_relacionadas["Datos Extras"]["total_importados"].'<br>';
        $mensaje.= $i18n_funciones[148].$key.': '.$total_datos_extras.'<br>';
        $mensaje.= $i18n_funciones[150].$key.': '.($array_total_registros_tablas_relacionadas["Datos Extras"]["total_importados"] -  $total_datos_extras).'<br>';
    }
}

email_queue(array('<EMAIL>' => 'SaaS Argentina'), $_SESSION['usuario_mail'], $i18n_funciones[260], $mensaje);

// Libero archivo temporal para bloquear otra importación
unlink(PATH_ARCHIVOS.$_SESSION['empresa_idempresa'].'.importando');

// Muestro la ventana con lista previa
ventana_inicio($i18n_funciones[73].' '.$i18n_funciones[$modulo]);
{
    contenido_inicio();
    {
        entrada('hidden','random','random',$random);
        entrada('hidden','paso','paso', 1);

        texto('resaltado', '', $i18n_funciones[146]);
        texto('texto', '', $i18n_funciones[147].$nombre_amigable.': '.$total_registros);
        texto('texto', '', $i18n_funciones[148].$nombre_amigable.': '.$total_registros_importados);
        texto('texto', '', $i18n_funciones[149].$nombre_amigable.': '.$total_registros_actualizados);
        texto('texto', '', $i18n_funciones[264].$nombre_amigable.': '.$total_registros_descartados);
        foreach ($array_total_registros_tablas_relacionadas as $key => $value) {
            if ($key != 'Datos Extras'){
                if ($key && array_search($key, $array_precios_stock) === false){
                    texto('texto', '', $i18n_funciones[147].$key.': '.$value["total"]);
                    texto('texto', '', $i18n_funciones[148].$key.': '.$value["total_importados"]);
                    texto('texto', '', $i18n_funciones[150].$key.': '.($value["total"] - $value["total_importados"]));
                }
            } else {
                texto('texto', '', $i18n_funciones[147].$key.': '.$array_total_registros_tablas_relacionadas["Datos Extras"]["total_importados"]);
                texto('texto', '', $i18n_funciones[148].$key.': '.$total_datos_extras);
                texto('texto', '', $i18n_funciones[150].$key.': '.($array_total_registros_tablas_relacionadas["Datos Extras"]["total_importados"] -  $total_datos_extras));
            }
        }
    }
    contenido_fin();

    botones(array(
        array('tipo' => 'nueva', 'valor' => $i18n_funciones[98]),
    ));

}
ventana_fin();

?>
<script type="text/javascript" src="<?= $_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/js/modulos/herramientas.js' ?>"></script>
