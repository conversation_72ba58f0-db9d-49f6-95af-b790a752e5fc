<?php
switch ($boton) {
    case $i18n[6]: // Mostrar mensajes no vencidos
        consulta_sql("UPDATE controles SET mensajes_ocultar='0' WHERE idusuario=".$_SESSION['usuario_idusuario']." LIMIT 1");
        $_SESSION['control_mensajes_ocultar'] = 0;
        break;

    case $i18n[7]: // Ocultar mensajes no vencidos
        consulta_sql("UPDATE controles SET mensajes_ocultar='1' WHERE idusuario=".$_SESSION['usuario_idusuario']." LIMIT 1");
        $_SESSION['control_mensajes_ocultar'] = 1;
        break;

    case $i18n_funciones[29]: // Eliminar
        $marcas = recibir_marcas('mensaje_');
        if (count($marcas)) {
            $sql = "DELETE FROM mensajes WHERE idmensaje IN (";
            foreach ($marcas as $idmensaje) {
                $sql.= $idmensaje.',';
            }
            $sql[mb_strlen($sql) - 1] = ')';
            consulta_sql($sql);
        }
        break;

    case $i18n[65]: // Posponer a mañana
        $marcas = recibir_marcas('mensaje_');
        if (count($marcas)) {
            $nueva_fecha = date("Y-m-d", time() + 24 * 3600);
            $sql = "UPDATE mensajes SET fecha = CONCAT('$nueva_fecha ', TIME(fecha)), visto = 0"
                . " WHERE idmensaje IN (".implode(',', $marcas).")";
            consulta_sql($sql);
        }
        break;

    case $i18n[68]: // Marcar como leído
        $marcas = recibir_marcas('mensaje_');
        if (count($marcas)) {
            $sql = "UPDATE mensajes SET visto = 1"
                . " WHERE idmensaje IN (".implode(',', $marcas).")";
            consulta_sql($sql);
        }
        break;

    case $i18n[69]: // Marcar como no leído
        $marcas = recibir_marcas('mensaje_');
        if (count($marcas)) {
            $sql = "UPDATE mensajes SET visto = 0"
                . " WHERE idmensaje IN (".implode(',', $marcas).")";
            consulta_sql($sql);
        }
        break;
}

$sql = "SELECT *
    FROM mensajes
    WHERE tipo IN ('Recordatorio','Personal')
        AND idusuario = '".$_SESSION['usuario_idusuario']."'
    ".($_SESSION['control_mensajes_ocultar'] ? "AND fecha < '".date('Y-m-d', (time() + 60 * 60 * 24))."'" : '')."
    ORDER BY tipo ASC, fecha DESC, idmensaje
    LIMIT ".($_SESSION['control_agrandar'] + 1);
$resultado_sql = consulta_sql($sql);
$cantidad_mensajes = contar_sql($resultado_sql);

ventana_inicio($i18n[1], '100', array(array('url' => 'mensajes.php?a=alta', 'a' => 'alta', 'title' => $i18n[3])));
{
    contenido_inicio();
    {
        if ($cantidad_mensajes || ($_SESSION['control_mensajes_ocultar']
            && contar_sql(consulta_sql(
                "SELECT idmensaje
                FROM mensajes
                WHERE idusuario = '".$_SESSION['usuario_idusuario']."'
                    AND fecha >= '".date('Y-m-d', (time() + 60 * 60 * 24))."'
                LIMIT 1")))) {

            linea_inicio('titulo', 3);
            {
                marcas_todas('mensajes_todos');
                celda('imagen', '', 'imagen');
                celda('texto', $i18n[10]);
            }
            linea_fin(array(
                array('tipo' => 'ajax', 'url' => 'mensajes_listar', 'a' => 'actualizar'),
                array('tipo' => 'desplegable', 'a' => 'realizar', 'title' => $i18n_funciones[274], 'url' => 'mensajes_listar', 'opciones' => 'class="realizar"', 'desplegable' => array(
                    array('a' => $i18n[65]),
                    array('a' => $i18n[68]),
                    array('a' => $i18n[69]),
                    array('a' => $i18n_funciones[29]))), // NO FUNCIONA porque se pisan las funciones del a y confirma 'opciones' => 'onclick="return confirma('."'$i18n[67]'".')"'),
                array('tipo' => 'desplegable', 'a' => 'filtrar', 'title' => $i18n_funciones[70], 'url' => 'mensajes_listar', 'desplegable' => array(
                    array('a' => ($_SESSION['control_mensajes_ocultar'] ? $i18n[6] : $i18n[7]))
            ))));

            $mostrando = $_SESSION['control_agrandar'];
            while (($mensaje = array_sql($resultado_sql)) && $mostrando) {
                $mostrando--;

                $mensaje['texto'] = formatearMontoTexto($mensaje['texto']);
                // Tipo de mensaje
                switch ($mensaje['tipo']) {
                    case $i18n[12]:
                        $temp_icono_tipo = array('tipo' => 'ok', 'title' => $i18n[13]);
                        break;

                    case $i18n[14]:
                        $temp_icono_tipo = array('tipo' => 'recordatorio', 'title' => $i18n[15]);
                        if (substr($mensaje['fecha'], 0, 10) != '0000-00-00') {
                            $agregado = '';
                            if (date("d/m/Y", strtotime($mensaje['fecha'])) == date("d/m/Y")) {
                                $agregado = $i18n[16];
                            } elseif (date("d/m/Y", strtotime($mensaje['fecha'])) == date("d/m/Y", (time() + 60 * 60 * 24))) {
                                $agregado = $i18n[17];
                            } elseif (date("d/m/Y", strtotime($mensaje['fecha'])) == date("d/m/Y", (time() - 60 * 60 * 24))) {
                                $agregado = $i18n[18];
                            } else {
                                $agregado = $i18n[20].date("d/m/Y", strtotime($mensaje['fecha']));
                            }

                            if ($agregado != '' && date("H:i", strtotime($mensaje['fecha'])) != "00:00") {
                                $mensaje['texto'] = $agregado.$i18n[21].date("H:i", strtotime($mensaje['fecha'])).$i18n[23].$mensaje['texto'];
                            } else {
                                $mensaje['texto'] = $agregado.$i18n[23].$mensaje['texto'];
                            }
                        }
                        break;

                    case $i18n[24]:
                        $temp_icono_tipo = array('tipo' => 'info', 'title' => $i18n[25]);
                        break;

                    case $i18n[26]:
                        $temp_icono_tipo = array('tipo' => 'alerta', 'title' => $i18n[27]);
                        break;

                    case $i18n[28]:
                        $temp_icono_tipo = array('tipo' => 'mensaje', 'title' => $i18n[29]);
                        if ($mensaje['idremitente'])
                            $mensaje['texto'] = campo_sql(consulta_sql("SELECT nombre FROM usuarios WHERE idusuario='".$mensaje['idremitente']."'"), 0).$i18n[58].': '.$mensaje['texto'];
                        break;

                    case $i18n[60]:
                        $temp_icono_tipo = array('tipo' => 'notificacion', 'title' => $i18n[60]);
                        break;

                    case $i18n[63]:
                        $temp_icono_tipo = array('tipo' => 'ayuda', 'title' => $i18n[63]);
                        break;
                }

                // Boton modificar
                if ($mensaje['tipo'] == 'Recordatorio') {
                    $temp_boton_mod = array('tipo' => 'imagen', 'url' => 'mensajes.php?a=mod&id='.$mensaje['idmensaje'], 'a' => 'mod', 'title' => $i18n[30]);
                } elseif ($mensaje['tipo'] == 'Personal') {
                    $temp_boton_mod = array('tipo' => 'imagen', 'url' => 'mensajes.php?a=mod&id='.$mensaje['idmensaje'], 'a' => 'mod', 'title' => $i18n[31]);
                } else {
                    $temp_boton_mod = array('tipo' => 'imagen', 'a' => 'mod_no', 'title' => $i18n[32]);
                }

                // Mensaje borrado o desabilitado
                if ($mensaje['tipo'] == 'Confirmacion' || $mensaje['tipo'] == 'Alerta') {
                    $temp_boton_baja = array('tipo' => 'imagen', 'a' => 'baja_no', 'title' => $i18n[33]);
                    consulta_sql("DELETE FROM mensajes WHERE idmensaje='".$mensaje['idmensaje']."' LIMIT 1");
                } else {
                    $temp_boton_baja = array('tipo' => 'ajax', 'url' => 'mensajes.php?a=baja&id='.$mensaje['idmensaje'], 'a' => 'baja', 'title' => $i18n[34]);
                }

                $mensaje_enlace = $mensaje['tipo'] == 'Consulta'
                    ? 'tickets.php?a=ver&id='.$mensaje['idremitente']
                    : 'mensajes.php?a=ver&id='.$mensaje['idmensaje'];
                linea_inicio('fila', 3, $mensaje_enlace, ['class' => ($mensaje['visto'] ? '' : 'linea_destacada')]);
                {
                    marca('mensaje_'.$mensaje['idmensaje']);
                    celda('imagen', $temp_icono_tipo['title'], 'imagen', $temp_icono_tipo['tipo']);
                    celda('largo', $mensaje['texto'], 'auto');
                }
                linea_fin(array(
                    $temp_boton_mod,
                    $temp_boton_baja,
                    array('tipo' => 'exportar', 'url' => 'mensajes.php?a=exportar&id='.$mensaje['idmensaje'], 'a' => 'exportar', 'title' => $i18n[38])
                ));
            }

            // Para cuando hay mensajes pero están ocultos
            if (!$cantidad_mensajes) {
                linea_inicio();
                {
                    celda('texto', $i18n[59]);
                }
                linea_fin();
            }

            if ($cantidad_mensajes > $_SESSION['control_agrandar']) {
                agrandar(array(
                    array('valor' => $i18n_funciones[20], 'url' => 'mensajes_agrandar', 'cantidad' => $_SESSION['control_agrandar']),
                    array('valor' => $i18n_funciones[18], 'url' => 'mensajes_agrandar', 'cantidad' => $_SESSION['control_agrandar'], 'opciones' => 'todos')
                ));
            }

        } else {
            linea_inicio();
            {
                celda('texto', $i18n[61]);
            }
            linea_fin();
        }
    }
    contenido_fin();
}
ventana_fin();
