<?php

if ($_SESSION['empresa_estado'] == 'demo' && $boton == $i18n_funciones[22]) {
    mensajes_alta($i18n[142]);
    $boton = false;
}

switch ($boton) {
    case $i18n_funciones[22]: // Aceptar
        $datos = recibir_matriz(array(
        array('texto', 'nombre'),
        array('mail', 'mail'),
        array('texto', 'url'),
        array('texto', 'telefonos'),
        array('texto', 'domicilio'),
        array('entero', 'idlocalidad'),
        array('texto', 'razonsocial'),
        array('cuit', 'cuit'),
        array('texto', 'inicio'),
        array('texto', 'ingresosbrutos'),
        array('texto', 'emitir_impuestos'),
        array('texto', 'tributo_5329')
    ));
        $datos['cuit'] = str_replace('-', '', $datos['cuit']);
        if (!$datos['nombre']) {
            mensajes_alta($i18n[143]);
            $boton = false;
        } else {

            if (strlen($datos['cuit']) != 11)
                $datos['cuit'] = '';

            if ($datos['cuit']
                && $_SESSION['configuracion_cuit'] != $datos['cuit']) {

                $empresa_con_cuit_sql = consulta_sql(
                    "SELECT idempresa, nombre
                    FROM empresas
                    WHERE cuit = '{$datos['cuit']}'
                        AND estado = 'activada'
                        AND idempresa != {$_SESSION['empresa_idempresa']}", 'saasargentina');
                if (contar_sql($empresa_con_cuit_sql) || existe_wsfe($datos['cuit'], 'crt')) {
                    $mail = "La empresa N° {$_SESSION['empresa_idempresa']} de nombre {$_SESSION['empresa_nombre']} "
                    ."(<a href='".URL_SAAS."/saas.php?a=verempresa&id=".$_SESSION['empresa_idempresa']."'>".URL_SAAS."/saas.php?a=verempresa&id=".$_SESSION['empresa_idempresa']."</a>)"
                    ." configuró un CUIT (".mostrar_cuit($datos['cuit']).") repetido:";
                    if (existe_wsfe($datos['cuit'], 'crt'))
                        $mail.= "<br>- El certificado de ARCA YA existe.";
                    else
                        $mail.= "<br>- El certificado de ARCA NO existe.";
                    while ($empresa_con_cuit = array_sql($empresa_con_cuit_sql))
                        $mail.= "<br>- El CUIT está en uso en la empresa N° {$empresa_con_cuit['idempresa']} de nombre {$empresa_con_cuit['nombre']} "
                            ."(<a href='".URL_SAAS."/saas.php?a=verempresa&id=".$empresa_con_cuit['idempresa']."'>".URL_SAAS."/saas.php?a=verempresa&id=".$empresa_con_cuit['idempresa']."</a>).";
                    email_queue(MAIL_SERVIDOR, MAIL_INFO, "CUIT Repetido en empresa {$_SESSION['empresa_idempresa']}", $mail);
                }

                consulta_sql(
                    "UPDATE empresas
                    SET cuit = '{$datos['cuit']}'
                    WHERE idempresa = '{$_SESSION['empresa_idempresa']}'", 'saasargentina');
            }

            if ($_SESSION['configuracion_cuit']
                && existe_wsfe($_SESSION['configuracion_cuit'], 'crt')) {
                $datos['razonsocial'] = $_SESSION['configuracion_razonsocial'];
                $datos['cuit'] = $_SESSION['configuracion_cuit'];
            }

            consulta_sql("UPDATE configuraciones SET
                    " . (!existe_wsfe($_SESSION['configuracion_cuit'], 'req') ? "razonsocial='" . $datos['razonsocial'] . "', cuit='" . $datos['cuit'] . "'," : "") . "
                    mail='" . $datos['mail'] . "',
                    url='" . $datos['url'] . "',
                    telefonos='" . $datos['telefonos'] . "',
                    domicilio='" . $datos['domicilio'] . "',
                    idlocalidad='" . $datos['idlocalidad'] . "',
                    inicio='" . fecha_sql($datos['inicio']) . "',
                    ingresosbrutos='" . $datos['ingresosbrutos'] . "',
                    emitir_impuestos='" . $datos['emitir_impuestos'] . "',
                    tributo_5329='" . $datos['tributo_5329'] . "'
                LIMIT 1");

            foreach ($datos as $key => $value) {
                if (!in_array($key, array('razonsocial', 'cuit')) || (!existe_wsfe($_SESSION['configuracion_cuit'], 'req')))
                    $_SESSION['configuracion_' . $key] = $value;
            }
            consulta_sql("UPDATE empresas SET nombre='" . $datos['nombre'] . "' WHERE idempresa='" . $_SESSION['empresa_idempresa'] . "' LIMIT 1", 'saasargentina');

            $datos_cliente = array_sql(consulta_sql("SELECT idcliente FROM empresas WHERE idempresa = '" . $_SESSION['empresa_idempresa'] . "' LIMIT 1", 'saasargentina'));

            $datos_viejos = array_sql(consulta_sql("SELECT * FROM clientes WHERE idcliente = '" . $datos_cliente['idcliente'] . "' LIMIT 1", 'admin'));

            if ($datos_cliente['idcliente']) {
                $actualizar = array();
                $obsinterna = escape_sql($datos_viejos['obsinterna']);
                foreach(array('nombre','telefonos','razonsocial', 'cuit', 'domicilio') as $campo) {
                    if ($datos_viejos[$campo] != $datos[$campo]) {
                        $actualizar[] = $campo . " = '" . $datos[$campo] . "'";
                        $obsinterna.= '<br>' . $campo . ' anterior al ' . date("d-m-Y") . ': ' . escape_sql($datos_viejos[$campo]);
                    }
                }
                if (count($actualizar)) {
                    consulta_sql(
                        "UPDATE clientes
                        SET " . implode(",", $actualizar) . ", obsinterna = '" . $obsinterna . "'
                        WHERE idcliente='" . $datos_cliente['idcliente'] . "'
                        LIMIT 1", 'admin');
                }
            }

            if ($datos['emitir_impuestos'] && $datos['tributo_5329']
                && !contar_sql(consulta_sql("SELECT idtributo FROM categorias_tributos WHERE auto = '5329' LIMIT 1"))) {
                consulta_sql("INSERT INTO categorias_tributos SET
                    idtipotributo = '6',
                    nombre = 'Percepción de IVA (Res. 5329/2023)',
                    auto = '5329'
                ");
            }

            $_SESSION['empresa_nombre'] = $datos['nombre'];
            ir_atras();
        }
        break;

    case $i18n_funciones[23]: // Cancelar
        ir_atras();
        break;

    default:
        $datos = array_sql(consulta_sql("SELECT mail, url, telefonos, domicilio, idlocalidad, razonsocial, cuit, inicio, ingresosbrutos, emitir_impuestos, tributo_5329 FROM configuraciones LIMIT 1"));
        break;
}

ventana_inicio($i18n[258]);
{
    //Datos de la empresa
    contenido_inicio($i18n[20], '100', false, false, $i18n[285]);
    {
        texto('texto', $i18n[25], $_SESSION['empresa_mail']);
        texto('texto', $i18n[33], $_SESSION['configuracion_tipoiva']);
    }
    contenido_fin();

    //Datos de contacto
    contenido_inicio($i18n[18]);
    {
        entrada('texto', 'nombre', $i18n[24], $_SESSION['empresa_nombre'], '33', '100');
        entrada('email', 'mail', $i18n[27], $datos['mail'], '33', '320');
        entrada('texto', 'url', $i18n[403], $datos['url'], '33', '320');
        entrada('texto', 'domicilio', $i18n[28], $datos['domicilio'], '33', '150');
        entrada('texto', 'telefonos', $i18n[26], $datos['telefonos'], '33', '150');
        selector('idlocalidad', $i18n[29], $datos['idlocalidad'], '33', 'categorias_localidades', 'nombre', true, true, true, $i18n[554]);
    }
    contenido_fin();

    //Datos de facturación
    contenido_inicio($i18n[19], '100', false, false, $i18n[141]);
    {
        if ($_SESSION['configuracion_cuit']
            && (existe_wsfe($_SESSION['configuracion_cuit'], 'req')
                || existe_wsfe($_SESSION['configuracion_cuit'], 'crt'))) {
            texto('texto', $i18n[31], '<br>'.$datos['razonsocial'], '25');
            texto('texto', $i18n[32], '<br>'.$datos['cuit'], '25');
            entrada('fecha', 'inicio', $i18n[35], $datos['inicio'], '25');
            entrada('texto', 'ingresosbrutos', $i18n[34], $datos['ingresosbrutos'], '25', '20');

        } else {
            entrada('texto', 'razonsocial', $i18n[31], $datos['razonsocial'], '25', '150', false, 'id="razonsocial_texto"');
            entrada('cuit', 'cuit', $i18n[32], $datos['cuit'], '25', false, false, 'autocomplete="off"');
            entrada('fecha', 'inicio', $i18n[35], $datos['inicio'], '25');
            entrada('texto', 'ingresosbrutos', $i18n[34], $datos['ingresosbrutos'], '25', '20');
        }
        if (!$_SESSION['sistema_gratis'])
            texto('italica', false, $i18n[507], false, false, 'info');
    }
    contenido_fin();

    contenido_inicio($i18n[96]);
    {
        marcas('', 'auto', [
            ['nombre' => 'emitir_impuestos', 'valor' => $datos['emitir_impuestos'], 'titulo' => $i18n[649], 'ayuda_puntual' => $i18n[657], 'opciones' => $_SESSION['sistema_gratis'] ? "disabled='disabled'" : ""],
            ['nombre' => 'tributo_5329', 'valor' => $datos['tributo_5329'], 'titulo' => $i18n[658], 'ayuda_puntual' => $i18n[659],
                'opciones' => (!$datos['emitir_impuestos'] || $_SESSION['sistema_gratis'] ? "disabled='disabled'" : "")],
        ]);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n_funciones[22]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();
?>
<script type="text/javascript" charset="utf-8">

    function validacion_configuraciones_empresa(boton)
    {
        var mail = $("#marco_configuraciones_empresa input[name='mail']").val();
        if (boton == '<?php echo $i18n_funciones[22]; ?>' && mail != '' && !$("input[name='mail']").valid()) {
            <?php script_validacion_flotante("alerta", $i18n[95], "input[name='mail']"); ?>
            return false;
        } else {
            return true;
        }
    }

    // Cuando se activa el checkbox de emitir impuestos, se habilita el selector de tributo 5329
    $("input[name=emitir_impuestos]").click(function() {
        if ($(this).is(':checked') && confirm('<?php echo $i18n[660]; ?>')) {
            $("input[name='tributo_5329']").removeAttr('disabled');
        } else {
            $("input[name='emitir_impuestos']").removeAttr('checked');
            $("input[name='tributo_5329']").removeAttr('checked');
            $("input[name='tributo_5329']").attr('disabled', 'disabled');
        }
    });
</script>
