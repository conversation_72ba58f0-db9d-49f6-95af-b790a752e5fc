<?php

switch ($boton ? $boton : $a) {
    case $i18n_funciones['cancelar']:
        ir_atras();
        break;

    case $i18n_funciones['aceptar']:
        $datos = recibir_matriz(array(
        array('moneda', 'total'),
        array('fecha', 'fecha'),
        array('entero', 'idformapago'),
        array('entero', 'idcaja'),
        array('entero', 'idconcepto'),
        array('largo', 'observacion'),
        array('texto', 'tipo'),
        array('fecha', 'fechacobro'),
        array('entero', 'idbanco'),
        array('entero', 'numero'),
        array('entero', 'idcheque'),
        array('texto', 'titular'),
        array('texto', 'mensaje'),
        array('entero', 'idtributo'),
        array('largo', 'retencion_observacion'),
        array('texto', 'devolucion'),
        array('entero', 'idcliente'),
        array('entero', 'idrelacion'),
        array('entero', 'idproveedor'),
        array('texto', 'saldo'),
        array('texto', 'MP_operation_id'),
        array('entero', 'idmoneda')
    ));

        // Puse esto acá, porque si ingreso un valor > saldo, al volver del break, no obtengo el id nuevamente
        $idventa = $datos['idventa'] = recibir_variable('entero', 'idventa', false);

        if ($datos['idventa']) {

            $venta = array_sql(consulta_sql(
                "SELECT ventas.estado, ventas.situacion, ventas.fecha, ventas.total, ventas.numero, ventas.tiporelacion, ventas.idrelacion, ventas.operacioninversa, ventas.idmoneda,
                    (SELECT saldo FROM saldos WHERE tiporelacion = 'ventas' AND idrelacion = ventas.idventa LIMIT 1) AS saldo,
                    (SELECT simbolo FROM monedas WHERE idmoneda = ventas.idmoneda LIMIT 1) AS simbolo,
                    (SELECT total FROM ventaspagos WHERE idventapago = '$id' LIMIT 1) AS total_anterior,
                    (SELECT idmoneda FROM ventaspagos WHERE idventapago = '$id' LIMIT 1) AS idmoneda_anterior
                FROM ventas
                WHERE idventa = '{$datos['idventa']}'"));

            if (($datos['idmoneda'] == $venta['idmoneda']
                && ($datos['total'] - $venta['total_anterior']) > $venta['saldo'])
                || ($datos['idmoneda'] != $venta['idmoneda']
                    && (cotizacion($venta['idmoneda'], $datos['idmoneda'], $datos['total']) - cotizacion($venta['idmoneda'], $venta['idmoneda_anterior'], $venta['total_anterior'])) > $venta['saldo'])) {

                mensajes_alta($i18n[80].$venta['simbolo'].' '.number_format($venta['saldo'], 2, '.', ''));
                $datos['total'] = (float)$datos['saldo'];
                $a = 'modpago';
                ?>
                    <script>
                        seleccionar_tipo_caja();
                    </script>
                <?php
                break;
            }
        }

        if ($_SESSION['perfil_idperfil'] != '1') {
            $cajas_sql = consulta_sql("SELECT * FROM categorias_cajas
                LEFT JOIN perfilesxcajas ON categorias_cajas.idtipocaja=perfilesxcajas.idtipocaja
                WHERE estado='1' AND (categorias_cajas.compartida='1'
                    OR (idperfil='".$_SESSION['perfil_idperfil']."' AND mod_='1'))
                    AND idcaja = '".$datos['idcaja']."'
            ");
            if (!$cajas_sql)
                ir_atras($i18n[291]);
        }

        // PREPARAR NUEVOS DATOS
        $datos['idusuario'] = $_SESSION['usuario_idusuario'];
        $datos['tiporelacion'] = 'clientepago';
        // Hay datos que no pueden cambiar, así que los traigo desde la db
        $ventapago = array_sql(consulta_sql(
            "SELECT idventapago, total, idcliente, idformapago, MP_operation_id,
                (SELECT idcaja FROM movimientosxcajas WHERE tiporelacion = 'clientepago' AND idrelacion = '$id') AS idcaja
            FROM ventaspagos
            WHERE idventapago = '$id'"));

        $devolucion = $ventapago['total'] < 0
            ? true
            : false;
        $datos['idcliente'] = $ventapago['idcliente'];
        // PARCHE: No dejo modificar la forma de pago en esta actualización
        $datos['idformapago'] = $ventapago['idformapago'];

        $tipoformapago = campo_sql(consulta_sql(
            "SELECT tipo FROM tablas_formasdepago WHERE idformapago = '".$datos['idformapago']."' LIMIT 1"));

        $formapago_anterior = array_sql(consulta_sql(
            "SELECT tiporelacion, idrelacion,
                (SELECT idmensaje FROM cheques
                    WHERE ventaspagos.tiporelacion = 'cheque'
                        AND cheques.idcheque = ventaspagos.idrelacion) AS idmensaje
            FROM ventaspagos WHERE idventapago = '".$id."' LIMIT 1")); //averiguo forma de pago anterior
        $datos['idrelacion'] = $formapago_anterior['idrelacion'];

        if ($datos['total'] <= 0) {   // Val x total para todos
                ir_atras($i18n[77]);
                break;
        }
        if ($tipoformapago == 'cheque')
        {
            if (!$datos['numero']) {  // Val x número
                ir_atras($i18n[264]);
                break;
            } else {
                if (!$datos['fechacobro'] || !strtotime($datos['fechacobro'])) {  //Val x fecha
                    ir_atras($i18n[262]);
                    break;
                } else {
                    if (!$datos['titular']) { //Val x nombre
                        ir_atras($i18n[263]);
                        break;
                    } else {
                        if ($formapago_anterior['tiporelacion'] == $tipoformapago) {  //Si era cheque y sigue siendo cheque, update
                            if (!contar_sql(consulta_sql("SELECT idcheque, numero FROM cheques WHERE numero = '".$datos['numero']."' AND idcheque != '".$datos['idrelacion']."' LIMIT 1")))
                            {
                                consulta_sql("UPDATE cheques SET
                                    fechacobro = '".fecha_sql($datos['fechacobro'])."',
                                    idbanco = '".$datos['idbanco']."',
                                    numero = '".$datos['numero']."',
                                    titular = '".$datos['titular']."'
                                    WHERE idcheque = '".$datos['idrelacion']."'
                                    LIMIT 1"
                                );
                            } else {
                                script_flotante("alerta", $i18n[265], 7000);  // Alerta x cheque duplicado
                                break;
                            }
                        } else if ($formapago_anterior['tiporelacion'] != 'cheque') { //si antes era otra cosa y ahora es cheque, insert cheque
                            consulta_sql("INSERT INTO cheques SET
                                tipo = '".$datos['tipo']."',
                                fechacobro = '".fecha_sql($datos['fechacobro'])."',
                                idbanco = '".$datos['idbanco']."',
                                numero = '".$datos['numero']."',
                                titular = '".$datos['titular']."'
                            ");
                            $datos['tipo'] = 'cheque';
                            $datos['idrelacion'] = id_sql();
                        }
                    }
                }
            }
        } elseif ($tipoformapago == 'retencion') {
            if ($formapago_anterior['tiporelacion'] == $tipoformapago) {  //si era retención y sigue siendo retención
                consulta_sql("UPDATE retenciones SET
                    idtributo = '".$datos['idtributo']."',
                    observacion = '".$datos['retencion_observacion']."'
                    WHERE idretencion = '".$datos['idrelacion']."'
                    LIMIT 1
                ");
            } else if ($formapago_anterior['tiporelacion'] != 'retencion') { //si antes era otra cosa y ahora es retencion, insert retención
                consulta_sql("INSERT INTO retenciones SET
                    idtributo = '".$datos['idtributo']."',
                    nombre = '".$datos['retencion_observacion']."'
                ");
                $datos['tipo'] = 'retencion';
                $datos['idrelacion'] = id_sql();
            }
        } else {
            //por si antes era otra cosa que no sea cheque o retención o cambia a otra cosa que nos ea cheque y retención, por las dudas, vació variables
            $datos['tiporelacion'] = '';
            $datos['idrelacion'] = '';
        }
        //delete si antes era retención o cheque (con su respectivo mensaje)
        if ($formapago_anterior['tiporelacion'] != $tipoformapago) {
            if ($formapago_anterior['tiporelacion'] == 'retencion') {
                consulta_sql("DELETE FROM retenciones WHERE idretencion = '".$formapago_anterior['idrelacion']."' LIMIT 1");
            } else if ($formapago_anterior['tiporelacion'] == 'cheque') {
                consulta_sql("DELETE FROM mensajes WHERE idmensaje = '".$datos['idmensaje']."' LIMIT 1");
                consulta_sql("DELETE FROM cheques WHERE idcheque = '".$formapago_anterior['idrelacion']."' LIMIT 1");
            }
        }

        $buscar_monedas = [
            'clientes' => $datos['idcliente'],
            'cajas' => $datos['idcaja'],
        ];
        if ($datos['idventa'])
            $buscar_monedas['ventas'] = $datos['idventa'];
        $idmonedas = idmonedas($buscar_monedas);


        //UPDATE SQL
        consulta_sql("UPDATE ventaspagos SET
                total = '".($datos['total'] * ($devolucion ? -1 : 1))."',
                idformapago = '".$datos['idformapago']."',
                idmoneda = '".$datos['idmoneda']."',
                fecha = '".fecha_sql($datos['fecha'])."',
                observacion = '".escape_sql($datos['observacion'])."',
                tiporelacion = '".$tipoformapago."',
                idrelacion = '".$datos['idrelacion']."',
                MP_operation_id = '".$datos['MP_operation_id']."'
            WHERE idventapago = '".$id."'
            LIMIT 1
        ");
        consulta_sql("UPDATE ventasxclientes SET
                fecha = '".fecha_sql($datos['fecha'])."',
                total = '".cotizacion($idmonedas['clientes'], $datos['idmoneda'], $datos['total'] * ($devolucion ? -1 : 1))."'
            WHERE idtipoventa = '".($devolucion ? -1 : 0)."'
            AND id = '".$id."'
            LIMIT 1
        ");
        consulta_sql("UPDATE movimientosxcajas SET
                fecha = '".fecha_sql($datos['fecha'])."',
                total = '".cotizacion($idmonedas['cajas'], $datos['idmoneda'], $datos['total'] * ($devolucion ? -1 : 1))."',
                idconcepto = '".$datos['idconcepto']."',
                idcaja = '".$datos['idcaja']."'
            WHERE tiporelacion = 'clientepago'
            AND idrelacion = '".$id."'
            LIMIT 1
        ");

        if ($tipoformapago == 'cheque'
            && ($formapago_anterior['idmensaje'] || $datos['mensaje'])) {
            // Si es cheque y el anterior también, tengo que actualizar el idmensaje
            // Si no tenía mensaje y en el select eligió crearlo, también entro en el if

            $dias_antes = $datos['mensaje'] - 1; // Cantidad de días antes de la fecha de cobro
            $fechamensaje = fecha_sql($datos['fechacobro']);
            $fechamensaje = date("Y-m-d", strtotime("$fechamensaje -$dias_antes day"));
            $datos['cliente'] = campo_sql(consulta_sql("SELECT nombre FROM clientes WHERE idcliente = '".$datos['idcliente']."' LIMIT 1"));
            $mensaje = '<p>' .
                $i18n[272] . '<a href="clientes.php?a=ver&id=' . $datos['idcliente'] . '">' . $datos['cliente'] .'</a><br>'
                .$i18n[273] . '<a href="cheques.php?a=ver&id=' . $datos['idrelacion'] . '">' . $datos['numero'] .'</a><br>'
                .$i18n[274] . $datos['total'] .'<br>'
                .$i18n[275] . $datos['fechacobro'] . '</p>';

            if ($datos['mensaje']) {
                consulta_sql(
                    "INSERT INTO mensajes SET
                    idusuario = '".$datos['idusuario']."',
                    tipo = 'Recordatorio',
                    fecha = '".$fechamensaje."',
                    texto = '".escape_sql($mensaje)."'"
                    );
                $idmensaje = id_sql();
                consulta_sql("UPDATE cheques SET idmensaje = '".$idmensaje."' WHERE idcheque = '".$datos['idrelacion']."' LIMIT 1");

            } else {
                consulta_sql(
                    "UPDATE mensajes SET
                    idusuario = '".$datos['idusuario']."',
                    tipo = 'Recordatorio',
                    fecha = '".$fechamensaje."',
                    texto = '".escape_sql($mensaje)."'
                    WHERE idmensaje = '".$datos['idmensaje']."'
                ");
            }
        }
        // Actualizar saldos
        $a = 'modpago';
        $diferencia = ($devolucion ? -1 : 1) * (abs($ventapago['total']) - abs($datos['total']));
        if ($datos['idventa']) {
            actualizar_saldo('ventas', $datos['idventa'], cotizacion($idmonedas['ventas'], $datos['idmoneda'], $diferencia));
            if ($datos['tiporelacion'] == 'servicio')
                actualizar_saldo('ventasxservicios', $datos['idrelacion'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia));
        }
        actualizar_saldo('clientes', $datos['idcliente'], cotizacion($idmonedas['clientes'], $datos['idmoneda'], $diferencia));
        if ($venta['idcaja'] == $datos['idcaja']) {
            actualizar_saldo('cajas', $datos['idcaja'], cotizacion($idmonedas['cajas'], $datos['idmoneda'], - $diferencia));
        } else {
            actualizar_saldo('cajas', $ventapago['idcaja'], cotizacion($idmonedas['cajas'], $datos['idmoneda'], ($devolucion ? 1 : -1) * abs($ventapago['total'])));
            actualizar_saldo('cajas', $datos['idcaja'], cotizacion($idmonedas['cajas'], $datos['idmoneda'], ($devolucion ? -1 : 1) * abs($datos['total'])));
        }

        // ACTUALIZO CONTROLES
        actualizar_controles(array(
            'ultimoformapagoventa' => $datos['idformapago'],
            'ultimoconceptoventa' => $datos['idconcepto'],
            'ultimotipocajaventa' => campo_sql(consulta_sql("SELECT idtipocaja FROM cajas WHERE idcaja = '".$datos['idcaja']."' LIMIT 1"), 0),
        ));

        ir_atras();
        break;

    case $i18n_funciones['agregar']:
        // RECIBO Y VALIDO DATOS
        $datos = recibir_matriz(array(
        array('moneda', 'total'),
        array('fecha', 'fecha'),
        array('entero', 'idformapago'),
        array('entero', 'idcaja'),
        array('entero', 'idconcepto'),
        array('largo', 'observacion'),
        array('texto', 'tipo'),
        array('fecha', 'fechacobro'),
        array('entero', 'idbanco'),
        array('entero', 'numero'),
        array('texto', 'titular'),
        array('texto', 'mensaje'),
        array('entero', 'idtributo'),
        array('largo', 'retencion_observacion'),
        array('texto', 'devolucion'),
        array('entero', 'idcliente'),
        array('texto', 'saldo'),
        array('texto', 'MP_operation_id'),
        array('entero', 'idnumeroventapago'),
        array('entero', 'idmoneda')
    ));

        // Puse esto acá, porque si ingreso un valor > saldo, al volver del break, no obtengo el id nuevamente
        $idventa = $datos['idventa'] = recibir_variable('entero', 'idventa', false);

        if ($datos['idventa']) {
            $venta = array_sql(consulta_sql(
                "SELECT ventas.estado, ventas.situacion, ventas.fecha, ventas.total, ventas.numero, ventas.tiporelacion, ventas.idrelacion, ventas.operacioninversa, ventas.idmoneda,
                    (SELECT saldo FROM saldos WHERE tiporelacion = 'ventas' AND idrelacion = ventas.idventa LIMIT 1) AS saldo,
                    (SELECT simbolo FROM monedas WHERE idmoneda = ventas.idmoneda LIMIT 1) AS simbolo
                FROM ventas
                WHERE idventa = '{$datos['idventa']}'"));

            if (($datos['idmoneda'] == $venta['idmoneda'] && $datos['total'] > $venta['saldo'])
                || ($datos['idmoneda'] != $venta['idmoneda']
                    && cotizacion($venta['idmoneda'], $datos['idmoneda'], $datos['total']) > $venta['saldo'])) {

                mensajes_alta($i18n[80].$venta['simbolo'].' '.number_format($datos['saldo'], 2, '.', ''));
                $a = 'altapago';
                ?>
                    <script>
                        seleccionar_tipo_caja(); // Andy, se te ocurre otra manera porque sino, contenido_cheque y contenido_retenciones??!!
                    </script>
                <?php
                break;
            }
        }

        if ($_SESSION['perfil_idperfil'] == '1') {
            $cajas_sql = consulta_sql("SELECT * FROM categorias_cajas
                LEFT JOIN perfilesxcajas ON categorias_cajas.idtipocaja=perfilesxcajas.idtipocaja
                WHERE estado='1' AND (categorias_cajas.compartida='1'
                    OR (idperfil='".$_SESSION['perfil_idperfil']."' AND alta='1'))
                    AND idcaja = '".$datos['idcaja']."'
            ");
            if (!$cajas_sql)
                ir_atras($i18n[290]);
        }

        $datos['idusuario'] = $_SESSION['usuario_idusuario'];
        $datos['tiporelacion'] = 'clientepago';

        $devolucion = $datos['devolucion']
            ? true
            : false;

        $tipoformapago = campo_sql(consulta_sql(
            "SELECT tipo FROM tablas_formasdepago WHERE idformapago = '".$datos['idformapago']."' LIMIT 1"));
        // EMPIEZO CON LOS INSERT SQL
        if ($datos['total'] <= 0) {   // Val x total para todos
                ir_atras($i18n[77]);
                break;
        }
        if ($tipoformapago == 'cheque') {
            // Si es cheque lo tengo que dar de alta primero
            if (!$datos['numero']) {  // Val x número
                ir_atras($i18n[264]);
                break;
            } else {
                if (!$datos['fechacobro'] || !strtotime($datos['fechacobro'])) {  //Val x fecha
                    ir_atras($i18n[262]);
                    break;
                } else {
                    if (!$datos['titular']) { //Val x titular
                        ir_atras($i18n[263]);
                        break;
                    } else {
                        consulta_sql("INSERT INTO cheques SET
                            tipo = '".$datos['tipo']."',
                            fechacobro = '".fecha_sql($datos['fechacobro'])."',
                            idbanco = '".$datos['idbanco']."',
                            numero = '".$datos['numero']."',
                            titular = '".$datos['titular']."'
                        ");
                        $datos['tipo'] = 'cheque';
                        $datos['idrelacion'] = id_sql();
                    }
                }
            }
        } elseif ($tipoformapago == 'retencion') {
            // Si es retención la tengo que dar de alta primero
            consulta_sql("INSERT INTO retenciones SET
                idtributo = '".$datos['idtributo']."',
                observacion = '".$datos['retencion_observacion']."'
            ");
            $datos['tipo'] = 'retencion';
            $datos['idrelacion'] = id_sql();
        }

        consulta_sql("INSERT INTO ventaspagos SET
            idcliente = '".$datos['idcliente']."',
            idusuario = '".$datos['idusuario']."',
            idventa = '".$datos['idventa']."',
            idmoneda = '".$datos['idmoneda']."',
            fecha = '".fecha_sql($datos['fecha'])."',
            closed_at = NOW(),
            idformapago = '".$datos['idformapago']."',
            total = '".($datos['total'] * ($devolucion ? -1 : 1))."',
            observacion = '".escape_sql($datos['observacion'])."',
            tiporelacion = '".$datos['tipo']."',
            idrelacion = '".$datos['idrelacion']."',
            MP_operation_id = '".$datos['MP_operation_id']."',
            idnumeroventapago = '".$datos['idnumeroventapago']."'
        ");
        $datos['idventapago'] = id_sql();

        // PREPARAR NUEVOS DATOS
        if ($devolucion) {
            $datos['idtipoventa'] = -1;
            $datos['detalle'] = $i18n[250]. 'DP'.completar_numero($datos['idventapago'], 8);
        } else {
            $datos['idtipoventa'] = 0;
            $datos['detalle'] = $i18n[125]. 'RP'.completar_numero($datos['idventapago'], 8);
        }

        if ($tipoformapago == 'cheque') {
            $datos['detalle'] = $datos['detalle']. ' | ' .$i18n[287]. ' ' .$datos['numero'];
        }

        if ($datos['idventa']) {
            $venta = array_sql(consulta_sql(
                "SELECT ventas.numero, ventas.idrelacion, ventas.tiporelacion, categorias_ventas.letra, categorias_ventas.puntodeventa, ventas.total, ventas.operacioninversa, ventas.idmoneda
                FROM ventas
                    LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
                WHERE idventa = '".$datos['idventa']."'"));
            $datos['numeroventa'] = $venta['letra'].completar_numero($venta['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8);
        }

        if (!$datos['idnumeroventapago']) {
            consulta_sql("UPDATE ventaspagos SET idnumeroventapago = '".$datos['idventapago']."' WHERE idventapago = '".$datos['idventapago']."'");
            $datos['idnumeroventapago'] = $datos['idventapago'];
        }

        $buscar_monedas = [
            'clientes' => $datos['idcliente'],
            'cajas' => $datos['idcaja'],
        ];
        if ($datos['idventa'])
            $buscar_monedas['ventas'] = $datos['idventa'];
        // if ($venta['tiporelacion'] == 'servicio')
        //     $buscar_monedas['servicios'] = $venta['idrelacion'];
        $idmonedas = idmonedas($buscar_monedas);

        consulta_sql("INSERT INTO ventasxclientes SET
            idcliente = '".$datos['idcliente']."',
            idtipoventa = '".$datos['idtipoventa']."',
            id = '".$datos['idventapago']."',
            fecha = '".fecha_sql($datos['fecha'])."',
            total = '".cotizacion($idmonedas['clientes'], $datos['idmoneda'], $datos['total'] * ($devolucion ? -1 : 1))."',
            numero = '".$datos['numeroventa']."'
        ");
        consulta_sql("INSERT INTO movimientosxcajas SET
            idcaja = '".$datos['idcaja']."',
            idusuario = '".$datos['idusuario']."',
            idconcepto = '".$datos['idconcepto']."',
            fecha = '".fecha_sql($datos['fecha'])."',
            total = '".cotizacion($idmonedas['cajas'], $datos['idmoneda'], $datos['total'] * ($devolucion ? -1 : 1))."',
            detalle = '".$datos['detalle']."',
            tiporelacion = '".$datos['tiporelacion']."',
            idrelacion = '".$datos['idventapago']."'
        ");

        if ($tipoformapago == 'cheque' && $datos['mensaje']) {
            // Si es cheque se genera el recordatorio
            $dias_antes = $datos['mensaje'] - 1; // Cantidad de días antes de la fecha de cobro
            $fechamensaje = fecha_sql($datos['fechacobro']);
            $fechamensaje = date("Y-m-d", strtotime("$fechamensaje -$dias_antes day"));
            $datos['cliente'] = campo_sql(consulta_sql("SELECT nombre FROM clientes WHERE idcliente = '".$datos['idcliente']."' LIMIT 1"));
            $mensaje = '<p>' .
                $i18n[272] . '<a href="clientes.php?a=ver&id=' . $datos['idcliente'] . '">' . $datos['cliente'] .'</a><br>'
                .$i18n[273] . '<a href="cheques.php?a=ver&id=' . $datos['idrelacion'] . '">' . $datos['numero'] .'</a><br>'
                .$i18n[274] . $datos['total'] .'<br>'
                .$i18n[275] . $datos['fechacobro'] . '</p>';
            consulta_sql(
                "INSERT INTO mensajes SET
                idusuario = '".$datos['idusuario']."',
                tipo = 'Recordatorio',
                fecha = '".$fechamensaje."',
                texto = '".escape_sql($mensaje)."'
            ");
            $idmensaje = id_sql();
            consulta_sql("UPDATE cheques SET idmensaje = '".$idmensaje."' WHERE idcheque = '".$datos['idrelacion']."' LIMIT 1");
        }

        // Actualizar saldos
        $diferencia = ($devolucion ? 1 : -1) * $datos['total'];
        if ($datos['idventa']) {
            actualizar_saldo('ventas', $datos['idventa'], cotizacion($idmonedas['ventas'], $datos['idmoneda'], $diferencia));
            if ($datos['tiporelacion'] == 'servicio')
                actualizar_saldo('ventasxservicios', $datos['idrelacion'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia));
        }
        actualizar_saldo('clientes', $datos['idcliente'], cotizacion($idmonedas['clientes'], $datos['idmoneda'], $diferencia));
        actualizar_saldo('cajas', $datos['idcaja'], cotizacion($idmonedas['cajas'], $datos['idmoneda'], - $diferencia));

        // ACTUALIZO CONTROLES
        actualizar_controles(array(
            'ultimoformapagoventa' => $datos['idformapago'],
            'ultimoconceptoventa' => $datos['idconcepto'],
            'ultimotipocajaventa' => campo_sql(consulta_sql("SELECT idtipocaja FROM cajas WHERE idcaja = '".$datos['idcaja']."' LIMIT 1"), 0),
            ));

        // NOS VAMOS
        if ((float)$datos['saldo'] - $datos['total'] == 0 || (float)$datos['saldo'] < $datos['total']) { //Si completo el pago, o es un pago a cuenta
            ir_atras();
        } else {
            ir_ahora('ventas.php?a=verpago&id='.$datos['idnumeroventapago']);
        }
        break;

    case 'altapago':
        $idcliente = recibir_variable('entero', 'idcliente', true);
        $idventa = recibir_variable('entero', 'idventa', true);
        $devolucion = recibir_variable('texto', 'devolucion', true);
        $idnumeroventapago = recibir_variable('entero', 'idnumeroventapago', true);

        $datos = array(
            'idcliente' => $idcliente,
            'idventa' => $idventa,
            'fecha' => fecha_sql('ahora'),
            'idconcepto' => $_SESSION['control_ultimoconceptoventa'],
            'idformapago' => $_SESSION['control_ultimoformapagoventa'],
            'idnumeroventapago' => $idnumeroventapago,
            'idcaja' => campo_sql(consulta_sql(
                "SELECT idcaja
                FROM categorias_cajas
                WHERE idtipocaja = (SELECT ultimotipocajaventa FROM controles WHERE idusuario = '".$_SESSION['usuario_idusuario']."')
                LIMIT 1"), 0),
            'idbanco' => $_SESSION['control_ultimobanco'],
            );

        break;

    case 'modpago':
        $datos = array_sql(consulta_sql(
            "SELECT ventaspagos.idcliente, ventaspagos.total, ventaspagos.fecha, ventaspagos.idformapago, ventaspagos.idventa, ventaspagos.observacion, ventaspagos.MP_operation_id, ventaspagos.idusuario, ventaspagos.idmoneda,
                ventaspagos.tiporelacion, ventaspagos.idrelacion, movimientosxcajas.idcaja, movimientosxcajas.idconcepto,
                cajas.idtipocaja, cajas.fechacierre,
                categorias_cajas.nombre AS caja
            FROM ventaspagos
                LEFT JOIN movimientosxcajas ON (ventaspagos.idventapago = movimientosxcajas.idrelacion AND movimientosxcajas.tiporelacion = 'clientepago')
                LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
                LEFT JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
            WHERE idventapago = '$id' AND movimientosxcajas.tiporelacion = 'clientepago'
            LIMIT 1"));

        $devolucion = $datos['total'] < 0
            ? true
            : false;

        $idtipoventa = campo_sql(consulta_sql("SELECT idtipoventa FROM ventasxclientes WHERE id = '$id' ORDER BY idtipoventa LIMIT 1"));

        if ($datos['fechacierre'] != '0000-00-00 00:00:00') {
            if ($_SESSION['perfil_idperfil'] == '1' || contar_sql(consulta_sql("SELECT idtipocaja FROM perfilesxcajas WHERE idperfil = '".$_SESSION['perfil_idperfil']."' AND idtipocaja = '".$datos['idtipocaja']."' AND mod_cerrada = '1' LIMIT 1")))
                script_flotante('informacion', $i18n[27]);
            else
                ir_atras($i18n[26]);
        }

        // Si es una cotización anterior, no se puede modificar
        if ($_SESSION['modulo_multimoneda'] && es_cotizacion_anterior('ventaspagos', $id))
            ir_ahora('ventas.php?a=verpago&id='.$id, $i18n[394]);

        if ($datos['tiporelacion'] == 'cheque') {
            $cheque = array_sql(consulta_sql(
                "SELECT cheques.*,
                    (SELECT fecha FROM mensajes WHERE mensajes.idmensaje = cheques.idmensaje) AS fecha_mensaje,
                    tablas_bancos.nombre
                FROM cheques
                    LEFT JOIN tablas_bancos ON tablas_bancos.idbanco = cheques.idbanco
                WHERE idcheque = '".$datos['idrelacion']."'
                LIMIT 1")
            );
            if ($cheque['estado'] == 1) { //bloqueo de cheque conciliado en MOD
                ir_atras($i18n[286]);
                break;
            }

        } else if ($datos['tiporelacion'] == 'retencion') {
            $retencion = array_sql(consulta_sql(
                "SELECT retenciones.*,
                    categorias_tributos.nombre AS tributo
                FROM retenciones
                    LEFT JOIN categorias_tributos ON categorias_tributos.idtributo = retenciones.idtributo
                WHERE idretencion = '".$datos['idrelacion']."'
                LIMIT 1")
            );
        }

        break;

    default:
        mostrar_error('Error en default', true);
        break;
}


// DATOS FIJOS PARA LA VISTA
if ($devolucion) { //Paso a negativo una devolución de dinero y distingo RP de DP
    $temp_detalle = 'DP';
    $idtipoventa = '-1';
} else {
    $temp_detalle = 'RP';
    $idtipoventa = '0';
}

$cliente = array_sql(consulta_sql(
    "SELECT clientes.*,
        categorias_localidades.nombre AS localidad,
        tablas_condiciones.nombre AS tipoiva
    FROM clientes
        LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
        LEFT JOIN tablas_condiciones ON clientes.idtipoiva = tablas_condiciones.idtipoiva
    WHERE idcliente = '".$datos['idcliente']."'
    LIMIT 1"));

obsrecordatorio($cliente['obsrecordatorio']);

if ($datos['idventa']) {
    $venta = array_sql(consulta_sql(
        "SELECT ventas.estado, ventas.situacion, ventas.fecha, ventas.total, ventas.numero, ventas.idrelacion, ventas.idmoneda,
                (SELECT saldo FROM saldos WHERE tiporelacion = 'ventas' AND idrelacion = ventas.idventa LIMIT 1) AS saldo,
            categorias_ventas.nombre AS tipoventa, categorias_ventas.tienesituacion, categorias_ventas.letra, categorias_ventas.puntodeventa,
            usuarios.nombre AS usuario
         FROM ventas
            LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
            LEFT JOIN usuarios ON ventas.idusuario = usuarios.idusuario
         WHERE idventa = '".$datos['idventa']."'
         LIMIT 1"));
    $numeroventa = $venta['letra'].completar_numero($venta['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8);

    if ($a == 'altapago')
        $datos['total'] = $venta['saldo'];

}

if ($_SESSION['perfil_idperfil'] == '1')
    $cajas_sql = consulta_sql("SELECT categorias_cajas.idcaja, nombre, tipo FROM categorias_cajas
        INNER JOIN cajas ON categorias_cajas.idcaja = cajas.idcaja
        WHERE estado = '1'
        AND cajas.fechacierre = '0000-00-00 00:00:00' ORDER by categorias_cajas.idcaja = '".$datos['idcaja']."'
    ");
else
    $cajas_sql = consulta_sql("SELECT DISTINCT categorias_cajas.idcaja, nombre, tipo FROM categorias_cajas
        LEFT JOIN perfilesxcajas ON categorias_cajas.idtipocaja = perfilesxcajas.idtipocaja
        INNER JOIN cajas ON categorias_cajas.idcaja = cajas.idcaja
        WHERE estado = '1'
        AND cajas.fechacierre = '0000-00-00 00:00:00'
        AND (categorias_cajas.compartida = '1'
        OR (idperfil = '".$_SESSION['perfil_idperfil']."' AND alta = '1'))
        ORDER by categorias_cajas.idcaja = '".$datos['idcaja']."'
    ");

$cajas = array();
$formasdepago_disponibles = array();
while ($array_tipo_caja = array_sql($cajas_sql)) {
    $cajas[] = array(
        'id' => $array_tipo_caja['idcaja'],
        'valor' => $array_tipo_caja['nombre'],
        'datasets' => array(
            'data-tipoformapago' => $array_tipo_caja['tipo'],
            )
    );
    if (!in_array($array_tipo_caja['tipo'], $formasdepago_disponibles))
        $formasdepago_disponibles[] = "'".$array_tipo_caja['tipo']."'";
}

if (!count($cajas)) {
    ir_atras($i18n[105]);
}

$formasdepago = array();
$formasdepago_sql = consulta_sql("SELECT *
    FROM tablas_formasdepago
    WHERE tipo IN (".implode(',', $formasdepago_disponibles).")");
while ($formadepago = array_sql($formasdepago_sql)) {
    $formasdepago[] = array(
        'id' => $formadepago['idformapago'],
        'valor' => $formadepago['nombre'],
        'datasets' => array(
            'data-tipoformapago' => $formadepago['tipo'])
        );
}
