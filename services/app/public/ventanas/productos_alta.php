<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('entero', 'estado'),
        array('entero', 'estadoventa'),
        array('entero', 'estadocompra'),
        array('entero', 'estadocombo'),
        array('texto', 'combo'),
        array('entero', 'idrubro'),
        array('texto', 'sku'),
        array('texto', 'codigo'),
        array('texto', 'codigoproveedor'),
        array('texto', 'nombre'),
        array('cantidad', 'controlarstock'),
        array('cantidad', 'stocknegativo'),
        array('entero', 'idunidad'),
        array('entero', 'idiva'),
        array('entero', 'idproveedor'),
        array('moneda', 'prod_costo'),
        array('texto', 'mostrartienda'),
        array('texto', 'trazabilidad'),
        array('texto', 'tributo_5329'),
        array('largo', 'observacion'),
        array('largo', 'obstienda'),
        array('largo', 'obsinterna'),
        array('texto', 'ML_item_id'),
        array('texto', 'ML_item_id2'),
        array('entero', 'idcombo'),
        array('entero', 'idlista'),
        array('entero', 'iddeposito'),
        array('entero', 'idmoneda')
    ));
    $datos_listas = recibir_sucursales('listas');
    $datos_depositos = recibir_sucursales('depositos');
    $temp_modulo = recibir_variable('texto', 'temp_modulo', false);

} else {
    $datos = array('ventas_input', 'compras_input', 'idunidad' => 7, 'estado' => 1, 'estadoventa' => 1, 'estadocompra' => 1, 'controlarstock' => 1, 'idiva' => 5, 'prod_costo' => '0.00', 'utilidad' => '0.00', 'prod_precio' => '0.00', 'prod_preciofinal' => '0.00');

    $datos['ventas_input'] = recibir_variable('texto', 'ventas_input', false);
    $datos['compras_input'] = recibir_variable('texto', 'compras_input', false);
    if ($datos['ventas_input']) {
        $datos['nombre'] = $datos['ventas_input'];
        $temp_modulo = 'ventas';

    } else if ($datos['compras_input']) {
        $datos['nombre'] = $datos['compras_input'];
        $datos['prod_costo'] = $datos['prod_precio'];
        $datos['prod_precio'] = 0;
        $temp_modulo = 'compras';

    } else {
        $temp_modulo = 'productos';
    }
}

if ($boton == $i18n[9] && $datos['nombre'] == '') {
    mensajes_alta($i18n[14]);
    $boton = '';

} elseif ($boton == $i18n[9] && ($datos['codigo'] || $datos['ML_item_id'] || $datos['ML_item_id2'])) {

    $resultado_sql = consulta_sql(
        $sql =
        "SELECT idproducto, nombre, codigo, ML_item_id, ML_item_id2
        FROM productos
        WHERE idproducto != '$id'
            AND ((codigo != '' AND codigo = '" . $datos['codigo'] . "')" .
        ($datos['ML_item_id'] ? " OR ML_item_id = '" . $datos['ML_item_id'] . "' OR ML_item_id2 = '" . $datos['ML_item_id'] . "'" : '') .
        ($datos['ML_item_id2'] ? " OR ML_item_id = '" . $datos['ML_item_id2'] . "' OR ML_item_id2 = '" . $datos['ML_item_id2'] . "'" : '') .
        ") LIMIT 1");

    if (contar_sql($resultado_sql)) {
        $producto = array_sql($resultado_sql);

        if ($datos['codigo'] == $producto['codigo']) {
            mensajes_alta($i18n[71] . '<a href="productos.php?a=ver&id=' . $producto['idproducto'] . '" target="_blank">' . $producto['nombre'] . '</a>');
            $boton = '';
        }
        if ($datos['ML_item_id'] && ($datos['ML_item_id'] == $producto['ML_item_id'] || $datos['ML_item_id'] == $producto['ML_item_id2'])) {
            mensajes_alta($i18n[113] . '<a href="productos.php?a=ver&id=' . $producto['idproducto'] . '" target="_blank">' . $producto['nombre'] . '</a>');
            $boton = '';
        }
        if ($datos['ML_item_id2'] && ($datos['ML_item_id2'] == $producto['ML_item_id'] || $datos['ML_item_id2'] == $producto['ML_item_id2'])) {
            mensajes_alta($i18n[114] . '<a href="productos.php?a=ver&id=' . $producto['idproducto'] . '" target="_blank">' . $producto['nombre'] . '</a>');
            $boton = '';
        }
    }
}

switch ($boton) {
    case $i18n[9]:    // Agregar
    case $i18n[162]:
        if (!$datos['codigo']) {
            $proximoid = campo_sql(consulta_sql("SELECT idproducto FROM productos ORDER BY idproducto DESC LIMIT 1"), 0) + 1;
            // $proximoid = campo_sql(consulta_sql("SELECT AUTO_INCREMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'productos'"), 0);
            $datos['codigo'] = $proximoid < 10000
                ? completar_numero($proximoid, 5)
                : $proximoid;
        }

        consulta_sql("INSERT INTO productos SET
            estado = '" . $datos['estado'] . "',
            estadoventa = '" . $datos['estadoventa'] . "',
            estadocompra = '" . $datos['estadocompra'] . "',
            estadocombo = '" . $datos['estadocombo'] . "',
            combo = '" . $datos['combo'] . "',
            idrubro = '" . $datos['idrubro'] . "',
            idmoneda = '" . $datos['idmoneda'] . "',
            sku = '" . $datos['sku'] . "',
            codigo = '" . validar_codigo_producto($datos['codigo']) . "',
            codigoproveedor = '" . $datos['codigoproveedor'] . "',
            nombre = '" . $datos['nombre'] . "',
            url_amigable = '"     . url_amigable($datos['nombre']) . "',
            controlarstock = '" . $datos['controlarstock'] . "',
            idunidad = '" . $datos['idunidad'] . "',
            idiva = '" . $datos['idiva'] . "',
            idproveedor = '" . $datos['idproveedor'] . "',
            costo = '" . $datos['prod_costo'] . "',
            stocknegativo = '" . $datos['stocknegativo'] . "',
            mostrartienda = '" . $datos['mostrartienda'] . "',
            trazabilidad = '" . $datos['trazabilidad'] . "',
            tributo_5329   = '".$datos['tributo_5329']."',
            observacion = '" . escape_sql($datos['observacion']) . "',
            obstienda = '" . escape_sql($datos['obstienda']) . "',
            obsinterna = '" . escape_sql($datos['obsinterna']) . "',
            ML_item_id = '" . $datos['ML_item_id'] . "',
            ML_item_id2 = '" . $datos['ML_item_id2'] . "'
            ");
        $id = id_sql();

        $sql_utilidad = "";
        $sql_precio = "";
        foreach ($datos_listas as $key => $value) {
            switch ($key) {
                case strpos($key, 'idlista_') !== false:
                    $sql = "INSERT INTO precios SET
                    idproducto = '".$id."',
                    idlista = '".$value."', ";
                    break;
                case strpos($key, 'utilidad_') !== false:
                    $sql .= "utilidad = '".$value."', ";
                    $sql_utilidad = ", utilidad = '".$value."'";
                    break;
                case strpos($key, 'prod_precio_') !== false:
                    if ($_SESSION['configuracion_discrimina']) {
                        $sql .= "precio = '".$value."', ";
                        $sql_precio = ", precio = '".$value."'";
                    } else {
                        $sql .= "precio = '".$value."'";
                        $sql .= " ON DUPLICATE KEY UPDATE precio = '".$value."' " .$sql_utilidad.$sql_precio;
                        consulta_sql($sql);
                    }
                    break;
                case strpos($key, 'prod_preciofinal_') !== false:
                    $sql .= "preciofinal = '".$value."'";
                    $sql .= " ON DUPLICATE KEY UPDATE preciofinal = '".$value."' " .$sql_utilidad.$sql_precio;
                    consulta_sql($sql);
                    break;
                default:
                    break;
            }

        }

        $sql_stockactual = "";
        $sql_stockideal = "";
        foreach ($datos_depositos as $key => $value) {
            switch ($key) {
                case strpos($key, 'iddeposito_') !== false:
                    $sql = "INSERT INTO stock SET
                    idproducto = '".$id."',
                    iddeposito = '".$value."', ";
                    break;
                case strpos($key, 'stockactual_') !== false:
                    $sql .= "stockactual = '".$value."', ";
                    $sql_stockactual = ", stockactual = '".$value."'";
                    break;
                case strpos($key, 'stockideal_') !== false:
                    $sql .= "stockideal = '".$value."', ";
                    $sql_stockideal = ", stockideal = '".$value."'";
                    break;
                case strpos($key, 'stockminimo_') !== false:
                    $sql .= "stockminimo = '".$value."'";
                    $sql .= " ON DUPLICATE KEY UPDATE stockminimo = '".$value."' " .$sql_stockactual.$sql_stockideal;
                    consulta_sql($sql);
                    break;
                default:
                    break;
            }
        }

        $motivo = 'alta';
        $idrelacion = '0';
        log_productos($id, $motivo, $idrelacion);
        fullsearch_insert('productos', $id);
        extras_alta();
        if($boton == $i18n[162]){
            ir_ahora('productos.php?a=mod&id='.$id);
        }
        if ($temp_modulo == 'productos') {
            ir_ahora('productos.php?a=ver&id='.$id);
        } else {
            $script = '
            $("#' . $temp_modulo . '_seleccionador").hide();
            $("#' . $temp_modulo . '_resultados").html("");
            $("#' . $temp_modulo . '_resultados").hide();
            $("#' . $temp_modulo . '_seleccionado").show();
            $("#' . $temp_modulo . '_seleccionado").html(' . "'" . '<div class="buscar_botones"><a href="#" id="' . $temp_modulo . '_cambiar"><img style="margin-top: -5px;" src="' . $_SESSION['servidor_url'] . 'estilos/estilo_' . $_SESSION['usuario_idestilo'] . '/images/cambiar.png" title="' . $i18n_funciones[19] . '"></a></div>' . $datos['nombre'] . "'" . ');
            $("#linea_agregar input[name=' . "'" . 'idproducto' . "'" . ']").val("' . $id . '");
            $("#linea_agregar #codigo_nuevo").html("' . $datos['codigo'] . '");
            $("#linea_agregar input[name=' . "'" . 'precio' . "'" . ']").val("' . ($temp_modulo == 'compras' ? $datos['prod_costo'] : $datos['prod_precio']) . '");
            $("#linea_agregar input[name=' . "'" . 'preciofinal' . "'" . ']").val("' . $datos['prod_preciofinal'] . '");
            $("#linea_agregar option[value=' . "'" . $datos['idiva'] . "'" . ']").attr("selected", "selected");
            $("#linea_agregar select[name=' . "'" . 'idiva' . "'" . ']").attr("disabled", "disabled");';
            modal_cerrar($script);
        }
        break;

    case $i18n[13]:    // Cancelar
        if ($temp_modulo == 'productos') {
            ir_atras();

        } else {
            modal_cerrar();
        }
        break;
}

$tienda = array_sql(consulta_sql("SELECT tienda_estado, ML_estado, MP_estado, ML_access_token, ML_user_id FROM tienda LIMIT 1"));
$sucursales = armar_sqls_sucursales();
$cotizaciones = obtener_cotizaciones();

mensajes_efimeros();

ventana_inicio($i18n[1]);
{
    entrada('hidden', 'temp_modulo', false, $temp_modulo);

    // Datos básicos
    contenido_inicio($i18n[18], '66');
    {
        entrada('texto', 'nombre', $i18n[3], $datos['nombre'], '50', '200');
        entrada('texto', 'codigo', $i18n[4], $datos['codigo'], '25', '60');
        entrada('texto', 'codigoproveedor', $i18n[16], $datos['codigoproveedor'], '25', '60');
        selector('idunidad', $i18n[5], $datos['idunidad'], '33', 'tablas_unidades');
        selector_familiar('idrubro', $i18n[8], $datos['idrubro'], '33', 'categorias_rubros', true, true, true);
        selector('idproveedor', $i18n[17], $datos['idproveedor'], '34', 'proveedores', 'nombre', false, true, true);
    }
    contenido_fin();

    // Datos de comportamiento
    contenido_inicio($i18n[20], '33');
    {
        marcas('', '100', array(
            array('nombre' => 'estado', 'titulo' => $i18n[26], 'valor' => $datos['estado'], 'opciones' => 'id="estado"'),
            array('nombre' => 'estadoventa', 'titulo' => $i18n[168], 'valor' => $datos['estadoventa'], 'opciones' => 'id="estadoventa"'),
            array('nombre' => 'estadocompra', 'titulo' => $i18n[145], 'valor' => $datos['estadocompra'], 'opciones' => 'id="estadocompra"'),
        ));
        marcas('', '100', array(
            array('nombre' => 'estadocombo', 'titulo' => $i18n[146], 'valor' => $datos['estadocombo'], 'opciones' => 'id="estadocombo"'),
            array('nombre' => 'combo', 'titulo' => $i18n[147], 'valor' => $datos['combo'], 'opciones' => 'id="combo"'),
            array('nombre' => 'controlarstock', 'titulo' => $i18n[28], 'valor' => $datos['controlarstock'], 'opciones' => 'id="controlarstock"')
        ));

       marcas('', 'auto', array(array('nombre' => 'stocknegativo', 'titulo' => $i18n[29], 'valor' => $datos['stocknegativo'], 'opciones' => 'id="stocknegativo"')));

        if ($_SESSION['modulo_trazabilidad'])
            marcas('', 'auto', array(array('nombre' => 'trazabilidad', 'titulo' => $i18n[45], 'valor' => $datos['trazabilidad'])));

        if ($_SESSION['modulo_tienda'] && $tienda['tienda_estado'])
            marcas('', 'auto', array(array('nombre' => 'mostrartienda', 'titulo' => $i18n[87], 'valor' => $datos['mostrartienda'])));

        if ($_SESSION['configuracion_emitir_impuestos'] && $_SESSION['configuracion_tributo_5329'])
            marcas('', 'auto', array(array('nombre' => 'tributo_5329', 'titulo' => $i18n[261], 'valor' => $datos['tributo_5329'], 'ayuda_puntual' => $i18n[262])));

    }
    contenido_fin();

    // Datos de costos y stock
    contenido_inicio($i18n[191], '100', false, false, false, false, 'mini-icono-listas-de-precios');
    {
        if ($_SESSION['modulo_multimoneda']) {
            selector_array('idmoneda', $i18n[265], 1, '30', $cotizaciones, false, 'id="idmoneda_costo"');
        } else {
            texto('titulo', $i18n[264], $i18n[266], '30');
            entrada('hidden', 'idmoneda', '', 1);
        }
        entrada('moneda', 'prod_costo', $i18n[6], $datos['prod_costo'], '15', '10');
        if ($_SESSION['configuracion_discrimina'])
            selector('idiva', $i18n[10] . ' (%)', $datos['idiva'], '15', 'tablas_ivas', false, false, false);
    }
    contenido_fin();

    //Listas de precio
    puntero_sql($sucursales['listas']);
    while ($temp_array = array_sql($sucursales['listas'])) {
        $solapas_listas[] = array('idsolapa' => $temp_array['idlista'], 'tipo' => 'idlista', 'nombre' => $temp_array['nombre']);
    }
    solapas_multiples_inicio($solapas_listas);
    {
        entrada('hidden', 'idlista', '', $idlista);
        puntero_sql($sucursales['listas']);
        while ($temp_array = array_sql($sucursales['listas'])) {
            solapa_inicio($temp_array['nombre']);
            {
                contenido_inicio('', '100', false, false, false, 'id="idlista" style="display: '.($temp_array[0] == 1 ? 'block':'none').'";');
                {
                    if ($_SESSION['modulo_multimoneda']) {
                        texto('titulo', $i18n[267], $temp_array['moneda'].' ('.$temp_array['simbolo'].')');
                        salto_linea();
                    }
                    entrada('hidden', 'idmoneda_precio_'.$temp_array['idlista'], '', $temp_array['idmoneda']);
                    entrada('hidden', 'idlista_'.$temp_array['idlista'], '', $temp_array['idlista']);
                    entrada('porcentaje', 'utilidad_'.$temp_array['idlista'], $i18n[25].' (%)', $datos['utilidad_'.$temp_array['idlista']], '15', '6', false, 'data="utilidad"');
                    entrada('moneda', 'prod_precio_'.$temp_array['idlista'], $i18n[7].' ('.$temp_array['simbolo'].')', $datos['precio_'.$temp_array['idlista']], '15', '10', false, 'data="prod_precio"');
                    if ($_SESSION['configuracion_discrimina'])
                        entrada('moneda', 'prod_preciofinal_'.$temp_array['idlista'], $i18n[81].' ('.$temp_array['simbolo'].')', $datos['preciofinal_'.$temp_array['idlista']], '15', '10', false, 'data="prod_preciofinal"');
                }
                contenido_fin();
            }
            solapa_fin();
        }
    }
    solapas_fin();
    salto_linea();

    contenido_inicio($i18n[192], '100', false, false, false, false, 'mini-icono-stock-depositos');
    {

    }
    contenido_fin();

    //Depósitos
    puntero_sql($sucursales['depositos']);
    while ($temp_array = array_sql($sucursales['depositos'])) {
        $solapas_depositos[] = array('idsolapa' => $temp_array['iddeposito'], 'tipo' => 'iddeposito', 'nombre' => $temp_array['nombre']);
    }
    solapas_multiples_inicio($solapas_depositos);
    {
        entrada('hidden', 'iddeposito', '', $iddeposito);
        puntero_sql($sucursales['depositos']);
        while ($temp_array = array_sql($sucursales['depositos'])) {
            solapa_inicio($temp_array['nombre']);
            {
                contenido_inicio('', '100', false, false, false, 'id="iddeposito" style="display: '.($temp_array[0] == 1 ? 'block':'none').'";');
                {
                    entrada('hidden', 'iddeposito_'.$temp_array['iddeposito'], '', $temp_array['iddeposito']);
                    entrada('cantidadnegativa', 'stockactual_'.$temp_array['iddeposito'], $i18n[21], $datos['stockactual_'.$temp_array['iddeposito']], '15', '13');
                    entrada('cantidadnegativa', 'stockideal_'.$temp_array['iddeposito'], $i18n[22], $datos['stockideal_'.$temp_array['iddeposito']], '15', '13');
                    entrada('cantidadnegativa', 'stockminimo_'.$temp_array['iddeposito'], $i18n[23], $datos['stockminimo_'.$temp_array['iddeposito']], '15', '13');
                    if ($datos['combo'] && $datos['stockdinamico'] || $datos['stockdinamico'] === 0)
                        texto('cantidad', $i18n[173], $datos['stockdinamico'], '15');
                    elseif ($datos['combo'])
                        texto('texto', $i18n[173], $i18n[183], '15');
                }
                contenido_fin();
            }
            solapa_fin();
        }
    }
    solapas_fin();

    if ($_SESSION['modulo_ML'] && ($tienda['ML_estado'] || $tienda['MP_estado'])) {
        // Integración con MercadoLibre
        contenido_inicio($i18n[41], '100', false, false, false, false, 'mini-icono-ml');
        {
            entrada('texto', 'sku', $i18n[204], $datos['sku'], '33', '255', $i18n[242]);
            entrada('numeros', 'ML_item_id', $i18n[42], $datos['ML_item_id'], '33', '10');
            entrada('numeros', 'ML_item_id2', $i18n[112], $datos['ML_item_id2'], '33', '10');
            texto('italica', false, $i18n[125]);
            if (!$tienda['ML_access_token'] || !$tienda['ML_user_id']) {
                texto('italica', '', $i18n[117]);
            }
        }
        contenido_fin();
    }

    // Combos
    contenido_inicio($i18n[148], '100', false, false, false, 'id="ventana_combo"', 'mini-icono-composicion-de-productos');
    {
        botones(array(array(
            'valor' => $i18n[162],
            'opciones' => ($temp_modulo != 'productos' ? 'disabled="disabled"' : '')
            )));
    }
    contenido_fin();

    extras();

    // Observaciones
    contenido_inicio($i18n[86], '100', true, false);
    {
        area('observacion', $i18n[84], $datos['observacion'], 'auto', $i18n[37]);
        if ($_SESSION['modulo_tienda'] && $tienda['tienda_estado']) {
            area('obstienda', $i18n[36], $datos['obstienda']);
        }

        area('obsinterna', $i18n[85], $datos['obsinterna']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[9]), array('valor' => $i18n[13])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    var stocknegativo = "<?php echo $i18n[29]; ?>";
    var stocknegativo_combo = "<?php echo $i18n[184]; ?>";
    var idcombo = '<?php if($datos["combo"]) echo $id; ?>';
    var cotizaciones = JSON.parse('<?php echo json_encode($cotizaciones); ?>');

    function validacion_productos_alta(boton)
    {
        if ((boton == '<?=$i18n[9]?>' || boton == '<?=$i18n[162]?>')
            && $("#marco_productos_alta input[name='nombre']").val() == '') {
<?php script_validacion_flotante("alerta", $i18n[14], "input[name='nombre']");?>

            return false;
        } else
            return true;
    };

    $(function() {

        $('#estado, #estadoventa, #estadocompra, #estadocombo, #combo, #controlarstock, #stocknegativo').change(estados_producto);
        estados_producto();

    });

</script>
