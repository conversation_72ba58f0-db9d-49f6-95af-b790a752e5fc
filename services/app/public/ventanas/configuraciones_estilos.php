<?php
ventana_inicio($i18n[207]);
{
    //Logo
    contenido_inicio($i18n[208], 50);
    {
        if ($_SESSION['configuracion_estilo_logo']) {
            imagen(URL_S3.$_SESSION['configuracion_estilo_logo'].'/estilo_logo.'.$_SESSION['configuracion_ext_logo']);
            enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_logo', 'valor' => $i18n[111]), array('tipo' => 'ajax', 'url' => 'configuraciones_estilos_baja', 'id' => 'estilo_logo', 'valor' => $i18n[112])));
        } else {
            texto('texto', '', $i18n[282]);
            enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_logo', 'valor' => $i18n[110])));
        }
    }
    contenido_fin();

    //Configuración regional
    //Habilitado para retomar desarrollo
    //$_SESSION['control_formato_separador_miles'] = false;
    if ($_SESSION['usuario_idestilo'] > 1) {
        contenido_inicio($i18n[632], 50, false, false, $i18n[633]);
        {
            marcas($i18n[634], 'auto', array(array('nombre' => 'separador_miles', 'titulo' => $i18n[635], 'valor' => $_SESSION['control_formato_separador_miles'], 'opciones' => 'onchange="cambiar_separador_miles(this)"')));
        }
        contenido_fin();
    }

    salto_linea();

    //Estilos avanzados
    contenido_inicio($i18n[209], '50', false, false, $i18n[249]);
    {
        if ($_SESSION['sistema_gratis']) {
            texto('texto', '', $i18n[436]);

        } elseif ($_SESSION['configuracion_estilo_empresa']) {
            texto('texto', '', $i18n[284]);
            enlaces('', array(array('url' => URL_S3.$_SESSION['configuracion_estilo_empresa'].'/estilo_empresa.css', 'valor' => $i18n[121], 'opciones' => 'target="_blank"'), array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_empresa', 'valor' => $i18n[111]), array('tipo' => 'ajax', 'url' => 'configuraciones_estilos_baja', 'id' => 'estilo_empresa', 'valor' => $i18n[112])));

        } else {
            texto('texto', '', $i18n[283]);
            enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_empresa', 'valor' => $i18n[110])));
        }
    }
    contenido_fin();

    contenido_inicio($i18n[211], '50', false, false, $i18n[251]);
    {
        if ($_SESSION['sistema_gratis']) {
            texto('texto', '', $i18n[436]);

        } elseif ($_SESSION['configuracion_estilo_ventas']) {
            texto('texto', '', $i18n[284]);
            enlaces('', array(array('url' => URL_S3.$_SESSION['configuracion_estilo_ventas'].'/estilo_ventas.css', 'valor' => $i18n[121], 'opciones' => 'target="_blank"'), array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_ventas', 'valor' => $i18n[111]), array('tipo' => 'ajax', 'url' => 'configuraciones_estilos_baja', 'id' => 'estilo_ventas', 'valor' => $i18n[112])));
        } else {
            texto('texto', '', $i18n[283]);
            enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_ventas', 'valor' => $i18n[110])));
        }
    }
    contenido_fin();

    contenido_inicio($i18n[210], '50', false, false, $i18n[250]);
    {
        if ($_SESSION['sistema_gratis']) {
            texto('texto', '', $i18n[436]);

        } elseif ($_SESSION['configuracion_estilo_informes']) {
            texto('texto', '', $i18n[284]);
            enlaces('', array(array('url' => URL_S3.$_SESSION['configuracion_estilo_informes'].'/estilo_informes.css', 'valor' => $i18n[121], 'opciones' => 'target="_blank"'), array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_informes', 'valor' => $i18n[111]), array('tipo' => 'ajax', 'url' => 'configuraciones_estilos_baja', 'id' => 'estilo_informes', 'valor' => $i18n[112])));
        } else {
            texto('texto', '', $i18n[283]);
            enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_informes', 'valor' => $i18n[110])));
        }
    }
    contenido_fin();

    contenido_inicio($i18n[345], '50', false, false, $i18n[346]);
    {
        if ($_SESSION['sistema_gratis']) {
            texto('texto', '', $i18n[436]);

        } elseif ($_SESSION['configuracion_estilo_ventas_pdf']) {
            texto('texto', '', $i18n[284]);
            enlaces('', array(array('url' => URL_S3.$_SESSION['configuracion_estilo_ventas_pdf'].'/estilo_ventas_pdf.css', 'valor' => $i18n[121], 'opciones' => 'target="_blank"'), array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_ventas_pdf', 'valor' => $i18n[111]), array('tipo' => 'ajax', 'url' => 'configuraciones_estilos_baja', 'id' => 'estilo_ventas_pdf', 'valor' => $i18n[112])));
        } else {
            texto('texto', '', $i18n[283]);
            enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_ventas_pdf', 'valor' => $i18n[110])));
        }
    }
    contenido_fin();
}
ventana_fin();

?>
<script>
    function cambiar_separador_miles(e) {
        $.ajax(
        {
            url: "cargadores/ajax.php",
            type: "post",
            data: ({
                t: window.t,
                modulo: "configuraciones",
                ventana: "configuraciones_formato_regional",
                parametro: "formato_separador_miles",
                valor: (e.checked ? 1 : 0)
            }),
            beforeSend: function() {
                bloquear();
            },
            async: false,
            success: function(data) {
                desbloquear();
            }
        });
    }
</script>