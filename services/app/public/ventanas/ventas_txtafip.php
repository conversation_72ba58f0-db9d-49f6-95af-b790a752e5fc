<?php
require_once PATH_SCRIPTS."manual/txtafip/WrapperBD.php";
require_once PATH_SCRIPTS."manual/txtafip/generadores/GeneradorArchivoVentas.php";
require_once PATH_SCRIPTS."manual/txtafip/generadores/GeneradorArchivoAlicuotasVentas.php";
require_once PATH_SCRIPTS."manual/txtafip/generadores/GeneradorArchivoArba7.php";

$txt = recibir_variable('texto', 'txt', true);
if (!in_array($txt, ['rg3685', 'libroiva', 'arba-percepcion']))
    ir_inicio();

$enlace = recibir_variable('texto', 'tipo', true);
if (in_array($enlace, array('ventas', 'alicuotas_ventas', 'arba-percepcion'))) {
    $datos['periodo'] = recibir_variable('texto', 'periodo', true);
    $temp_array = explode('-', $datos['periodo']);
    $datos['desde'] = date("Y-m-d", strtotime('first day of '.$temp_array[1].'-'.$temp_array[0]));
    $datos['hasta'] = date("Y-m-d", strtotime('last day of '.$temp_array[1].'-'.$temp_array[0]));
}

$enlace_descarga = "ventas.php?a=$a&txt=$txt";

switch ($enlace) {
    case 'ventas':
        $nombre_archivo = $txt == 'rg3685'
            ? "Importación de Comprobantes de Ventas del período " . $datos['periodo'] . " (" . $_SESSION['configuracion_cuit'] . ").txt"
            : 'LIBRO_IVA_DIGITAL_VENTAS_CBTE.txt';
        $bd = new WrapperBD();
        $generador = new GeneradorArchivoVentas($bd);
        $generador->generar($datos['desde'], $datos['hasta']);
        $generador->descargarTxt($nombre_archivo);
        exit();
        break;

    case 'alicuotas_ventas':
        $nombre_archivo = $txt == 'rg3685'
            ? "Importación de Alicuotas de Ventas del período " . $datos['periodo'] . " (" . $_SESSION['configuracion_cuit'] . ").txt"
            : 'LIBRO_IVA_DIGITAL_VENTAS_ALICUOTAS.txt';
        $bd = new WrapperBD();
        $generador = new GeneradorArchivoAlicuotasVentas($bd);
        $generador->generarAlicuota($datos['desde'], $datos['hasta']);
        $generador->descargarTxt($nombre_archivo);
        exit();
        break;

    case 'arba-percepcion':
        $temp_array = explode('-', $datos['periodo']);
        // AR-CUIT-PERIODO-ACTIVIDAD-LOTE_MD5
        $nombre_archivo = 'AR-'.$_SESSION['configuracion_cuit'].'-'.$temp_array[1].$temp_array[0].'0-D7-LOTE1';
        $bd = new WrapperBD();
        $generador = new GeneradorArchivoArba7($bd); // Mensuales Método Devengado (Actividad 7)
        $generador->generar($datos['desde'], $datos['hasta']);
        $generador->descargarZipMd5($nombre_archivo);
        exit();
        break;

    default:
        $datos = array('periodo' => '0');
        break;
}


switch ($txt) {
    case 'rg3685':
        $i18n_titulo_ventana = $i18n[201];
        $i18n_italica = $i18n[203].' '.$i18n_funciones[99];
        $enlaces = [
            ['url' => 'https://www.afip.gob.ar/comprasyventas',
                'valor' => $i18n[208], 'opciones' => 'target="_blank"'],
            ['url' => 'https://www.afip.gob.ar/Aplicativos/oProgramasImpositivos/RegimenInfComprasVentas.asp',
                'valor' => $i18n[285], 'opciones' => 'target="_blank"'],
        ];
        $i18n_italica_descarga = $i18n[284];
        $i18n_descargas = $i18n[207];

        $descargas = array(
            array('valor' => $i18n[205] . ' de Ventas', 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=ventas"),
            array('valor' => $i18n[206] . ' de Ventas', 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=alicuotas_ventas"),
        );
        break;

    case 'libroiva':
        $i18n_titulo_ventana = $i18n_funciones[261];
        $i18n_italica = $i18n[339].' '.$i18n_funciones[99];
        $enlaces = [
            ['url' => 'https://www.afip.gob.ar/libro-iva-digital', 'valor' => $i18n[340], 'opciones' => 'target="_blank"'],
        ];
        $i18n_italica_descarga = $i18n[344];
        $i18n_descargas = $i18n[341];

        $descargas = array(
            array('valor' => $i18n[342], 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=ventas"),
            array('valor' => $i18n[343], 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=alicuotas_ventas"),
        );
        break;

    case 'arba-percepcion':
        $i18n_titulo_ventana = $i18n_funciones[300];
        $i18n_italica = $i18n[375].$i18n[376].'. '.$i18n_funciones[99];
        $enlaces = [
            ['url' => 'https://web.arba.gov.ar/agentes#presentacion-de-ddjj', 'valor' => $i18n[377], 'opciones' => 'target="_blank"'],
        ];
        $i18n_italica_descarga = $i18n[378];
        $i18n_descargas = $i18n[379];

        $descargas = array(
            array('valor' => $i18n[376], 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=arba-percepcion"),
        );
        break;
}

ventana_inicio($i18n_titulo_ventana);
{
    contenido_inicio($i18n[202]);
    {
        texto('italica', false, $i18n_italica);
        enlaces(false, $enlaces);

        $fechas = array_sql(consulta_sql("SELECT
            (SELECT fecha FROM ventas WHERE fecha != 0 AND estado = 'cerrado' ORDER BY fecha ASC LIMIT 1) AS desde,
            (SELECT fecha FROM ventas WHERE fecha != 0 AND estado = 'cerrado' ORDER BY fecha DESC LIMIT 1) AS hasta
            "));
        $meses = listar_meses($fechas['desde'], $fechas['hasta']);
        $array_selector_opciones = array(
            array('valor' => 'Seleccionar período', 'id' => '0')
            );
        for ($i = count($meses); $i; $i--) {
            $mes = $meses[$i-1];
            $array_selector_opciones[] = array('id' => $mes, 'valor' => $mes . ' ('.$i18n_funciones['mes_'.$mes[0].$mes[1]].')');
        }
        selector_array('periodo', $i18n[204], $datos['periodo'], '25', $array_selector_opciones);
    }
    contenido_fin();

    contenido_inicio(false, '100', false, false, false, 'id="enlaces" style="display: none;"');
    {
        texto('italica', false, $i18n_italica_descarga, 'auto', false, 'info');
        enlaces($i18n_descargas, $descargas);
    }
    contenido_fin();

}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    $("select[name='periodo']").change(function() {
        if ($("select[name='periodo'] option:selected").val() != 0) {
            var periodo = $("select[name='periodo'] option:selected").val();
            $("#enlaces a").each(function (a) {
                $(this).attr('href', $(this).attr('href') + '&periodo=' + periodo);
            })
            $("#enlaces").fadeOut(function() {
                $("#enlaces").fadeIn();
            });

        } else {
            $("#enlaces").fadeOut();
        }
    });
</script>
