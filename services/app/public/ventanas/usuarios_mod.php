<?php

if ($_SESSION['renovar']) {
    unset($_SESSION['renovar']);
    unset($_SESSION['history_0']);
    script_flotante('informacion', $i18n_funciones[92]);
}

switch ($boton) {
    case $i18n[1]: // Aceptar
        $datos = recibir_matriz(array(
        array('texto', 'nombrereal'),
        array('texto', 'nombre'),
        array('mail', 'mail'),
        array('texto', 'passi'),
        array('texto', 'passi2'),
        array('mail', 'mail_original'),
        array('texto', 'telefonos'),
        array('entero', 'idperfil'),
        array('entero', 'idestilo'),
        array('largo', 'obsinterna'),
        array('largo', 'texto_rapido_1'),
        array('largo', 'texto_rapido_2'),
        array('largo', 'texto_rapido_3'),
        array('largo', 'texto_rapido_4'),
        array('largo', 'texto_rapido_5'),
        array('texto', 'estilo_oscuro'),
        array('moneda', 'total_administradores')
    ));

        if ($_SESSION['perfil_idperfil'] == 1 && $datos['nombrereal'] == '')
            mensajes_alta($i18n[16], 'Alerta');
        elseif ($datos['nombre'] == '')
            mensajes_alta($i18n[17], 'Alerta');
        elseif ($datos['mail'] == '')
            mensajes_alta($i18n[18], 'Alerta');
        elseif ($datos['passi'] != '' && $datos['passi'] != $datos['passi2'])
            mensajes_alta($i18n[39], 'Alerta');
        elseif ($_SESSION['usuario_idusuario'] == $id
            && $_SESSION['perfil_idperfil'] == 1
            && $datos['idperfil'] != 1
            && !contar_sql(consulta_sql(
                "SELECT idusuario FROM usuarios
                WHERE idusuario != '$id' AND idusuario != 0 AND idperfil = 1 AND estado = 1
                LIMIT 1"))) {
            // El usuario es el último administrador y quiere sacarse ese perfil
            $datos['idperfil'] = 1;
            mensajes_alta($i18n[48]);
        }
        elseif ($datos['mail'] != $datos['mail_original'] && contar_sql(consulta_sql("SELECT idusuario FROM usuarios WHERE mail='".$datos['mail']."' LIMIT 1", 'saasargentina')))
            // No utilizo mensajes_alta porque da error porque se pisan las conexiones
            // mensajes_alta($i18n[32], 'Alerta');
            consulta_sql("INSERT INTO mensajes (idusuario, tipo, texto) VALUES (".$_SESSION['usuario_idusuario'].",'Alerta','".$i18n[32]."')");

        else {
            $datos['pass'] = $datos['passi'];

            $idperfil_anterior = campo_sql(consulta_sql(
                "SELECT idperfil FROM usuarios WHERE idusuario = '$id'"));

            // Modo oscuro sólo habilitado para el estilo 2 temporalmente
            if ($datos['idestilo'] != 2) {
                $datos['estilo_oscuro'] = 0;
                mensajes_alta('El modo oscuro sólo está habilitado para el estilo Adaptativo Responsive por el momento');
            }

            consulta_sql("UPDATE usuarios SET
                    nombre = '".$datos['nombre']."',
                    idestilo = '".$datos['idestilo']."',
                    estilo_oscuro = '".$datos['estilo_oscuro']."',
                    telefonos = '".$datos['telefonos']."',
                    mail = '".$datos['mail']."',
                    obsinterna = '".$datos['obsinterna']."'".
                ($_SESSION['perfil_idperfil'] == 1
                    ? ", nombrereal = '".$datos['nombrereal']."', idperfil = '".($datos['idperfil'] > 0 ? $datos['idperfil'] : $_SESSION['perfil_idperfil'])."'"
                    : "")."
                WHERE idusuario = '".$id."'
                LIMIT 1");

            if ($id == $_SESSION['usuario_idusuario']) {
                $_SESSION['usuario_nombre'] = $datos['nombre'];
                $_SESSION['usuario_idestilo'] = $datos['idestilo'];
                $_SESSION['usuario_estilo_oscuro'] = $datos['estilo_oscuro'];

                if ($datos['idperfil'] != $_SESSION['perfil_idperfil']) {
                    // El mismo usuario cambió su perfil
                    $_SESSION['usuario_idperfil'] = $datos['idperfil'];
                    $datos_perfiles = array_sql(consulta_sql(
                        "SELECT * FROM perfiles WHERE idperfil = '" . $datos['idperfil'] . "'"));
                    foreach ($datos_perfiles as $key => $value) {
                        $_SESSION['perfil_' . $key] = $value;
                    }
                }

            } else if ($datos['idperfil'] != $idperfil_anterior) {
                // Cambió el perfil a otro usuario, hay que agregarle un mensaje y patearlo
                guardar_sql('mensajes', array(
                    'idusuario' => $id,
                    'tipo' => 'Confirmacion',
                    'texto' => $i18n[49].$_SESSION['usuario_nombre']
                ));
                consulta_sql("UPDATE controles SET ultimosid = '' WHERE idusuario = '$id' LIMIT 1");

            }

            if ($datos['pass'])
                consulta_sql("UPDATE usuarios SET mail = '".$datos['mail']."', pass = '".md5($datos['pass'])."' WHERE idusuario = '".$id."' LIMIT 1", 'saasargentina');
            else
                consulta_sql("UPDATE usuarios SET mail = '".$datos['mail']."' WHERE idusuario = '".$id."' LIMIT 1", 'saasargentina');
            ir_inicio();
        }
        break;

    case $i18n[31]: // Cancelar
        ir_atras();
        break;

    default:
        $datos = array_sql(consulta_sql("SELECT nombrereal, nombre, mail, idperfil, telefonos, idestilo, estilo_oscuro, obsinterna, (SELECT COUNT(*) FROM usuarios WHERE estado = 1 AND idperfil = 1) AS total_administradores FROM usuarios WHERE idusuario='".$id."' LIMIT 1"));
        $datos['mail_original'] = $datos['mail'];
        break;
}

mensajes_efimeros();

$datos['obsinterna'] = campo_sql(consulta_sql("SELECT obsinterna FROM usuarios WHERE idusuario='".$id."' LIMIT 1"), 0);

ventana_inicio($i18n[2].$id);
{
    //Datos de acceso
    contenido_inicio($i18n[3]);
    {
        entrada('email', 'mail', $i18n[12], $datos['mail'], '33', '320', $i18n[37]);
        entrada('hidden', 'mail_original', '', $datos['mail_original']);
        entrada('password', 'passi', $i18n[10], '', '33', false, $i18n[38], 'autocomplete="off"');
        entrada('password', 'passi2', $i18n[9], '', '34', false, false, 'autocomplete="off"');
    }
    contenido_fin();

    // Datos básicos
    contenido_inicio($i18n[4], '66');
    {
        entrada('texto', 'nombrereal', $i18n[5], $datos['nombrereal'], '50', '60', $i18n[35]);
        entrada('texto', 'nombre', $i18n[8], $datos['nombre'], '50', '60', $i18n[36]);
        entrada('texto', 'telefonos', $i18n[11], $datos['telefonos'], '50', '50');
        entrada('hidden', 'total_administradores', '', $datos['total_administradores']);
        entrada('hidden', 'idperfil', '', $datos['idperfil']);
        selector('idperfil', $i18n[6], $datos['idperfil'], '50', 'perfiles', 'nombre', false, false, false, false,
            ($_SESSION['perfil_idperfil'] == 1 && $datos['total_administradores'] == 1 //Si soy el último/único admin
            && $datos['idperfil'] == 1)// Pero tengo que poder modificar otro(s)
            || ($_SESSION['perfil_idperfil'] != 1) // Si no soy admin
            ? 'disabled="disabled"' : '');
    }
    contenido_fin();

    // Estilo
    contenido_inicio($i18n[13], '33');
    {
        selector('idestilo', $i18n[13], $datos['idestilo'], 'auto', 'tablas_estilos', 'idestilo', false, false);
        marcas(false, 'auto', array(array('nombre' => 'estilo_oscuro', 'titulo' => $i18n['50'], 'valor' => $datos['estilo_oscuro'])));
        if ($_SESSION['usuario_fondo']) {
            texto('texto', $i18n[44], $i18n[47], 'auto', false, false, $i18n[45]);
            enlaces('', array(
                array('url' => URL_S3.$_SESSION['usuario_fondo'].'/usuario_fondo.jpg', 'valor' => $i18n_funciones[34], 'opciones' => 'target="_blank"'),
                array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'usuario_fondo', 'valor' => $i18n_funciones[28]),
                array('tipo' => 'ajax', 'url' => 'configuraciones_estilos_baja', 'id' => 'usuario_fondo', 'valor' => $i18n_funciones[29])
            ));

        } else {
            texto('texto', $i18n[44], $i18n[46], 'auto', false, false, $i18n[45]);
            enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'usuario_fondo', 'valor' => $i18n_funciones[27])));
        }
    }
    contenido_fin();

    // Observación interna
    contenido_inicio($i18n[15]);
    {
        area('obsinterna', $i18n[15], $datos['obsinterna']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[1]), array('valor' => $i18n[31])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">

    let modo_oscuro = document.createElement('link');
    modo_oscuro.rel = 'stylesheet';
    modo_oscuro.type = 'text/css';
    modo_oscuro.href = "<?php echo $_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/css/modo-oscuro-estilo_2.css'; ?>";

    let idestilo = "<?php echo $_SESSION['usuario_idestilo']; ?>";
    habilitar_modo_oscuro(idestilo);

    function validacion_usuarios_mod(boton)
    {
        var mail = $("#marco_usuarios_mod input[name='mail']").val();
        if (boton == '<?php echo $i18n[1]; ?>' && (mail == '' || !$("input[name='mail']").valid())) {
            <?php script_validacion_flotante("alerta", $i18n[18], "input[name='mail']"); ?>
            return false;

        } else
            return true;
    };

    function habilitar_modo_oscuro(idestilo)
    {
        if (idestilo == 2) {
            $("input[name='estilo_oscuro']").parent().show();
        } else {
            $("input[name='estilo_oscuro']").parent().hide();
        }
    }

    $("#select_idestilo").change(function(e){
        e.preventDefault();
        habilitar_modo_oscuro($(this).val());
    });

    $("input[name='estilo_oscuro']").change(function(e){
        e.preventDefault();
        if (idestilo != 1) {
            let activar_modo_oscuro = $(this).is(":checked");

            if (activar_modo_oscuro) {
                bloquear();
                $("html").fadeOut(500, function(){
                    document.getElementsByTagName('HEAD')[0].appendChild(modo_oscuro);
                    $("html").fadeIn(500);
                    desbloquear();
                });
            } else {
                bloquear();
                $("html").fadeOut(500, function(){
                    let modo_oscuro_actual = document.getElementsByTagName('link')[5];
                    if (modo_oscuro_actual.href.toString().includes("modo-oscuro-estilo_2")) {
                        modo_oscuro_actual.parentNode.removeChild(modo_oscuro_actual);
                    } else {
                        let modo_oscuro = document.querySelector('link[href="<?php echo $_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/css/modo-oscuro-estilo_2.css'; ?>"]');
                        if (modo_oscuro) {
                            document.getElementsByTagName('HEAD')[0].removeChild(modo_oscuro);
                        }
                    }
                    desbloquear();
                    $("html").fadeIn(500);
                });
            }
        }
    });

</script>
