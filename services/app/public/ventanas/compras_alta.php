<?php

switch ($boton) {
    case $i18n[10]:
         $datos = recibir_matriz(array(
        array('entero', 'idtipocompra'),
        array('texto', 'proveedores_id'),
        array('entero', 'numerocompleto'),
        array('fecha', 'fecha'),
        array('entero', 'idrelacion'),
        array('entero', 'puntodeventa'),
        array('texto', 'discrimina'),
        array('entero', 'numero'),
        array('entero', 'idrelacion')
    ));

        consulta_sql("UPDATE controles SET ultimotipocompra = '".$datos['idtipocompra']."'
            WHERE idusuario = '".$_SESSION['usuario_idusuario']."' LIMIT 1");
        $_SESSION['control_ultimotipocompra'] = $datos['idtipocompra'];

        // Si no tengo proveedor entonces es el sin registrar
        $idproveedor = $datos['proveedores_id'] ? $datos['proveedores_id'] : 1;

        // Es una devolución de pago
        if ($datos['idtipocompra'] == -1)
            ir_ahora('compras.php?a=altapago&devolucion=1&idproveedor='.$idproveedor);

        // Es una órden de pago
        if ($datos['idtipocompra'] == 0)
            ir_ahora('compras.php?a=altapago&idproveedor='.$idproveedor);

        $discrimina = (($_SESSION['configuracion_discrimina'] && campo_sql(consulta_sql(
            "SELECT discrimina
            FROM tablas_condiciones
            INNER JOIN proveedores ON tablas_condiciones.idtipoiva = proveedores.idtipoiva
            WHERE idproveedor = '".$idproveedor."'
            LIMIT 1"), 0))
            ? 'A' : 'C');

        // Vamos a compras_alta_auto con parámetros x GET
        ir_ahora('compras.php?a=alta_auto&idproveedor='.$idproveedor.'&idtipocompra='.$datos['idtipocompra'].'&discrimina='.$discrimina.'&numero='.$datos['numero'].'&fecha='.$datos['fecha'].'&puntodeventa='.$datos['puntodeventa'].'&idrelacion='. $datos['idrelacion']);
        break;

    case $i18n[15]: // Cancelar
        ir_atras();
        break;

    default:
        $idservicio = recibir_variable('entero', 'idservicio', true);
        $datos = array('fecha' => 'ahora', 'numerocompleto' => 'CO'.completar_numero(campo_sql(consulta_sql("SELECT idcompra FROM compras ORDER BY idcompra DESC LIMIT 1"), 0) + 1, 8), 'idtipocompra' => $_SESSION['control_ultimotipocompra'], 'idrelacion' => $idservicio);
        mensajes_efimeros();
        break;
}

// Armo opciones del selector de compras, incluyendo orden de pago y devolu
$selector_compras = array();
$selector_compras[0] = array('id' => 0, 'valor' => $i18n[117]);
$selector_compras[-1] = array('id' => -1, 'valor' => $i18n[239]);
$categorias_sql = consulta_sql("SELECT idtipocompra, nombre FROM categorias_compras");
while ($categoria = array_sql($categorias_sql)){
    $selector_compras[$categoria['idtipocompra']] = array('id' => $categoria['idtipocompra'], 'valor' => $categoria['nombre']);
}

// VISTA
ventana_inicio($i18n[56]);
{
    // Seleccion de proveedores
    contenido_inicio($i18n[35], '100', false, false, $i18n[12]);
    {
        seleccionador('proveedores');
    }
    contenido_fin();

    // Datos basicos
    contenido_inicio($i18n[34]);
    {
        entrada('hidden', 'idrelacion', '',$datos['idrelacion']);
        selector_array('idtipocompra', $i18n[49], $datos['idtipocompra'], '100', $selector_compras);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[10]), array('valor' => $i18n[15])));
}
ventana_fin();
