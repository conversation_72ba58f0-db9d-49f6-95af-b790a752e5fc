<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('largo', 'texto'),
        array('texto', 'tipo'),
        array('entero', 'idusuario'),
        array('fecha', 'fecha'),
        array('texto', 'destacado')
    ));
} else {
    $datos = array('tipo' => 'Personal', 'idusuario' => $_SESSION['usuario_idusuario'], 'fecha' => 'ahora');
}

switch ($boton) {
    case $i18n[39]: //Agregar
        if ($datos['idusuario'] == 0) {
            $resultado_sql = consulta_sql("SELECT idusuario FROM usuarios WHERE estado = '1'");
            while ($temp_array = array_sql($resultado_sql)) {
                if ($sql)
                    $sql.= ", ";
                else
                    $sql = "INSERT INTO mensajes (idusuario, idremitente, tipo, fecha, texto, destacado) VALUES ";
                $sql.= "('".$temp_array['idusuario']."', '".$_SESSION['usuario_idusuario']."', '".$datos['tipo']."', '".fecha_sql($datos['fecha'])."', '".$datos['texto']."', '".$datos['destacado']."')";
            }
        } else {
            $sql.= "INSERT INTO mensajes (idusuario, idremitente, tipo, fecha, texto, destacado) VALUES ('".$datos['idusuario']."', '".$_SESSION['usuario_idusuario']."', '".$datos['tipo']."', '".fecha_sql($datos['fecha'])."', '".$datos['texto']."', '".$datos['destacado']."')";
        }
        consulta_sql($sql);
        ir_inicio();
        break;

    case $i18n[43]: //Cancelar
        ir_inicio();
        break;
}

ventana_inicio($i18n[45]);
{
    contenido_inicio();
    {
        selector_array('tipo', $i18n[49], $datos['tipo'], '33', array(array('id' => 'Personal', 'valor' => $i18n[51]), array('id' => 'Recordatorio', 'valor' => $i18n[50])));
        selector('idusuario', $i18n[53], $datos['idusuario'], '33', 'usuarios', 'nombre', false, $i18n[66], true);
        entrada('fechayhora', 'fecha', $i18n[55], $datos['fecha'], '20');
        marcas($i18n[76], '15', array(
            array('nombre' => 'destacado', 'valor' => $datos['destacado']),
        ));
        area('texto', $i18n[48], $datos['texto']);
    }
    contenido_fin();
    botones(array(array('valor' => $i18n[39]), array('valor' => $i18n[43])));
}
ventana_fin();
