<?php
$idventa = recibir_variable('entero', 'idventa', true);
$idservicio = recibir_variable('entero', 'idservicio', true);
$idcliente = recibir_variable('entero', 'idcliente', true);
$idtipoventa = recibir_variable('entero', 'idtipoventa', true);
$actualiza_precios = recibir_variable('moneda', 'act_precios', true);
$marcas_ventas = recibir_marcas('venta_');
$altamasiva = false;

if ($a == 'alta_relacionada' && empty($marcas_ventas)) {
    mensajes_alta($i18n[355]);
    ir_inicio();
} elseif ($a == 'alta_relacionada' && !empty($marcas_ventas)) {
    $altamasiva = true;
}

$i = 0;
do {
    $venta_relacionada = false;
    if ($altamasiva) {
        $idventa = $marcas_ventas[$i];
    }

    // Es un pago a cuenta ya que el idtipoventa no se encuentra
    if (!$idtipoventa === 0 || $boton == 'Recibo de pago a cuenta')
        ir_ahora('ventas.php?a=altapago&idcliente='.$idcliente);

    // Es una devolución de pago ya que el idtipoventa
    if ($idtipoventa === -1 || $boton == 'Devolución de pago')
        ir_ahora('ventas.php?a=altapago&devolucion=1&idcliente='.$idcliente);

    if ($idventa) {
        // Puede venir relacionado a una venta
        $venta_relacionada = true;
        $venta = venta($idventa);

        $idcliente = $venta["idcliente"];
        $idservicio = false; // Limpio el parámetro por si alguien manda ambos en la url a mano

    } elseif ($idservicio) {
        // Puede venir relacionado a un servicio
        $idcliente = campo_sql(consulta_sql("SELECT idcliente FROM servicios WHERE idservicio = '$idservicio'"), 0);
    }

    // Si no tengo idcliente, fuerzo 1 que es el Consumidor final
    if (!is_numeric($idcliente) || $idcliente < 1)
        $idcliente = 1;

    $cliente = cliente($idcliente);
    $tipoventa = tipoventa($idtipoventa, $boton);
    $proximonumero = $tipoventa['ultimonumero'] + 1;

    guardar_sql('categorias_ventas', ['ultimonumero' => $proximonumero], $tipoventa['idtipoventa']);

    // Si el tipo de venta no es compatible con el tipo de venta del cliente
    if (!comprobantes_habilitados_validar($idcliente, $tipoventa["idtipoventa"])) {
        // No lo pongo en $i18n porque se lo llama desde clientes y desde ventas
        mensajes_alta('El cliente tiene configurada como condición fiscal '
            .$cliente['tipoiva']
            .' y no se le puede emitir un comprobante '
            .$tipoventa['discrimina']);
        ir_ahora('clientes.php?a=ver&id='.$idcliente);
    }

    // Si es Nota de crédito/débito electrónica y no está relacionada ARCA no lo aprueba después
    if ($tipoventa['tipofacturacion'] == 'electronico' && in_array($tipoventa['idcomportamiento'], [2,3,7,8,12,13,52,53])
        && (!$idventa
            || $venta['tipofacturacion'] != 'electronico'
            || !in_array($venta['idcomportamiento'], posibles_idcomportamiento_asoc($tipoventa['idcomportamiento'])))) {
        mensajes_alta($i18n_funciones[271]);
        ir_ahora('clientes.php?a=ver&id='.$idcliente);
    }

    // PARCHE: Tenemos que evitar que 2 usuarios estén procesando una misma venta pendiente, por ahora evalúo si ya esté aprobada la primer venta. Después hay que buscar una mejor forma de resolver esto que por ahora no se me ocurre
    if ($idventa
        && $venta['auto_aprobar']
        && $venta['situacion'] == 'aprobado'
        && contar_sql(consulta_sql(
            "SELECT ventasxventas.idventa
            FROM ventasxventas
                LEFT JOIN ventas ON ventasxventas.idrelacion = ventas.idventa
            WHERE ventasxventas.idventa = '$idventa'
                AND ventas.idtipoventa = '".$tipoventa['idtipoventa']."' LIMIT 1")
        )) {
        mensajes_alta(($modulo == 'clientes' ? $i18n[327] : $i18n[248])); //Modulo clientes o ventas
        exit('<script>window.history.go(0);</script>');
    }

    // Si el cliente no tiene localidad, informo que le asigno la de la empresa
    if ($idcliente > 1 && !$cliente['idlocalidad'])
        mensajes_alta('El cliente seleccionado no tiene una localidad cargada. Se asignará a la venta la localidad de su configuración de empresa.', 'Alerta'); // No pongo idiomas porque viene desde 2 módulos diferentes

    $datos = array();
    $datos['idcliente'] = $idcliente;
    $datos['idventa'] = $idventa;
    $datos['idservicio'] = $idservicio;
    $datos['proximonumero'] = $proximonumero;
    $datos['venta'] = $venta;
    $datos['cliente'] = $cliente;
    $datos['tipoventa'] = $tipoventa;

    //Valido tipo venta contra cliente
    if (comprobantes_habilitados_validar($idcliente, $tipoventa["idtipoventa"])) {
        try {
            begin_transaction();
            $idventa_nuevo = alta_venta($datos, $venta_relacionada);
            if ($venta_relacionada) {
                validar_deposito_lista_precios($venta, $tipoventa);
            }
            if ($idventa) {
                actualizar_relaciones_venta($idventa, $idventa_nuevo, $venta, $tipoventa);
            }
            if ($altamasiva) {
                cerrar_venta_masiva($idventa_nuevo);
            }
            commit_transaction();
        } catch (Exception $e) {
            rollback_transaction();
            mostrar_error('Error en la transacción: ' . $e->getMessage());
        }
    }
    $i++;
}
while ($i < count($marcas_ventas));

if (!$altamasiva) {
    ir_ahora('ventas.php?a=mod&id='.$idventa_nuevo.($actualiza_precios ? '&act_precios=1' : ''));
} else {
    $ahora = fecha_sql('ahora');
    $desde = date("d-m-Y H:i", strtotime($ahora));
    mensajes_alta($i18n[353].'<a href="'.URL_INFORMES.'/informes/mod/14?id=14&a=mod&desde='.$desde.'" target=_blank>'.$i18n[354].'</a>', 'Confirmacion');
    ir_inicio();
}
