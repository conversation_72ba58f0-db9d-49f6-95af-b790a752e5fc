<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('entero', 'estado'),
        array('texto', 'nombre'),
        array('texto', 'contacto'),
        array('texto', 'telefonos'),
        array('texto', 'domicilio'),
        array('mail', 'mail'),
        array('entero', 'idtipocliente'),
        array('entero', 'idusuario'),
        array('entero', 'idlocalidad'),
        array('entero', 'idtipoiva'),
        array('texto', 'razonsocial'),
        array('largo', 'observacion'),
        array('largo', 'obsinterna'),
        array('largo', 'obsrecordatorio'),
        array('cuit', 'cuit'),
        array('texto', 'telefonos_verificado'),
        array('texto', 'tipodoc'),
        array('texto', 'dni'),
        array('texto', 'ML_user_id'),
        array('texto', 'ML_user_id2'),
        array('texto', 'ML_nickname')
    ));
    // TODO: Eliminar esta línea al arreglar el validador de cuit
    $datos['cuit'] = str_replace('-', '', $datos['cuit']);

} else {
    $datos = array_sql(consulta_sql(
        "SELECT clientes.*,
            categorias_localidades.nombre AS localidad,
            tablas_condiciones.nombre AS tipoiva,
            monedas.nombre AS moneda, monedas.simbolo
        FROM clientes
            LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
            LEFT JOIN tablas_condiciones ON clientes.idtipoiva = tablas_condiciones.idtipoiva
            LEFT JOIN monedas ON clientes.idmoneda = monedas.idmoneda
        WHERE idcliente = '$id'"));
}
/*
if ($datos['cuit'] =! '' && $datos['razonsocial'] == ''){
    $razonsocialok == true;
}*/

$tienda = array_sql(consulta_sql("SELECT tienda_estado, ML_estado, MP_estado, ML_access_token, ML_user_id FROM tienda LIMIT 1"));
$tipodocs = tipoDocs(false, true);

// Comento la validación porque precisa más revisión
// generar_validador_js($i18n[67].$datos['nombre'],$campos_reglas,$i18n[258]);

switch ($boton) {
    case $i18n[62]: // Aceptar
        if ($datos['mail'] && !filter_var($datos['mail'], FILTER_VALIDATE_EMAIL)) {
            mensajes_alta($i18n[65]);
        }
        // if (validar_reglas($campos_reglas)){
        if (true) { // Comento la validación porque precisa más revisión
            if ($datos['dni'] || $datos['cuit']) {
                $dato_consultar_sql = $datos['cuit']
                    ? "cuit = '".$datos['cuit']."'"
                    : "dni = '".$datos['dni']."'";
                $existe_sql = consulta_sql(
                    "SELECT idcliente, nombre
                    FROM clientes
                    WHERE ".$dato_consultar_sql."
                        AND idcliente != '$id'
                    LIMIT 1"
                    );
                if (contar_sql($existe_sql)) {
                    $existe_cliente = array_sql($existe_sql);
                    mensajes_alta($i18n[318].'<a href=clientes.php?a=ver&id='.$existe_cliente['idcliente'].' target=_blank>'.$existe_cliente['nombre'].'</a>');
                }
                if ($datos['cuit'] && !$datos['razonsocial']) {
                    mensajes_alta($i18n[323]);
                }
            }

            consulta_sql("UPDATE clientes SET
                estado = '".$datos['estado']."',
                nombre = '".$datos['nombre']."',
                contacto = '".$datos['contacto']."',
                idtipocliente = '".$datos['idtipocliente']."',
                idusuario = '".$datos['idusuario']."',
                telefonos = '".$datos['telefonos']."',
                telefonos_verificado = '".$datos['telefonos_verificado']."',
                mail = '".$datos['mail']."',
                domicilio = '".$datos['domicilio']."',
                idlocalidad = '".$datos['idlocalidad']."',
                idtipoiva = '".$datos['idtipoiva']."',
                razonsocial = '".$datos['razonsocial']."',
                cuit = '".$datos['cuit']."',
                tipodoc = '".$datos['tipodoc']."',
                dni = '".$datos['dni']."',
                observacion = '".escape_sql($datos['observacion'])."',
                obsinterna = '".$datos['obsinterna']."',
                obsrecordatorio = '".$datos['obsrecordatorio']."'"
                .($_SESSION['modulo_ML'] && $tienda['ML_estado'] || $tienda['MP_estado']
                    ? ", ML_user_id = '".$datos['ML_user_id']."', ML_user_id2 = '".$datos['ML_user_id2']."', ML_nickname = '".$datos['ML_nickname']."'"
                    : "")."
            WHERE idcliente = '$id'");

            fullsearch_update('clientes',$id);
            extras_mod();
            ir_atras();
        }
        break;

    case $i18n[258]: // Cancelar
        ir_atras();
        break;
}

$tipocliente = array_sql(consulta_sql("SELECT * FROM categorias_clientes WHERE idtipocliente='".$datos['idtipocliente']."' LIMIT 1"));

ventana_inicio($i18n[67].$datos['nombre'], '100', array(
    array('tipo' => 'imagen', 'url' => 'clientes.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[285]),
    array('tipo' => 'imagen', 'url' => 'clientes.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[124], 'permiso' => 'clientes_baja', 'opciones' => 'onclick="return confirma('."'$i18n[171]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[169])));
{
    // Datos de contacto
    contenido_inicio($i18n[68]);
    {
        entrada('texto', 'nombre', $i18n[28], $datos['nombre'], '40', '60');
        marcas($i18n[38], '10', array(array('nombre' => 'estado', 'titulo' => $i18n[71], 'valor' => $datos['estado'])));
        entrada('texto', 'contacto', $i18n[29], $datos['contacto'], '25', '150');
        selector('idtipocliente', $i18n[30], $datos['idtipocliente'], '25', 'categorias_clientes', 'nombre', true, true, true, $i18n[260]);
        entrada('email', 'mail', $i18n[33], $datos['mail'], '25', '320');
        if ($_SESSION['modulo_trazabilidad']) {
            entrada('texto', 'telefonos', $i18n[31], $datos['telefonos'], '15', '150');
            marcas(' ', '10', array(array('nombre' => 'telefonos_verificado', 'titulo' => 'Verificado', 'valor' => $datos['telefonos_verificado'])));
        } else {
            entrada('texto', 'telefonos', $i18n[31], $datos['telefonos'], '25', '150');
        }
        entrada('texto', 'domicilio', $i18n[32], $datos['domicilio'], '25', '150');
        entrada('hidden', 'idlocalidad', false, $datos['idlocalidad']);
        entrada('texto', 'localidad', $i18n[78], $datos['localidad'], '25', '150', false, 'onFocus="seleccionarLocalidades();"', 'categorias_localidades');

        if ($_SESSION['modulo_tienda'] && $tienda['tienda_estado']) {
            enlaces('', array(
                array('tipo' => 'modal', 'url' => 'clientes_pass', 'id' => $id, 'valor' => $i18n[187])
            ));
        }
    }
    contenido_fin();

    // Datos de facturación
    contenido_inicio($i18n[69]);
    {
        texto('texto', $i18n[86], '<br>'.$datos['razonsocial'].' ', '25', false, false, false, 'id="razonsocial_texto"'); // Pongo un espacio para que aparezca siempre
        texto('texto', $i18n[79], '<br>'.$datos['tipoiva'].' ', '25', false, false, false, 'id="tipoiva_texto"'); // Pongo un espacio para que aparezca siempre
        salto_linea();

        entrada('cuit', 'cuit', $i18n[49], $datos['cuit'], '25', false, $i18n[338], 'autocomplete="off"');
        selector_array('tipodoc', $i18n[337], $datos['tipodoc'], '25', $tipodocs, '', 'onchange="validar_dni()"');
        entrada('numeros', 'dni', $i18n[183], ($datos['dni'] ? $datos['dni'] : ''), '25');

        entrada('hidden', 'idtipoiva', false, $datos['idtipoiva']);
        entrada('hidden', 'razonsocial', false, $datos['razonsocial']);

        salto_linea();

        bloque_inicio('afip_responde');
        {
            texto('italica', false, $i18n[309], '50', false, 'info');
            enlaces(false, array(
                array('tipo' => 'modal', 'modulo' => 'clientes',
                    'url' => 'afip_no_responde', 'id' => $id, 'valor' => $i18n[202])
                ), '50');
            enlaces(false, array(
                array('tipo' => 'enlace', 'url' => '#', 'valor' => $i18n[304],
                    'modulo' => 'clientes', 'opciones' => 'onclick="return chequear_dni()"')),
                '50');
        }
        bloque_fin();
        bloque_inicio('afip_validando', 'style="display: none;"');
        {
            texto('italica', false, $i18n[312], '100', false, 'ajax');
        }
        bloque_fin();
        bloque_inicio('afip_no_responde', 'style="display: none;"');
        {
            texto('italica', false, $i18n[203], '50', false, 'alerta');
            enlaces(false, array(
                array('tipo' => 'modal', 'modulo' => 'clientes',
                    'url' => 'afip_no_responde', 'id' => $id, 'valor' => $i18n[202])
                ), '50');
        }
        bloque_fin();
    }
    contenido_fin();

    // Vendedor
    if (!$_SESSION['sistema_gratis']) {
        contenido_inicio($i18n[334], '50');
        {
            selector('idusuario', $i18n[118], $datos['idusuario'], '50', 'usuarios', 'nombre', false, true, true, $i18n[335]);
        }
        contenido_fin();
    }

    if ($_SESSION['modulo_ML'] && ($tienda['ML_estado'] || $tienda['MP_estado'])) {
        // Integración con MercadoLibre
        contenido_inicio($i18n[184], '100', false, false, false, false, 'mini-icono-ml');
        {
            entrada('numeros', 'ML_user_id', $i18n[185], ($datos['ML_user_id'] ? $datos['ML_user_id'] : ''), '25', '9');
            entrada('texto', 'ML_nickname', $i18n[186], $datos['ML_nickname'], '25', '60');
            salto_linea();
            entrada('numeros', 'ML_user_id2', $i18n[321], ($datos['ML_user_id2'] ? $datos['ML_user_id2'] : ''), '25', '9', $i18n[322]);
            if (!$tienda['ML_access_token'] || !$tienda['ML_user_id']) {
                texto('italica', '', $i18n[299]);
            }
        }
        contenido_fin();
    }

    contenido_inicio($i18n[330], '50');
    {
        texto('titulo', $i18n[330], $datos['moneda'].' ('.$datos['simbolo'].')');
    }
    contenido_fin();

    extras();

    // Observaciones
    contenido_inicio($i18n[253], '100', true, false);
    {
        area('observacion', $i18n[89], $datos['observacion']);
        area('obsinterna', $i18n[90], $datos['obsinterna'], 'auto', $i18n[259]);
        area('obsrecordatorio', $i18n[91], $datos['obsrecordatorio'], 'auto', $i18n[272]);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[62]), array('valor' => $i18n[258])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    // Necesito la tabla condiciones en JS
    var tablas_condiciones = <?php echo tabla_json('tablas_condiciones'); ?>;

    $(function() { validar_dni(); });

    function validacion_clientes_mod(boton)
    {
        var mail = $("#marco_clientes_mod input[name='mail']").val();

        if (boton == '<?php echo $i18n[62]; ?>' && $("#marco_clientes_alta input[name='nombre']").val() == '') {
            <?php script_validacion_flotante("alerta", $i18n[24], "input[name='nombre']"); ?>
            if (mail == '') {
                <?php script_validacion_flotante("informacion", $i18n[25], "input[name='mail']"); ?>
            }
            return false;

        } else if (boton == '<?php echo $i18n[62]; ?>' && mail != '' && !$("input[name='mail']").valid()) {
            <?php script_validacion_flotante("alerta", $i18n[65], "input[name='mail']"); ?>
            return false;

        } else {
            return true;
        }
    };
</script>
