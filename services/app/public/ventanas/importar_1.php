<?php
mensajes_efimeros();

ventana_inicio($i18n_funciones[73].' '.strtolower($i18n_funciones[$modulo]).' - '.$paso.'º paso');
{
    contenido_inicio();
    {
        texto('italica', false, $i18n_herramientas[2].$i18n_funciones[99], false, false, 'ayuda');
    }
    contenido_fin();

    include 'backup.php';

    contenido_inicio($i18n_herramientas[1], '100', false, false, '', 'id="archivo"');
    {
        texto('italica', false, $i18n_herramientas[3]);

        $random = recibir_variable('texto', 'random', false);
        if (!$random){
            $random = generar_random(5);
            entrada('file', 'archivo', ' ', '', 'auto', false, false, 'id="archivo"');
        } else {
            /*recibi el random por get, entonces chequeo si existe el archivo*/
            entrada('hidden', 'archivo_subido', 'archivo_subido', 1);
            texto('texto', false, $i18n_funciones[108]);
            botones(array(array('tipo' => 'nueva', 'valor' => $i18n_funciones[107])));
        }

        entrada('hidden', 'paso', 'paso', $paso);
        entrada('hidden', 'random', 'random', $random);
    }
    contenido_fin();

    botones(array(array('tipo' => 'nueva', 'valor' => $i18n_funciones[80], 'opciones' => 'id = "aceptar-proceso" disabled')));
}
ventana_fin();

?>
<script>
    var sucursales = '<?php if($modulo == 'productos') echo '&idlista=1&iddeposito=1'; else echo '';?>';
    $("#url_exportar").attr('href', '<?php echo URL_SCRIPTS."/?script=exportar&modulo=".$modulo; ?>'+sucursales);
    $("#archivo").hide();
</script>
