<?php

if ($boton) {
    $puntodeventa = recibir_variable('entero', 'puntodeventa', false);
    consulta_sql("UPDATE variables SET valor = '".$puntodeventa."' WHERE variable = 'puntodeventa_predeterminado_ventas_facturacion_masiva'");
    ir_atras();
}

$resultado_sql = consulta_sql("SELECT idtipoventa, estado, nombre, letra, puntodeventa, ultimonumero FROM categorias_ventas ORDER BY idtipoventa");
$puntodeventa = campo_sql(consulta_sql("SELECT valor FROM variables WHERE variable = 'puntodeventa_predeterminado_ventas_facturacion_masiva'"));

ventana_inicio($i18n[256], '100', array(array('url' => 'configuraciones.php?a=altaventa', 'a' => 'alta', 'title' => $i18n[189])));
{
    contenido_inicio();
    {
        if (contar_sql($resultado_sql)) {
            linea_inicio('titulo', 2);
            {
                celda('texto', '', 'imagen');
                celda('texto', $i18n[186], '20');
                celda('texto', $i18n[59], 'auto');
            }
            linea_fin();

            $selector_punto_de_venta[] = array('id' => 0, 'valor' => $i18n[245]);
            while ($tipoventa = array_sql($resultado_sql)) {
                if (array_search($tipoventa['puntodeventa'], array_column($selector_punto_de_venta, 'id')) === false && $tipoventa['estado'] == 1) {
                    $selector_punto_de_venta[] = array('id' => $tipoventa['puntodeventa'], 'valor' => $i18n[193].' '. $tipoventa['puntodeventa']);
                }
                linea_inicio('fila', 2, ($tipoventa['estado'] ? 'configuraciones.php?a=modventa&id='.$tipoventa['idtipoventa'] : false));
                {
                    $estado = ($tipoventa['estado'] ? 'habilitado' : 'deshabilitado');
                    celda('imagen', $i18n[$estado], 'imagen', $estado);
                    celda('texto', $tipoventa['letra'].completar_numero($tipoventa['puntodeventa'], 5).'-'.completar_numero($tipoventa['ultimonumero'], 8), '20');
                    celda('texto', $tipoventa['nombre'], 'auto');
                }
                linea_fin(array(
                    array('url' => 'configuraciones.php?a=modventa&id='.$tipoventa['idtipoventa'], 'a' => 'mod', 'title' => $i18n[187]),
                    array('tipo' => 'ajax', 'url' => 'tiposdeventas.php?a=baja&id='.$tipoventa['idtipoventa'], 'a' => 'baja', 'title' => $i18n[188])
                ));
            }
        } else {
            linea_inicio();
            {
                celda('texto', $i18n[48]);
            }
            linea_fin();
        }
    }
    contenido_fin();

    /* Oculto funcionalidad hasta próxima actualización
    contenido_inicio($i18n[636], '100', false, false, $i18n[638]);
    {
        selector_array('puntodeventa', $i18n[637], $puntodeventa, '30', $selector_punto_de_venta);
        texto('italica', false, $i18n[639].'. '.$i18n[638], false, false, 'info');
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[65]), array('valor' => $i18n[66])));
    */
}
ventana_fin();