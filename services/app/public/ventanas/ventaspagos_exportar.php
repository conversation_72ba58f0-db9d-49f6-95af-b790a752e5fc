<?php
$multiples_pagos = $pagos ? $pagos : recibir_variable('texto', 'pagos', true);
if ($multiples_pagos) {
    $resultado_sql = consulta_sql(
        "SELECT ventaspagos.*,
        usuarios.nombre AS usuario,
        tablas_formasdepago.nombre AS formapago,
        movimientosxcajas.idcaja,
        categorias_conceptos.padres, categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto,
        categorias_cajas.nombre AS caja,
        ventas.numero,
        categorias_ventas.letra, categorias_ventas.puntodeventa,
        cheques.*,
        categorias_tributos.nombre AS tributo,
        retenciones.observacion AS retencionesobservacion,
        tablas_bancos.nombre AS banco,
        monedas.simbolo
    FROM ventaspagos
        LEFT JOIN usuarios ON ventaspagos.idusuario = usuarios.idusuario
        LEFT JOIN tablas_formasdepago ON ventaspagos.idformapago = tablas_formasdepago.idformapago
        LEFT JOIN movimientosxcajas ON (ventaspagos.idventapago = movimientosxcajas.idrelacion AND movimientosxcajas.tiporelacion = 'clientepago')
        LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
        LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
        LEFT JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
        LEFT JOIN ventas ON ventaspagos.idventa = ventas.idventa
        LEFT JOIN categorias_ventas ON categorias_ventas.idtipoventa = ventas.idtipoventa
        LEFT JOIN cheques ON ventaspagos.idrelacion = cheques.idcheque
        LEFT JOIN tablas_bancos ON cheques.idbanco = tablas_bancos.idbanco
        LEFT JOIN retenciones ON ventaspagos.idrelacion = retenciones.idretencion
        LEFT JOIN categorias_tributos ON retenciones.idtributo = categorias_tributos.idtributo
        LEFT JOIN monedas ON ventaspagos.idmoneda = monedas.idmoneda
    WHERE idnumeroventapago = (
        SELECT idnumeroventapago FROM ventaspagos
        WHERE idventapago = '$id'
        LIMIT 1
    )
    AND movimientosxcajas.tiporelacion = 'clientepago'
    ORDER BY ventaspagos.fecha ASC");

    $tmp_data = array_all_sql($resultado_sql);
    $ventapago = $tmp_data[0];

} else {

    $ventapago = array_sql(consulta_sql(
        "SELECT ventaspagos.*,
            usuarios.nombrereal AS usuario,
            tablas_formasdepago.nombre AS formapago,
            cheques.*,
            categorias_tributos.nombre AS tributo,
            retenciones.observacion AS retencionesobservacion,
            tablas_bancos.nombre AS banco,
            monedas.simbolo
        FROM ventaspagos
            LEFT JOIN usuarios ON ventaspagos.idusuario = usuarios.idusuario
            LEFT JOIN tablas_formasdepago ON ventaspagos.idformapago = tablas_formasdepago.idformapago
            LEFT JOIN cheques ON ventaspagos.idrelacion = cheques.idcheque
            LEFT JOIN tablas_bancos ON cheques.idbanco = tablas_bancos.idbanco
            LEFT JOIN retenciones ON ventaspagos.idrelacion = retenciones.idretencion
            LEFT JOIN categorias_tributos ON retenciones.idtributo = categorias_tributos.idtributo
            LEFT JOIN monedas ON ventaspagos.idmoneda = monedas.idmoneda
        WHERE idventapago = '$id'
        LIMIT 1"));
}

$cliente = array_sql(consulta_sql("SELECT idcliente, nombre, contacto, razonsocial, cuit FROM clientes WHERE idcliente = '".$ventapago['idcliente']."' LIMIT 1"));

// Defino si es RP o DP
if (contar_sql(consulta_sql("SELECT idtipoventa FROM ventasxclientes WHERE id = '$id' AND idtipoventa = '-1' LIMIT 1"))) {
    $temp_recibo = 'Devolución de pago Nº DP';
} else {
    $temp_recibo = 'Recibo pago Nº RP';
}

exportar('
    <div id="prn_ventana_cuerpo">
        <div id="prn_ventana_cuerpo_inicio">
            <p class="prn_p_titulo_ventana_cuerpo">'.$temp_recibo.completar_numero($ventapago['idnumeroventapago'], 8).'</p>
    </div> <!--Cierro el div id="ventana_cuerpo_inicio-->

        <table class="prn_t_ventana_cuerpo" border="0" width="100%">
            <tr>
                <td class="prn_td_subtitulos" width="50%">Datos básicos</td>
                <td class="prn_td_subtitulos" width="50%">Cliente</td>
            </tr>
            <tr>
                <td class="prn_td_datos">');
if ($ventapago['idventa'] != '0') {
    $venta = array_sql(consulta_sql("SELECT * FROM ventas WHERE idventa='".$ventapago['idventa']."'"));
    $tipoventa = array_sql(consulta_sql("SELECT * FROM categorias_ventas WHERE idtipoventa='".$venta['idtipoventa']."'"));
    $numeroventa = $tipoventa['letra'].completar_numero($tipoventa['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8);
    exportar('
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Venta relacionado: </span>'.$numeroventa.'</p>');
}
exportar('
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Total: </span>'.convertir_numero($multiples_pagos ? array_sum(array_column($tmp_data, 'total')) : $ventapago['total'], $ventapago['simbolo']).'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Fecha: </span>'.fecha_sql($ventapago['fecha']).'</p>');
if (!$multiples_pagos) {
    exportar('
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Forma de pago: </span>'.$ventapago['formapago'].'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Usuario: </span>'.$ventapago['usuario'].'</p>');
}
exportar('
                </td>
                <td class="prn_td_datos">
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Nombre: </span>'.$cliente['nombre'].'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Cliente Nº: </span>'.$cliente['idcliente'].'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Contacto: </span>'.$cliente['contacto'].'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Razón social: </span>'.$cliente['razonsocial'].'</p>
                    <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">CUIT: </span>'.($cliente['cuit'] ? $cliente['cuit']['0'].$cliente['cuit']['1'].'-'.$cliente['cuit']['2'].$cliente['cuit']['3'].$cliente['cuit']['4'].$cliente['cuit']['5'].$cliente['cuit']['6'].$cliente['cuit']['7'].$cliente['cuit']['8'].$cliente['cuit']['9'].'-'.$cliente['cuit']['10'] : '').'</p>
                </td>
            </tr>');

if (!$multiples_pagos && $ventapago['tiporelacion'] == 'cheque') {
    if ($ventapago['tipo'] == 'propio'){
        $ventapago['tipo'] = 'Propio';
        $ventapago['cobropago'] = 'pago: ';
    } else {
        $ventapago['tipo'] = 'De tercero';
        $ventapago['cobropago'] = 'cobro: ';
    }
    exportar('
        <tr>
            <td class="prn_td_datos">
                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Tipo de cheque: </span>'.$ventapago['tipo'].'</p>
                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Fecha de '.$ventapago["cobropago"].'</span>'.fecha_sql($ventapago['fechacobro']).'</p>
                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Banco: </span>'.$ventapago['banco'].'</p>
                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Titular: </span>'.$ventapago['titular'].'</p>
                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Número de cheque: </span>'.$ventapago['numero'].'</p>
            </td>
        </tr>
    ');
}
if (!$multiples_pagos && $ventapago['tiporelacion'] == 'retencion') {
    exportar('
    <tr>
        <td class="prn_td_datos">
            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Impuesto de la retención: </span>'.$ventapago['tributo'].'</p>
            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">Observación de la retención: </span>'.$ventapago['retencionesobservacion'].'</p>
        </td>
    </tr>
    ');
}

if ($multiples_pagos) {
    puntero_sql($resultado_sql);

    if (contar_sql($resultado_sql)) {
        exportar('</table>');
        exportar('<table class="prn_t_ventana_cuerpo" border="0" width="100%">');
        exportar('<tr class="prn_tr_titulo_lista">
                    <th>Fecha</th>
                    <th>Forma de pago</th>
                    <th>Detalle</th>
                    <th>Importe</th>');

        exportar('</tr>');
    }

    while ($venta_pago = array_sql($resultado_sql)) {
        if ($venta_pago['tiporelacion'] == 'cheque') {
            $detalle = 'Nro. de cheque: ' . $venta_pago['numero'] . ' | Banco: ' . $venta_pago['banco'] . ' | Titular: ' . $venta_pago['titular'] . ($venta_pago['tipo'] == 'tercero' ? ' | Fecha cobro: ' : ' | Fecha pago: ') . mostrar_fecha('fecha', $venta_pago['fechacobro']);
        } else if ($venta_pago['tiporelacion'] == 'retencion') {
            $detalle = 'Impuesto de la retención: ' . $venta_pago['tributo'] . ' | Obs. de la retención: ' . $venta_pago['retencionesobservacion'];
        } else {
            $detalle = '';
        }
        exportar('
                    <tr>
                        <td class="prn_td_texto">'.mostrar_fecha('fechayhora', $venta_pago['fecha']).'</td>
                        <td class="prn_td_texto">'.$venta_pago['formapago'].'</td>
                        <td class="prn_td_texto">'.$detalle.'</td>
                        <td class="prn_td_texto">'.convertir_numero($venta_pago['total'], 'moneda', $venta_pago['simbolo']).'</td>');
        exportar('
                    </tr>');
    }

    exportar('</table>');
    exportar('<table class="prn_t_ventana_cuerpo" border="0" width="50%">');
}
extras_exportar();
exportar('
            <tr>
                <td class="prn_td_datos" colspan="2">
                    <p class="prn_p_nombre_campo">Observaciones</p>
                    <p class="prn_p_texto_campo">'.$ventapago['observacion'].'</p>
                </td>
            </tr>
            <tr>
                <td></td>
                <td class="prn_td_datos"><br><br>
                    <p class="prn_p_nombre_campo" style="border-top: dotted;" id="ventaspagos_firma"><br>Firma y aclaración</p>
                </td>
            </tr>
        </table>

        <div id="prn_ventana_cuerpo_fin"> </div>
    </div>  <!--Cierro el <div id="prn_ventana_cuerpo">-->
');
