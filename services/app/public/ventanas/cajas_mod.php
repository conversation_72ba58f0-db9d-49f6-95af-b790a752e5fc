<?php
$tipocaja = array_sql(consulta_sql(
    "SELECT cajas.idtipocaja, saldoapertura, saldocierre, idmoneda,
        categorias_cajas.estado
    FROM cajas
        LEFT JOIN categorias_cajas ON categorias_cajas.idtipocaja = cajas.idtipocaja
    WHERE cajas.idcaja = '".$id."'
    LIMIT 1")
);

switch ($boton ? $boton : $a) {
    case $i18n_funciones['aceptar']:
        $datos = recibir_matriz(array(
        array('largo', 'observacion')
    ));
        consulta_sql("UPDATE cajas SET
            observacion = '".escape_sql($datos['observacion'])."'
            WHERE idcaja = '".$id."'
            LIMIT 1
        ");
        ir_atras();
        break;

    case $i18n_funciones['abrir']:
        consulta_sql("INSERT INTO cajas SET
            idtipocaja = '".$tipocaja['idtipocaja']."',
            fechaapertura = '".fecha_sql('ahora')."',
            saldoapertura = '".$tipocaja['saldocierre']."',
            idmoneda = '".$tipocaja['idmoneda']."'
        ");
        $id = id_sql();
        consulta_sql("UPDATE categorias_cajas SET
            idcaja = '".$id."'
            WHERE idtipocaja='".$tipocaja['idtipocaja']."'
            LIMIT 1
        ");
        actualizar_saldo('cajas', $id, $tipocaja['saldocierre']);
        ir_ahora('cajas.php?a=mod&id='.$id);
        break;

    case $i18n_funciones['reabrir']:
        consulta_sql("UPDATE cajas SET
            fechacierre = ''
            WHERE idcaja = '".$id."'
            LIMIT 1
        ");
        ir_atras();
        break;

    case $i18n_funciones['modificar']:
        ir_ahora('cajas.php?a=mod&id='.$id.'&modificar=1');

    case 'mod':
        $acepto_modificar = isset($_GET['modificar'])
            ? true
            : false;
        $datos = array_sql(consulta_sql(
            "SELECT cajas.*,
                categorias_cajas.*,
                categorias_cajas.idcaja AS idcaja_categorias,
                saldos.saldo,
                (SELECT SUM(total) FROM movimientosxcajas WHERE idcaja = $id AND total > 0) AS totalingreso,
                (SELECT SUM(total) FROM movimientosxcajas WHERE idcaja = $id AND total < 0) AS totalegreso,
                (SELECT SUM(total) FROM movimientosxcajas WHERE idcaja = $id AND conciliacion = '1') AS totalconciliado,
                (SELECT SUM(total) FROM movimientosxcajas WHERE idcaja = $id AND conciliacion = '0') AS totalsinconciliar,
                monedas.nombre AS moneda, monedas.simbolo
            FROM cajas
                INNER JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
                LEFT JOIN movimientosxcajas ON cajas.idcaja = movimientosxcajas.idcaja
                LEFT JOIN saldos ON saldos.tiporelacion = 'cajas' AND saldos.idrelacion = cajas.idcaja
                LEFT JOIN monedas ON cajas.idmoneda = monedas.idmoneda
            WHERE cajas.idcaja = '$id'
            LIMIT 1")
        );

        // La caja está abierta si no tiene fecha de cierre
        $caja_abierta = $datos['fechacierre'] == '0000-00-00 00:00:00'
            ? true : false;
        $saldo = $caja_abierta
            ? $datos['saldo']
            : $datos['saldocierre'];

        $perfilxcaja = array_sql(consulta_sql("SELECT *
            FROM perfilesxcajas
            WHERE idtipocaja = '".$tipocaja['idtipocaja']."'
            AND idperfil = '".$_SESSION['usuario_idperfil']."'
            LIMIT 1 ")
        );

        if($datos['tipo'] == 'cheque' && $_SESSION['control_cheques_ordenar'] == 'fechacobro DESC')
            $orden = 'IF(fechacobro IS NULL, fechaconciliacion , fechacobro) DESC';
        else
            $orden = 'fecha DESC';

        $orden .= ', idmovimientoxcaja ASC';

        $resultado_sql = consulta_sql("SELECT movimientosxcajas.*, cheques.fechacobro, compraspagos.idnumerocomprapago AS idnumero,
                    categorias_conceptos.padres, categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto
                FROM movimientosxcajas
                    LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
                    LEFT JOIN compraspagos ON movimientosxcajas.idrelacion = compraspagos.idcomprapago
                    LEFT JOIN cheques ON compraspagos.idrelacion = cheques.idcheque AND compraspagos.tiporelacion = 'cheque'
                WHERE movimientosxcajas.idcaja = '$id'
                    AND movimientosxcajas.tiporelacion = 'proveedorpago'

                UNION

                SELECT movimientosxcajas.*, cheques.fechacobro, ventaspagos.idnumeroventapago AS idnumero,
                    categorias_conceptos.padres, categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto
                FROM movimientosxcajas
                    LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
                    LEFT JOIN ventaspagos ON movimientosxcajas.idrelacion = ventaspagos.idventapago
                    LEFT JOIN cheques ON ventaspagos.idrelacion = cheques.idcheque AND ventaspagos.tiporelacion = 'cheque'
                WHERE movimientosxcajas.idcaja = '$id'
                AND movimientosxcajas.tiporelacion = 'clientepago'

                UNION

                SELECT movimientosxcajas.*, COALESCE(null) AS fechacobro, COALESCE(0) AS idnumero, categorias_conceptos.padres,
                    categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto
                FROM movimientosxcajas
                    LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
                    LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
                WHERE movimientosxcajas.idcaja = '$id'
                    AND movimientosxcajas.tiporelacion != 'clientepago' AND movimientosxcajas.tiporelacion != 'proveedorpago'

                ORDER BY ".$orden."
                LIMIT ".$_SESSION['control_agrandar']);

        $i = 0;
        while ($ordenar[$i] = array_sql($resultado_sql)) {
            $saldo -= $ordenar[$i]['total'];
            $i++;
        }
        break;

    case $i18n[112]: // Ordernar fecha por cobro / pago
        actualizar_controles(array('cheques_ordenar' => 'fechacobro DESC'));
        ir_ahora($modulo.'.php?a=mod&id='.$id); //Ojo, no me convence esto
        break;
    case $i18n[113]: // Ordenar por fecha de movimiento
        actualizar_controles(array('cheques_ordenar' => 'fecha DESC'));
        ir_ahora($modulo.'.php?a=mod&id='.$id); //Ojo, no me convence esto
        break;

    default:
        mostrar_error('Error en default', true);
        break;
}
