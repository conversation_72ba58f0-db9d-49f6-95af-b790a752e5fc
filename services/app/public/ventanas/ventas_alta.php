<?php

// Si es nuevo en FEG no tiene tipos de ventas configurados lo mando a configurar
if ($_SESSION['sistema_gratis'] && !contar_sql(consulta_sql(
    "SELECT idtipoventa FROM categorias_ventas WHERE estado = 1 LIMIT 1"))) {
    mensajes_alta($i18n[214]);
    ir_ahora('configuraciones.php?a=wsfe');
}

switch ($boton) {
    // TODO: llevar esta lógica a JavaScript y directamente redireccionar a ventas_alta_auto o altapago
    case $i18n[10]:
        $datos = recibir_matriz(array(
        array('entero', 'idtipoventa'),
        array('texto', 'clientes_id')
    ));
        $datos['idcliente'] = $datos['clientes_id'] ? $datos['clientes_id'] : 1;

        consulta_sql("UPDATE controles SET ultimotipoventa='".$datos['idtipoventa']."' WHERE idusuario='".$_SESSION['usuario_idusuario']."' LIMIT 1");
        $_SESSION['control_ultimotipoventa'] = $datos['idtipoventa'];

        if ($datos['idtipoventa'] == -1)
            ir_ahora('ventas.php?a=altapago&devolucion=1&idcliente='.$datos['idcliente']);

        if ($datos['idtipoventa']) {
            ir_ahora('ventas.php?a=alta_auto&idcliente='.$datos['idcliente'].'&idtipoventa='.$datos['idtipoventa']);
        } else {
            ir_ahora('ventas.php?a=altapago&idcliente='.$datos['idcliente']);
        }
        break;

    case $i18n[15]: // Cancelar
        ir_atras();
        break;

    default:
        $datos = array('idtipoventa' => $_SESSION['control_ultimotipoventa']);
        break;
}

ventana_inicio($i18n[56]);
{
    // Seleccion de clientes
    contenido_inicio($i18n[35], '100', false, false, $i18n[12]);
    {
        seleccionador('clientes');
    }
    contenido_fin();

    // Datos basicos
    contenido_inicio($i18n[34]);
    {
        // Select con array y sumamos -1 Devolución de pagos y 0 Recibo de pago si no es FG
        $selector_ventas = array();
        $categorias_sql = consulta_sql("SELECT idtipoventa, nombre FROM categorias_ventas WHERE estado = 1");
        if(!$_SESSION['sistema_gratis']){
            $selector_ventas[0] = array('id' => 0, 'valor' => $i18n[111]);
            $selector_ventas[-1] = array('id' => -1, 'valor' => $i18n[250]);
        }

        while ($categoria = array_sql($categorias_sql)){
            $selector_ventas[$categoria['idtipoventa']] = array('id' => $categoria['idtipoventa'], 'valor' => $categoria['nombre']);
        }
        selector_array('idtipoventa', $i18n[49], $datos['idtipoventa'], '', $selector_ventas);

    }
    contenido_fin();

    botones(array(array('valor' => $i18n[10]), array('valor' => $i18n[15])));
}
ventana_fin();
