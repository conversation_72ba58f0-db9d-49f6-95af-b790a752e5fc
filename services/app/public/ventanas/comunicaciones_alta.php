<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('entero', 'idcliente'),
        array('entero', 'idusuario'),
        array('entero', 'idtipocomunicacion'),
        array('largo', 'obssolicitado'),
        array('largo', 'obsrealizado'),
        array('fecha', 'fechainicio'),
        array('fecha', 'fechafin'),
        array('entero', 'idtiporespuestaxcomunicacion')
    ));
    if ($datos['clientes_id'])
        $datos['idcliente'] = $datos['clientes_id'];
    else
        $datos['idcliente'] = 1;
} else {
    $datos = array('idcliente' => 0, 'idtipocomunicacion' => 0, 'fechainicio' => 'ahora', 'idusuario' => $_SESSION['usuario_idusuario'], 'prioridad' => 3);
}

switch ($boton) {
    case $i18n[66]: //Agregar
        exit("ESTOY PROBANDO Y ESTO NO ESTÁ HABILITADO TODAVÍA, VUELVAS PRONTOS - INSERT INTO comunicaciones (idtipocomunicacion, idcliente, idusuario, fechainicio, fechafin, obssolicitado, obsrealizado, idtiporespuestaxcomunicacion) VALUES ('".$datos['idtipocomunicacion']."', '".$datos['idcliente']."', '".$datos['idusuario']."', '".fecha_sql($datos['fechainicio'])."', '".fecha_sql($datos['fechafin'])."','".$datos['obssolicitado']."','".$datos['obsrealizado']."', '".$datos['idtiporespuestaxcomunicacion']."')");
        $id = id_sql();
        extras_alta();
        ir_ahora('comunicaciones.php?a=ver&id'.$id);
        break;

    case $i18n[31]: // Cancelar
        ir_atras();
        break;
}

ventana_inicio($i18n[43]);
{
    // Seleccion de clientes
    contenido_inicio($i18n[45], '100', false, false, $i18n[67]);
    {
        seleccionador('clientes');
    }
    contenido_fin();

    // Ingrese los datos básicos
    contenido_inicio($i18n[33]);
    {
        selector_array('prioridad', $i18n[70], $datos['prioridad'], '25', array(array('id' => '1', 'valor' => $i18n['prioridad_1']), array('id' => '2', 'valor' => $i18n['prioridad_2']), array('id' => '3', 'valor' => $i18n['prioridad_3']), array('id' => '4', 'valor' => $i18n['prioridad_4']), array('id' => '5', 'valor' => $i18n['prioridad_5'])));
        selector('idusuario', $i18n[23], $datos['idusuario'], '25', 'usuarios', 'nombre', false, false, true);
        selector_familiar('idtipocomunicacion', $i18n[9], $datos['idtipocomunicacion'], '25', 'categorias_comunicaciones', true, true, true);
        selector_familiar('idtiporespuestaxcomunicacion', $i18n[20], $datos['idtiporespuestaxcomunicacion'], '25', 'categorias_respuestasxcomunicaciones', true, true, true);
        entrada('fechayhora', 'fechainicio', $i18n[7], $datos['fechainicio'], '25');
        entrada('fechayhora', 'fechafin', $i18n[8], $datos['fechafin'], '25');
    }
    contenido_fin();

    contenido_inicio($i18n[5]);
    {
        area('obssolicitado', $i18n[24], $datos['obssolicitado']);
        area('obsrealizado', $i18n[25], $datos['obsrealizado']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[66]), array('valor' => $i18n[31])));
}
ventana_fin();
