<?php
switch ($boton ? $boton : $a) {
    case $i18n_funciones['cancelar']:
        ir_atras();
        break;

    case $i18n_funciones['aceptar']:
        $datos = recibir_matriz(array(
        array('texto', 'nombre'),
        array('texto', 'compartida'),
        array('texto', 'tipo'),
        array('entero', 'estado'),
        array('fecha', 'fechacierre')
    ));
        // TODO: Falta la validación del nombre
        consulta_sql("UPDATE categorias_cajas SET
            nombre = '".$datos['nombre']."',
            compartida = '".$datos['compartida']."',
            tipo = '".$datos['tipo']."',
            estado = '".$datos['estado']."'
            WHERE idtipocaja = '".$id."'
            LIMIT 1
        ");

        if ($datos['estado'] != '1' && $datos['fechacierre'] == 0) {  //Si deshabilito, pongo fecha de cierre en la tabla campos, si no lo tiene
            consulta_sql("UPDATE cajas SET fechacierre = DATE('".date("Y-m-d H:i:s")."') WHERE idtipocaja = '".$id."'");
        }

        ir_atras();
        break;

    case $i18n_funciones['agregar']:
        $datos = recibir_matriz(array(
        array('texto', 'nombre'),
        array('texto', 'compartida'),
        array('texto', 'tipo'),
        array('entero', 'estado'),
        array('entero', 'idmoneda')
    ));

        // TODO: Falta la validación del nombre
        consulta_sql("INSERT INTO categorias_cajas SET
            nombre = '".$datos['nombre']."',
            compartida = '".$datos['compartida']."',
            estado = '".$datos['estado']."',
            tipo = '".$datos['tipo']."'");
        $idtipocaja = id_sql();

        consulta_sql("INSERT INTO cajas SET
            idtipocaja = '".$idtipocaja."',
            idmoneda = '".$datos['idmoneda']."',
            fechaapertura = '".fecha_sql('ahora')."'");
        $idcaja = id_sql();

        consulta_sql("UPDATE categorias_cajas
            SET idcaja = '$idcaja'
            WHERE idtipocaja = '$idtipocaja'");
        insertar_saldo('cajas', $idcaja);

        ir_atras();
        break;

    case 'altacaja':
        $datos = array(
            'estado' => 1,
            'compartida' => 1,
            );
        break;

    case 'modcaja':
        $datos = array_sql(consulta_sql(
            "SELECT categorias_cajas.nombre, compartida, tipo, estado, cajas.fechacierre, monedas.nombre AS moneda
            FROM categorias_cajas
            LEFT JOIN cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
            LEFT JOIN monedas ON cajas.idmoneda = monedas.idmoneda
            WHERE categorias_cajas.idtipocaja = '".$id."'
            LIMIT 1
        "));
        break;

    default:
        mostrar_error('Error en default', true);
        break;
}
