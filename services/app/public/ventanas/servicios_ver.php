<?php
$servicio = array_sql(consulta_sql(
    "SELECT servicios.*,
        usuarios.nombre AS usuario,
        categorias_servicios.nombre AS tiposervicio
    FROM servicios
        LEFT JOIN usuarios ON servicios.idusuario = usuarios.idusuario
        LEFT JOIN categorias_servicios ON servicios.idtiposervicio = categorias_servicios.idtiposervicio
    WHERE idservicio = '$id'
    LIMIT 1"));
$cliente = array_sql(consulta_sql(
    "SELECT idcliente, clientes.nombre, contacto, domicilio, telefonos, mail, clientes.obsrecordatorio,
        categorias_localidades.nombre AS localidad,
        monedas.simbolo
    FROM clientes
        LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
        LEFT JOIN monedas ON clientes.idmoneda = monedas.idmoneda
    WHERE idcliente = '".$servicio['idcliente']."'
    LIMIT 1"));

obsrecordatorio($cliente['obsrecordatorio']);

// Preparamos el boton de modificar
if ($_SESSION['perfil_servicios_mod'] && ($servicio['idusuario'] == $_SESSION['usuario_idusuario'] || $_SESSION['perfil_servicios_mod_todos'])) {
    $temp_boton_mod = array('url' => 'servicios.php?a=mod&id='.$id, 'a' => 'mod', 'title' => $i18n[225]);
} else {
    $temp_boton_mod = array('a' => 'mod_no', 'title' => $i18n[136]);
}

// Preparamos el boton de eliminacion
if ($_SESSION['perfil_servicios_baja'] && ($servicio['idusuario'] == $_SESSION['usuario_idusuario'] || $_SESSION['perfil_servicios_mod_todos'])) {
    $temp_boton_baja = array('tipo' => 'ajax', 'url' => 'servicios.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[230],
        'opciones' => 'onclick="return confirma('."'$i18n[249]'".')"');
} else {
    $temp_boton_baja = array('a' => 'baja_no', 'title' => $i18n[227]);
}

ventana_inicio($i18n[133].$id, '100', array(
    $temp_boton_mod,
    $temp_boton_baja,
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[228])));
{
    // Datos basicos
    contenido_inicio($i18n[94], '50');
    {
        texto('texto', $i18n[170], $i18n['estado_'.$servicio['estado']], 'auto', false, 'estado_'.$servicio['estado']);
        texto('texto', $i18n[37], $i18n['prioridad_'.$servicio['prioridad']], 'auto', false, 'prioridad_'.$servicio['prioridad']);
        texto('texto', $i18n[102], $servicio['usuario']);
        texto('texto', $i18n[103], $servicio['tiposervicio']);
        texto('fechayhora', $i18n[104], $servicio['fechasolicitado']);
        texto('fechayhora', $i18n[15], $servicio['fechainicio']);
        texto('fechayhora', $i18n[105], $servicio['fechafin']);
        texto('fechayhora', $i18n[233], $servicio['fechalimite']);
        texto('tiempo', $i18n[234], $servicio['tiempoestimado']);
        texto('tiempo', $i18n[235], $servicio['tiempodedicado']);
    }
    contenido_fin();

    // Cliente
    contenido_inicio($i18n[95], '50');
    {
        texto('texto', $i18n[106], $cliente['nombre'], 'auto', 'clientes.php?a=ver&id='.$cliente['idcliente'], 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_clientes', 'title' => $i18n[310]));
        texto('texto', $i18n[50], $cliente['idcliente']);
        texto('texto', $i18n[107], $cliente['contacto']);
        texto('mail', $i18n[109], $cliente['mail']);
        texto('texto', $i18n[108], $cliente['telefonos']);
        texto('texto', $i18n[110], $cliente['domicilio']);
        texto('texto', $i18n[53], $cliente['localidad']);
    }
    contenido_fin();

    // Situación del servicio
    contenido_inicio($i18n[96], '50', false, false, $i18n[101]);
    {
        // Para utilizar después en las consultas, busco los ids de los comprobantes separados por coma listos para ubicar en un IN ()
        $ids_comprobantes = array_sql(consulta_sql("SELECT
            (SELECT GROUP_CONCAT(DISTINCT idventa SEPARATOR ',') FROM ventas WHERE tiporelacion = 'servicio' AND idrelacion = '$id' AND muevesaldo = '1') AS ventas,
            (SELECT GROUP_CONCAT(DISTINCT idcompra SEPARATOR ',') FROM compras WHERE tiporelacion = 'servicio' AND idrelacion = '$id' AND muevesaldo = '1') AS compras
            "));

        $sql = '';
        if ($ids_comprobantes['ventas'])
            $sql .= "SELECT ventas.idmoneda, monedas.simbolo, monedas.nombre
                FROM ventas
                    LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda
                WHERE idventa IN (".$ids_comprobantes['ventas'].")
                GROUP BY ventas.idmoneda";
        if ($ids_comprobantes['compras']) {
            if ($sql)
                $sql .= ' UNION ';
            $sql .= "SELECT compras.idmoneda, monedas.simbolo, monedas.nombre
                FROM compras
                    LEFT JOIN monedas ON compras.idmoneda = monedas.idmoneda
                WHERE idcompra IN (".$ids_comprobantes['compras'].")
                GROUP BY compras.idmoneda";
        }

        $monedas_sql = consulta_sql($sql);
        while ($moneda = array_sql($monedas_sql)) {

            // Agrego a un array temporal las consultas que tengo que hacer para el array situación tenga los facturas y pagados, sin que salte un warning y haciendo una sola consulta a la base de datos
            $temp_consultas = array();
            if ($ids_comprobantes['ventas']) {
                $temp_consultas[] = "(SELECT COALESCE(SUM(total), 0) FROM ventas
                    WHERE operacioninversa = 0
                    AND idventa IN (".$ids_comprobantes['ventas'].") AND idmoneda = '{$moneda['idmoneda']}') AS facturado_ventas";
                $temp_consultas[] = "(SELECT COALESCE(SUM(total), 0) FROM ventas
                    WHERE operacioninversa != 0
                    AND idventa IN (".$ids_comprobantes['ventas'].") AND idmoneda = '{$moneda['idmoneda']}') AS facturado_ventas_operacioninversa";
                $temp_consultas[] = "(SELECT COALESCE(SUM(saldo), 0) FROM ventas
                    JOIN saldos ON ventas.idventa = saldos.idrelacion AND saldos.tiporelacion = 'ventas'
                    AND saldos.idrelacion IN (".$ids_comprobantes['ventas'].") AND ventas.idmoneda = '{$moneda['idmoneda']}') AS saldo_ventas";
            }
            if ($ids_comprobantes['compras']) {
                $temp_consultas[] = "(SELECT COALESCE(SUM(total), 0) FROM compras
                    WHERE operacioninversa = 0
                    AND idcompra IN (".$ids_comprobantes['compras'].") AND idmoneda = '{$moneda['idmoneda']}') AS facturado_compras";
                $temp_consultas[] = "(SELECT COALESCE(SUM(total), 0) FROM compras
                    WHERE operacioninversa != 0
                    AND idcompra IN (".$ids_comprobantes['compras'].") AND idmoneda = '{$moneda['idmoneda']}') AS facturado_compras_operacioninversa";
                $temp_consultas[] = "(SELECT COALESCE(SUM(saldo), 0) FROM compras
                    JOIN saldos ON compras.idcompra = saldos.idrelacion AND saldos.tiporelacion = 'compras'
                    AND saldos.idrelacion IN (".$ids_comprobantes['compras'].") AND compras.idmoneda = '{$moneda['idmoneda']}') AS saldo_compras";
            }

            if (count($temp_consultas)) {
                // Si hay consultas las uno todas juntas
                $situacion = array_sql(consulta_sql("SELECT "
                    .implode(',', $temp_consultas)
                    ));

            } else {
                // Si no hay genero el array vacío
                $situacion = array(
                    'facturado_ventas' => redondeo(0),
                    'facturado_compras' => redondeo(0),
                    );
            }

            // El total de ventas
            texto('texto', $i18n[97].$moneda['nombre'], $moneda['simbolo'].' '
                .formatear_numero(redondeo($situacion['facturado_ventas'] - $situacion['facturado_ventas_operacioninversa']))
                .' ('.$i18n[100].$moneda['simbolo'].' '.formatear_numero($situacion['saldo_ventas']).')');

            // El total de compras
            texto('texto', $i18n[98].$moneda['nombre'], $moneda['simbolo'].' '
                .formatear_numero(redondeo($situacion['facturado_compras'] - $situacion['facturado_compras_operacioninversa']))
                .' ('.$i18n[100].$moneda['simbolo'].' '.formatear_numero($situacion['saldo_compras']).')');

            // El total del servicio
            $situacion['facturado_servicio'] = redondeo($situacion['facturado_ventas'] - $situacion['facturado_ventas_operacioninversa'] - $situacion['facturado_compras'] + $situacion['facturado_compras_operacioninversa']);
            $situacion['saldo_servicio'] = redondeo($situacion['saldo_ventas'] - $situacion['saldo_compras']);
            texto('texto', $i18n[99].$moneda['nombre'], $moneda['simbolo'].' '
                .formatear_numero(redondeo($situacion['facturado_servicio']))
                .' ('.$i18n[100].$moneda['simbolo'].' '.formatear_numero($situacion['saldo_servicio']).')');
        }

        // Comparación con la Fecha límite
        if ($servicio['fechafin'] == '0000-00-00 00:00:00'
            && $servicio['fechalimite'] != '0000-00-00 00:00:00') {
            // Calculo los días de diferencia con la fecha límite
            $situacion['fechalimite'] = floor((strtotime($servicio['fechalimite']) - time()) / (60 * 60 * 24));

            if ($situacion['fechalimite'] < 0)
                texto('texto', $i18n[172], -1 * $situacion['fechalimite'].$i18n[174]);
            elseif ($situacion['fechalimite'] > 0)
                texto('texto', $i18n[173], $situacion['fechalimite'].$i18n[174]);
        }
        // Avance
        $situacion['realizado'] = porcentaje_realizado($servicio['tiempoestimado'], $servicio['tiempodedicado']);;
        if ($situacion['realizado'] !== false) {
            texto('porcentaje', $i18n[175], $situacion['realizado']);
        }


    }
    contenido_fin();

    salto_linea();

    extras_ver();

    // Contenido de la observacion
    contenido_inicio($i18n[114]);
    {
        texto('texto', $i18n[236], $servicio['titulo'], '100');

        if ($servicio['obssolicitado']) {
            observacion($i18n[115], $servicio['obssolicitado']);
        }

        if ($servicio['obsrealizado']) {
            observacion($i18n[116], $servicio['obsrealizado']);
        }

        if ($servicio['obsinterna']) {
            observacion($i18n[117], $servicio['obsinterna']);
        }
    }
    contenido_fin();
}
ventana_fin();
