<?php
$datos = array_sql(consulta_sql(
    "SELECT mensajes.*,
        usuarios.nombre AS remitente
    FROM mensajes
        LEFT JOIN usuarios ON mensajes.idremitente = usuarios.idusuario
    WHERE idmensaje = '$id'"));
consulta_sql("UPDATE mensajes SET visto = 1 WHERE idmensaje = '$id'");

$botones = array();
if ($datos['tipo'] == 'Personal' || $datos['tipo'] == 'Recordatorio')
    $botones[] = array('url' => 'mensajes.php?a=mod&id='.$id, 'a' => 'mod', 'title' => $i18n[30]);
$botones[] = array('url' => 'mensajes.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[34], 'opciones' => 'onclick="return confirma('."'$i18n[56]'".')"');
$botones[] = array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[38]);

ventana_inicio($i18n[41], '100', $botones);
contenido_inicio();
{
    texto('texto', $i18n[49], $datos['tipo'], '33');
    if ($datos['idremitente'])
        texto('texto', $i18n[44], $datos['remitente'], '33');
    texto('fechayhora', $i18n[55], $datos['fecha'], '33');
    salto_linea();
    observacion($i18n[48], formatearMontoTexto($datos['texto']));
}
contenido_fin();
ventana_fin();

// Si es el último mensaje sin leer cambio ícono de notificaciones pendientes
if (in_array($datos['tipo'], ['Notificacion', 'Consulta', 'Informacion'])) {
    $notificaciones_sin_leer = campo_sql(consulta_sql("SELECT COUNT(*) FROM mensajes WHERE tipo IN ('Notificacion', 'Consulta', 'Informacion') AND idusuario = '{$_SESSION['usuario_idusuario']}' AND visto = 0"));
    if ($notificaciones_sin_leer) {
        $title_notificaciones = $notificaciones_sin_leer == 1
            ? $i18n_funciones[307]
            : str_replace('{notificaciones_sin_leer}', $notificaciones_sin_leer, $i18n_funciones[305]);
        echo '
        <script>
        $("#icono_notificaciones").replaceWith(\'<a id="icono_notificaciones" href="javascript:void(0)" onclick="modal(\\\'mensajes\\\', \\\'mensajes_notificaciones\\\', \\\'\\\', \\\'\\\'); return false;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/notificaciones_alerta.png" title="'.$title_notificaciones.'"></a>\')
        </script>';

    } else {
        echo '
        <script>
        $("#icono_notificaciones").replaceWith(\'<a id="icono_notificaciones" href="javascript:void(0)" onclick="modal(\\\'mensajes\\\', \\\'mensajes_notificaciones\\\', \\\'\\\', \\\'\\\'); return false;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/notificaciones.png" title="'.$i18n_funciones[304].'"></a>\')
        </script>';
    }
}
