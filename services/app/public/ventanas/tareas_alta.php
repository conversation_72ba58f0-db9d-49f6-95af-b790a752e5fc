<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('largo', 'texto'),
        array('texto', 'prioridad'),
        array('entero', 'idusuario')
    ));
} else {
    $datos = array('idusuario' => $_SESSION['usuario_idusuario'], 'prioridad' => '3', 'texto' => '');
}

switch ($boton) {
    case $i18n[24]: //Agregar
        if ($datos['idusuario'] == 0 && $_SESSION['perfil_tareas_alta_todos']) {
            $resultado_sql = consulta_sql("SELECT idusuario FROM usuarios WHERE estado='1'");
            while ($temp_array = array_sql($resultado_sql)) {
                if ($sql)
                    $sql.= ", ";
                else
                    $sql = "INSERT INTO tareas (idusuario, prioridad, texto) VALUES ";
                $sql.= "('".$temp_array['idusuario']."', '".$datos['prioridad']."', '".$datos['texto']."')";
            }
        } else {
            $sql.= "INSERT INTO tareas (idusuario, prioridad, texto) VALUES ('".$datos['idusuario']."', '".$datos['prioridad']."', '".$datos['texto']."')";
        }
        consulta_sql($sql);
        ir_inicio();
        break;

    case $i18n[29]: //Cancelar
        ir_atras();
        break;
}

ventana_inicio($i18n[30]);
{
    contenido_inicio($i18n[32]);
    {
        if ($_SESSION['perfil_tareas_alta_todos']) {
            selector('idusuario', $i18n[34], $datos['idusuario'], '33', 'usuarios', 'nombre', false, $i18n[42], true);
        } else {
            selector_array('idusuario', $i18n[34], false, '33', array('id' => $_SESSION['usuario_idusuario'], 'valor' => $_SESSION['usuario_nombre'], 'selected' => 'selected'));
        }
        selector_array('prioridad', $i18n[35], $datos['prioridad'], '33', array(array('id' => '1', 'valor' => $i18n[36]), array('id' => '2', 'valor' => $i18n[37]), array('id' => '3', 'valor' => $i18n[38]), array('id' => '4', 'valor' => $i18n[39]), array('id' => '5', 'valor' => $i18n[40])));
        salto_linea();
        area('texto', $i18n[33], $datos['texto']);
    }
    contenido_fin();
    botones(array(array('valor' => $i18n[24]), array('valor' => $i18n[29])));
}
ventana_fin();
