<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('entero', 'idusuario'),
        array('entero', 'idtiposervicio'),
        array('texto', 'prioridad'),
        array('texto', 'titulo'),
        array('fecha', 'fechasolicitado'),
        array('fecha', 'fechainicio'),
        array('fecha', 'fechafin'),
        array('fecha', 'fechalimite'),
        array('tiempo', 'tiempoestimado'),
        array('tiempo', 'tiempodedicado'),
        array('largo', 'obssolicitado'),
        array('largo', 'obsrealizado'),
        array('largo', 'obsinterna'),
        array('tiempo', 'contartiempo')
    ));
    $resultado_sql = consulta_sql("SELECT estado, idcliente FROM servicios WHERE idservicio = '$id' LIMIT 1");
    $datos['estado'] = campo_sql($resultado_sql, 0, 'estado');
    $datos['idcliente'] = campo_sql($resultado_sql, 0, 'idcliente');

} else {
    $datos = array_sql(consulta_sql("SELECT idcliente, estado, idusuario, idtiposervicio, prioridad, titulo, fechasolicitado, fechainicio, fechafin, fechalimite, tiempoestimado, tiempodedicado, obssolicitado, obsrealizado, obsinterna, contartiempo FROM servicios WHERE idservicio='".$id."' LIMIT 1"));
}

switch ($boton) {
    case $i18n[31]: // Aplicar
    case $i18n[83]: // Aceptar
        switch ($datos['estado']) {
            case '1': // Sin comenzar
            case '2': // Comenzado
                if ($datos['fechafin']) { // Terminado
                    $estado =  '3';
                    $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
                    $fechafin = fecha_sql($datos['fechafin']);
                } elseif ($datos['fechainicio'] || $datos['obsrealizado'] || $datos['tiempodedicado'] != '00:00:00') { // Comenzado
                    $estado =  '2';
                    $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
                    $fechafin = '';
                } else { // Sin comenzar
                    $estado =  '1';
                    $fechainicio = '';
                    $fechafin = '';
                }
                break;

            case '3': // Terminado
            case '5': // Anulado
                if ($datos['fechafin']) { // Nada
                    $estado =  $datos['estado'];
                    $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
                    $fechafin = ($datos['fechafin']) ? fecha_sql($datos['fechafin']) : fecha_sql('ahora');
                } elseif ($datos['fechainicio'] || $datos['obsrealizado']) { // Comenzado
                    $estado =  '2';
                    $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
                    $fechafin = '';
                } else { // Sin comenzar
                    $estado =  '1';
                    $fechainicio = '';
                    $fechafin = '';
                }
                break;

            case '4': // Pausado
                if ($datos['fechafin']) { // Terminado
                    $estado =  '3';
                    $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
                    $fechafin = ($datos['fechafin']) ? fecha_sql($datos['fechafin']) : fecha_sql('ahora');
                } elseif ($datos['fechainicio'] || $datos['obsrealizado']) { // Comenzado
                    $estado =  '4';
                    $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
                    $fechafin = '';
                } else { // Sin comenzar
                    $estado =  '1';
                    $fechainicio = '';
                    $fechafin = '';
                }
                break;
        }
        break;

    case $i18n[17]: // Comenzar
        $estado = '2';
        $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
        $fechafin = '';
        break;

    case $i18n[85]: // Terminar
        $estado = '3';
        $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
        $fechafin = ($datos['fechafin']) ? fecha_sql($datos['fechafin']) : fecha_sql('ahora');
        break;

    case $i18n[87]: // Anular
        $estado = '5';
        $fechainicio = fecha_sql($datos['fechainicio']);
        $fechafin = ($datos['fechafin']) ? fecha_sql($datos['fechafin']) : fecha_sql('ahora');
        break;

    case $i18n[89]: // Pausar
        $estado = '4';
        $fechainicio = fecha_sql($datos['fechainicio']);
        $fechafin = fecha_sql($datos['fechafin']);
        break;

    case $i18n[91]: // Continuar
        $estado = '2';
        $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
        $fechafin = fecha_sql($datos['fechafin']);
        break;

    case $i18n[231]: // Re-abrir
        $estado = '2';
        $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
        $fechafin = '';
        break;

    case $i18n[92]: // Cancelar
        ir_atras();
        break;
}

if ($boton != '') {
    consulta_sql("UPDATE servicios SET
            prioridad = '".$datos['prioridad']."',
            titulo = '".$datos['titulo']."',
            idusuario = '".$datos['idusuario']."',
            idtiposervicio = '".$datos['idtiposervicio']."',
            fechasolicitado = '".fecha_sql($datos['fechasolicitado'])."',
            fechainicio = '".$fechainicio."',
            fechafin = '".$fechafin."',
            estado = '".$estado."',
            obssolicitado = '".$datos['obssolicitado']."',
            obsrealizado = '".$datos['obsrealizado']."',
            obsinterna = '".$datos['obsinterna']."',
            fechalimite = '".fecha_sql($datos['fechalimite'])."',
            tiempoestimado = '".$datos['tiempoestimado']."',
            tiempodedicado = '".$datos['tiempodedicado']."',
            contartiempo = '".$datos['contartiempo']."'
        WHERE idservicio='".$id."'
        LIMIT 1");
    fullsearch_update('servicios',$id);
    extras_mod();
    if ($boton != $i18n[31]) {
        ir_ahora('servicios.php?a=ver&id='.$id);
    }
}

$cliente = array_sql(consulta_sql(
    "SELECT idcliente, clientes.nombre, contacto, domicilio, telefonos, mail,
        obsrecordatorio, clientes.observacion,
        categorias_localidades.nombre AS localidad
    FROM clientes
        LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
    WHERE idcliente = '".$datos['idcliente']."'
    LIMIT 1"));

obsrecordatorio($cliente['obsrecordatorio']);

// Preparamos el boton de eliminacion
if ($_SESSION['perfil_servicios_baja'] && ($datos['idusuario'] == $_SESSION['usuario_idusuario'] || $_SESSION['perfil_servicios_mod_todos'])) {
    $temp_boton_baja = array('tipo' => 'ajax', 'url' => 'servicios.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[230], 'opciones' => 'onclick="return confirma('."'$i18n[249]'".')"');
} else {
    $temp_boton_baja = array('a' => 'baja_no', 'title' => $i18n[227]);
}

if ($boton == $i18n[31]) {
    $datos = array_sql(consulta_sql("SELECT * FROM servicios WHERE idservicio='".$id."' LIMIT 1"));
}

ventana_inicio($i18n[93].$id, '100', array(
    array('url' => 'servicios.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[90]),
    $temp_boton_baja,
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[228])));
{
    // Cliente
    contenido_inicio($i18n[95].': '.$cliente['nombre'], '100', true, false);
    {
        texto('texto', $i18n[50], $cliente['idcliente']);
        texto('texto', $i18n[107], $cliente['contacto']);
        texto('mail', $i18n[109], $cliente['mail']);
        texto('texto', $i18n[108], $cliente['telefonos']);
        texto('texto', $i18n[110], $cliente['domicilio']);
        texto('texto', $i18n[53], $cliente['localidad']);

        if ($cliente['observacion']) {
            observacion($i18n[178], $cliente['observacion']);
        }
        if ($cliente['obsinterna']) {
            observacion($i18n[177], $cliente['obsinterna']);
        }
        if ($cliente['obsrecordatorio']) {
            observacion($i18n[176], $cliente['obsrecordatorio']);
        }
    }
    contenido_fin();

    //Datos basicos
    contenido_inicio($i18n[94]);
    {
        texto('texto', $i18n[170], $i18n['estado_'.$datos['estado']], 'auto', false, 'estado_'.$datos['estado']);
        selector_array('prioridad', $i18n[37], $datos['prioridad'], '33', array(array('id' => '1', 'valor' => $i18n['prioridad_1']), array('id' => '2', 'valor' => $i18n['prioridad_2']), array('id' => '3', 'valor' => $i18n['prioridad_3']), array('id' => '4', 'valor' => $i18n['prioridad_4']), array('id' => '5', 'valor' => $i18n['prioridad_5'])));
        // Calculamos la lista de usuarios
        if ($_SESSION['perfil_servicios_alta_todos']) {
            selector('idusuario', $i18n[102], $datos['idusuario'], '33', 'usuarios', 'nombre', false, true, true);
        } else {
            selector_array('idusuario', $i18n[102], $datos['idusuario'], '33', array(array('id' => $_SESSION['usuario_idusuario'], 'valor' => $_SESSION['usuario_nombre'])));
        }
        selector_familiar('idtiposervicio', $i18n[103], $datos['idtiposervicio'], '33', 'categorias_servicios', true, true, true);
    }
    contenido_fin();

    //Fechas y tiempos
    contenido_inicio($i18n[120]);
    {
        entrada('fechayhora', 'fechasolicitado', $i18n[104], $datos['fechasolicitado'], '14');
        entrada('fechayhora', 'fechainicio', $i18n[15], $datos['fechainicio'], '14');
        entrada('fechayhora', 'fechafin', $i18n[105], $datos['fechafin'], '14');
        entrada('fechayhora', 'fechalimite', $i18n[233], $datos['fechalimite'], '14');
        entrada('tiempo', 'tiempoestimado', $i18n[234], $datos['tiempoestimado'], '14');
        entrada('tiempo', 'tiempodedicado', $i18n[235], $datos['tiempodedicado'], '14');
        marcas(' ', '15', array(array('nombre' => 'contartiempo', 'titulo' => $i18n[248], 'valor' => $datos['contartiempo'])));
    }
    contenido_fin();

    extras();

    // Contenido de la observacion
    contenido_inicio($i18n[114], '100');
    {
        entrada('texto', 'titulo', $i18n[236], $datos['titulo'], 'auto', '200');
        area('obssolicitado', $i18n[115], $datos['obssolicitado']);
        area('obsrealizado', $i18n[116], $datos['obsrealizado']);
        area('obsinterna', $i18n[117], $datos['obsinterna']);
    }
    contenido_fin();

    // Calculamos los botones segun el estado del servicio
    switch ($datos['estado']) {
        // default:
        case 1: // Sin comenzar
            botones(array(
                array('valor' => $i18n[31]), // Aplicar
                array('valor' => $i18n[83]), // Aceptar
                array('valor' => $i18n[92], 'opciones' => 'style="margin-right: 10px;"'), // Cancelar
                array('valor' => $i18n[17]), // Comenzar
                array('valor' => $i18n[85]), // Terminar
                array('valor' => $i18n[87]), // Anular
                array('valor' => $i18n[89]) // Pausar
            ));
            break;

        case 2: // Comenzado
            botones(array(
                array('valor' => $i18n[31]), // Aplicar
                array('valor' => $i18n[83]), // Aceptar
                array('valor' => $i18n[92], 'opciones' => 'style="margin-right: 10px;"'), // Cancelar
                array('valor' => $i18n[85]), // Terminar
                array('valor' => $i18n[87]), // Anular
                array('valor' => $i18n[89]) // Pausar
            ));
            break;

        case 3: // Terminado
            botones(array(
                array('valor' => $i18n[31]), // Aplicar
                array('valor' => $i18n[83]), // Aceptar
                array('valor' => $i18n[92], 'opciones' => 'style="margin-right: 10px;"'), // Cancelar
                array('valor' => $i18n[231]) // Reabrir
            ));
            break;

        case 4: // Pausado
            botones(array(
                array('valor' => $i18n[31]), // Aplicar
                array('valor' => $i18n[83]), // Aceptar
                array('valor' => $i18n[92], 'opciones' => 'style="margin-right: 10px;"'), // Cancelar
                array('valor' => $i18n[85]), // Terminar
                array('valor' => $i18n[87]), // Anular
                array('valor' => $i18n[91]) // Continuar
            ));
            break;

        case 5: // Anulado
            botones(array(
                array('valor' => $i18n[31]), // Aplicar
                array('valor' => $i18n[83]), // Aceptar
                array('valor' => $i18n[92], 'opciones' => 'style="margin-right: 10px;"'), // Cancelar
                array('valor' => $i18n[231]) // Reabrir
            ));
            break;
    }
}
ventana_fin();
?>
<script type="text/javascript" charset="utf-8">
    $(function() {
        if ($("input[name='contartiempo']").attr('checked')) {
            contartiempo(true);
        }
        $("input[name='contartiempo']").click(function() {
            if ($(this).attr('checked')) {
                contartiempo(true);
            } else {
                contartiempo(false);
            }
        });
    });
</script>
