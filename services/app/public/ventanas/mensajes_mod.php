<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('largo', 'texto'),
        array('texto', 'tipo'),
        array('entero', 'idusuario'),
        array('fecha', 'fecha'),
        array('texto', 'destacado')
    ));
} else {
    $datos = array_sql(consulta_sql("SELECT * FROM mensajes WHERE idmensaje = '$id'"));
    if ($datos['tipo'] == $i18n[51]) {
        $datos['idusuario'] = $datos['idremitente'];
    }
}

switch ($boton) {
    case $i18n[42]: //Aceptar
        $visto = contar_sql(consulta_sql(
            "SELECT idmensaje FROM mensajes
            WHERE idmensaje = '$id' AND fecha >= '".fecha_sql($datos['fecha'])."' AND idusuario = '".$datos['idusuario']."'"));
        consulta_sql("UPDATE mensajes SET
                idusuario = '".$datos['idusuario']."',
                idremitente = '".$_SESSION['usuario_idusuario']."',
                tipo = '".$datos['tipo']."',
                fecha = '".fecha_sql($datos['fecha'])."',
                texto = '".$datos['texto']."',
                destacado = '".$datos['destacado']."',
                visto = '$visto'
            WHERE idmensaje = '$id'");
        ir_inicio();
        break;

    case $i18n[43]: //Cancelar
        ir_inicio();
        break;
}

ventana_inicio($i18n[46], '100', array(
    array('url' => 'mensajes.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[37]),
    array('url' => 'mensajes.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[34], 'opciones' => 'onclick="return confirma('."'$i18n[56]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[38])));
{
    contenido_inicio();
    {
        selector_array('tipo', $i18n[49], $datos['tipo'], '33', array(array('id' => 'Personal', 'valor' => $i18n[51]), array('id' => 'Recordatorio', 'valor' => $i18n[50])));
        selector('idusuario', $i18n[53], $datos['idusuario'], '33', 'usuarios', 'nombre', false, false, true);
        entrada('fechayhora', 'fecha', $i18n[55], $datos['fecha'], '20');
        marcas($i18n[76], '15', array(
            array('nombre' => 'destacado', 'valor' => $datos['destacado']),
        ));
        salto_linea();
        area('texto', $i18n[48], $datos['texto']);
    }
    contenido_fin();
    botones(array(array('valor' => $i18n[42]), array('valor' => $i18n[43])));
}
ventana_fin();
