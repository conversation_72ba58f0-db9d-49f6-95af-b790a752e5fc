<?php

switch ($boton) {

    case $i18n[65]: //Aceptar
        $datos = recibir_matriz(array(
        array('cantidad', 'muevestock'),
        array('texto', 'muevesaldo'),
        array('texto', 'tienesituacion'),
        array('texto', 'situacion')
    ));

        consulta_sql(
            "UPDATE categorias_compras SET
                muevestock = '".$datos['muevestock']."',
                muevesaldo = '".$datos['muevesaldo']."',
                tienesituacion = '".$datos['tienesituacion']."',
                situacion = '".$datos['situacion']."'
            WHERE idtipocompra = '".$id."'
            LIMIT 1");

        ir_atras();
        break;

    case $i18n[66]: //Cancelar
        ir_atras();
        break;

    default:
        break;

}

//Muevo esto acá porque on submit y validar con errores, me quedan los $datos del form
$datos = array_sql(consulta_sql(
    "SELECT * FROM categorias_compras WHERE idtipocompra = $id"));

mensajes_efimeros();

ventana_inicio($i18n[675].$datos['nombre']);
{
    // Datos básicos
    contenido_inicio($i18n[89], '33');
    {
        entrada('texto', 'nombre', $i18n[180], $datos['nombre'], '70', '60', false, 'disabled="disabled"');
    }
    contenido_fin();

    // Comportamiento
    contenido_inicio($i18n[226], '33');
    {
        marcas('', 'auto', array(
            array('nombre' => 'muevestock', 'titulo' => $i18n[194], 'valor' => $datos['muevestock'], 'opciones' => 'id="muevestock"'),
            array('nombre' => 'muevesaldo', 'titulo' => $i18n[195], 'valor' => $datos['muevesaldo'], 'opciones' => 'id="muevesaldo"'),
        ));
    }
    contenido_fin();

    // Situación
    contenido_inicio($i18n[460], '33');
    {
        marcas($i18n[149], 'auto', array(
            array('nombre' => 'tienesituacion', 'titulo' => $i18n[97], 'valor' => 1, 'opciones' => 'id="tienesituacion"'),
        ));

        bloque_inicio('situacion');
        {
            $situaciones = array(
                array('id' => 'sin_especificar', 'valor' => $i18n['sin_especificar']),
                array('id' => 'pendiente', 'valor' => $i18n['pendiente']),
                array('id' => 'aprobado', 'valor' => $i18n['aprobado']),
                array('id' => 'rechazado', 'valor' => $i18n['rechazado'])
            );
            selector_array('situacion', $i18n[146], $datos['situacion'], 'auto', $situaciones);
        }
        bloque_fin();
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[65]), array('valor' => $i18n[66])));
}
ventana_fin();

?>

<script>
    $("#tienesituacion").attr("disabled", true);
</script>