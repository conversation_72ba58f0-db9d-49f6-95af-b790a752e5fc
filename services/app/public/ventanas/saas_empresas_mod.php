<?php
switch ($boton) {

    case $i18n[1]: //Aceptar
        $datos = recibir_matriz(array(
        array('entero', 'estado'),
        array('entero', 'idsistema'),
        array('mail', 'mail'),
        array('entero', 'idcliente'),
        array('texto', 'ultimocontacto'),
        array('fecha', 'fechavencimiento'),
        array('fecha', 'fechadeshabilitada'),
        array('fecha', 'fechacontinuidad'),
        array('largo', 'observacion'),
        array('mail', 'mailbasedatos'),
        array('texto', 'razonsocial'),
        array('entero', 'idtipoiva'),
        array('cuit', 'cuit'),
        array('texto', 'emitir_impuestos'),
        array('entero', 'puntodeventa'),
        array('texto', 'abonos'),
        array('texto', 'espacio_archivos')
    ));
        $datos['cuit'] = str_replace('-', '', $datos['cuit']);

        if ($datos['estado'] == 'deshabilitada' && !$datos['fechadeshabilitada'])
            $datos['fechadeshabilitada'] = date('d-m-Y');
        else if ($datos['estado'] != 'deshabilitada')
            $datos['fechadeshabilitada'] = '0000-00-00';

            consulta_sql("UPDATE empresas SET
                estado = '" . $datos['estado'] . "',
                idsistema = '" . $datos['idsistema'] . "',
                mail = '" . $datos['mail'] . "',
                idcliente = '" . $datos['idcliente'] . "',
                cuit = '" . $datos['cuit'] . "',
                ultimocontacto = '" . $datos['ultimocontacto'] . "',
                fechavencimiento = '" . fecha_sql($datos['fechavencimiento']) . "',
                fechadeshabilitada = '" . fecha_sql($datos['fechadeshabilitada']) . "',
                fechacontinuidad = '" . fecha_sql($datos['fechacontinuidad']) . "',
                observacion = '" . escape_sql($datos['observacion']) . "'
            WHERE idempresa = '" . $id . "'
            LIMIT 1", 'saasargentina');

        consulta_sql("UPDATE configuraciones SET
                mail = '" . $datos['mailbasedatos'] . "',
                razonsocial = '" . $datos['razonsocial'] . "',
                cuit = '" . $datos['cuit'] . "',
                idtipoiva = '" . $datos['idtipoiva'] . "',
                emitir_impuestos = " . ($datos['emitir_impuestos'] ? $datos['emitir_impuestos'] : '0') . ",
                abonos = " . ($datos['abonos'] ? $datos['abonos'] : '0') . ",
                espacio_archivos = " . ($datos['espacio_archivos'] ? $datos['espacio_archivos'] : '0') . "
                LIMIT 1", $id);

        if ($datos['puntodeventa']) {
            //Copiado de configuraciones_wsfe_puntodeventa
            if (contar_sql(consulta_sql(
                "SELECT idtipoventa
                FROM categorias_ventas
                WHERE estado = '1'
                    AND tipofacturacion != 'interno'
                    AND puntodeventa = '".$datos['puntodeventa']."'
                LIMIT 1", $id))) {

                mensajes_alta($i18n[180]);

            } else {
                $configuracion_idtipoiva = campo_sql(consulta_sql("SELECT idtipoiva FROM configuraciones LIMIT 1", $id));
                if (contar_sql(consulta_sql(
                    "SELECT idtipoventa
                    FROM categorias_ventas
                    WHERE estado = '0'
                        AND tipofacturacion = 'electronico'
                        AND puntodeventa = '".$datos['puntodeventa']."'
                    LIMIT 1", $id))) {
                // Hay tipos de venta deshabilitados
                    $sql = "UPDATE categorias_ventas SET
                            estado = '1'
                        WHERE tipofacturacion = 'electronico'
                            AND puntodeventa = '".$datos['puntodeventa']."'";

                } elseif ($configuracion_idtipoiva == 1) {
                // Responsable Inscripto
                    $sql = "
                        INSERT INTO `categorias_ventas` (`idtipoventa`, `idcomportamiento`, `estado`, `nombre`, `letra`, `puntodeventa`, `ultimonumero`, `discrimina`, `muevestock`, `muevesaldo`, `operacioninversa`, `tipofacturacion`, `tipoimpresion`, `estilo_venta`, `observacion`, `obsinterna`) VALUES
                        (NULL, '1', '1', 'Factura Electrónica A', 'A', '".$datos['puntodeventa']."', '0', 'A', '1', '1', '0', 'electronico', 'predeterminado', '', '', ''),
                        (NULL, '2', '1', 'Nota de Débito Electrónica A', 'A', '".$datos['puntodeventa']."', '0', 'A', '1', '1', '0', 'electronico', 'predeterminado', '', '', ''),
                        (NULL, '3', '1', 'Nota de Crédito Electrónica A', 'A', '".$datos['puntodeventa']."', '0', 'A', '1', '1', '1', 'electronico', 'predeterminado', '', '', ''),
                        (NULL, '6', '1', 'Factura Electrónica B', 'B', '".$datos['puntodeventa']."', '0', 'B', '1', '1', '0', 'electronico', 'predeterminado', '', '', ''),
                        (NULL, '7', '1', 'Nota de Débito Electrónica B', 'B', '".$datos['puntodeventa']."', '0', 'B', '1', '1', '0', 'electronico', 'predeterminado', '', '', ''),
                        (NULL, '8', '1', 'Nota de Crédito Electrónica B', 'B', '".$datos['puntodeventa']."', '0', 'B', '1', '1', '1', 'electronico', 'predeterminado', '', '', '');";

                } else {
                // Monotributista o Exento
                    $sql = "
                        INSERT INTO `categorias_ventas` (`idtipoventa`, `idcomportamiento`, `estado`, `nombre`, `letra`, `puntodeventa`, `ultimonumero`, `discrimina`, `muevestock`, `muevesaldo`, `operacioninversa`, `tipofacturacion`, `tipoimpresion`, `estilo_venta`, `observacion`, `obsinterna`) VALUES
                        (NULL, '11', '1', 'Factura Electrónica C', 'C', '".$datos['puntodeventa']."', '0', 'C', '1', '1', '0', 'electronico', 'predeterminado', '', '', ''),
                        (NULL, '12', '1', 'Nota de Débito Electrónica C', 'C', '".$datos['puntodeventa']."', '0', 'C', '1', '1', '0', 'electronico', 'predeterminado', '', '', ''),
                        (NULL, '13', '1', 'Nota de Crédito Electrónica C', 'C', '".$datos['puntodeventa']."', '0', 'C', '1', '1', '1', 'electronico', 'predeterminado', '', '', '');";
                }
                consulta_sql($sql, $id);
            }
        }

        ir_atras();
        break;

    case $i18n[3]: //Cancelar
        ir_atras();
        break;

    default:
    $datos = array_sql(consulta_sql("SELECT
            e.idempresa, e.nombre, e.iue, e.mail, e.idcliente, e.idservidor, e.idsistema, e.estado, e.cuit, e.fechaalta, e.fechavencimiento, e.fechacontinuidad, e.fechadeshabilitada, e.fechaeliminada, e.ultimoacceso, e.ultimocontacto, e.observacion, e.soporte,
            r.idregistracion, r.idtipoiva, r.nombre AS nombre_registro, r.empresa, r.telefonos, r.mail AS mail_registro, r.localidad, r.fecha, r.landing,
            (SELECT nombre FROM sistemas WHERE sis.idsistema = r.idsistema LIMIT 1) AS sistema_registro,
            (SELECT COUNT(idusuario) FROM usuarios WHERE usuarios.idempresa = e.idempresa) AS usuarios,
            (SELECT COUNT(idusuario) FROM usuarios WHERE estado = '1' AND usuarios.idempresa = e.idempresa)-2 AS adicionales,
            ser.nombre AS servidor, ser.version AS version,
            sis.nombre AS sistema
        FROM empresas AS e
            LEFT JOIN servidores AS ser ON e.idservidor = ser.idservidor
            LEFT JOIN sistemas AS sis ON e.idsistema = sis.idsistema
            LEFT JOIN registraciones AS r ON e.idempresa = r.idempresa
        WHERE e.idempresa = '$id'
        LIMIT 1", 'saasargentina'));

    $datos['tickets'] = campo_sql(consulta_sql("SELECT COUNT(*) FROM saas_tickets WHERE idempresa='" . $datos['idempresa'] . "'"), 0);

    // Busco las configuraciones es la base de datos de la empresa
    if ($datos['estado'] != 'eliminada') {
        $configuraciones = array_sql(consulta_sql(
                        "SELECT telefonos, mail, domicilio, razonsocial, configuraciones.idtipoiva, cuit, emitir_impuestos, estilo_empresa, estilo_informes, estilo_ventas, estilo_ventas_pdf, estilo_logo, ext_logo, tablas_condiciones.nombre AS tipoiva, espacio_archivos, abonos, categorias_localidades.nombre AS localidad,
            (SELECT nombrereal FROM usuarios WHERE idusuario > 0 ORDER BY idusuario ASC LIMIT 1) AS usuario,
            (SELECT SUM(bytes) FROM archivos) AS espacio_utilizado,
            (SELECT COUNT(idmoneda) FROM monedas) AS monedas
            FROM configuraciones
            LEFT JOIN tablas_condiciones ON configuraciones.idtipoiva = tablas_condiciones.idtipoiva
            LEFT JOIN categorias_localidades ON configuraciones.idlocalidad = categorias_localidades.idlocalidad
            LIMIT 1", $datos['idempresa']));

        $resultado_sql = consulta_sql(
                "SELECT nombre, tipoimpresion, estilo_venta, estilo_venta_pdf
            FROM categorias_ventas
            WHERE tipoimpresion!='predeterminado'"
                , $datos['idempresa']);
        $estilosdeventas = array();
        while ($temp_array = array_sql($resultado_sql)) {
            $estilosdeventas[] = $temp_array;
        }

        $resultado_sql = consulta_sql(
                "SELECT idtipoventa, categorias_ventas.nombre, letra, puntodeventa, ultimonumero, categorias_ventas.discrimina, categorias_ventas.muevestock, categorias_ventas.muevesaldo, categorias_ventas.operacioninversa, tienesituacion, situacion, auto_aprobar, tipofacturacion, tipoimpresion,
                tablas_comportamientos.nombre AS comportamiento
            FROM categorias_ventas
                LEFT JOIN tablas_comportamientos ON categorias_ventas.idcomportamiento = tablas_comportamientos.idcomportamiento
            WHERE estado = '1'
                ORDER BY categorias_ventas.idcomportamiento"
                , $datos['idempresa']);
        $tiposdeventas = array();
        while ($temp_array = array_sql($resultado_sql)) {
            $tiposdeventas[] = $temp_array;
        }

        $datos['ML_cuentas'] = campo_sql(consulta_sql("SELECT COUNT(idtienda) FROM tienda WHERE ML_estado != '' AND ML_user_id > 0", $datos['idempresa']));

    }

    // Busco los datos en el archivo de logs
    $archivo_log = PATH_LOGS.'empresa_' . $datos['idempresa'] . '.csv';
    if (!file_exists($archivo_log)) {
        $datos_log['cantidad'] = 'No tiene log';

    } else if (filesize($archivo_log) > 1000000) {
        $datos_log['cantidad'] = 'Archivo de log muy grande';

    } else {
        $log = file(PATH_LOGS.'empresa_' . $datos['idempresa'] . '.csv');
        $datos_log = array('cantidad' => count($log));
        $datos_log['primero'] = mb_substr($log[0], 0, 16);
        $datos_log['ultimo'] = mb_substr($log[$datos_log['cantidad'] - 1], 0, 16);
    }

    $resultado_sql = consulta_sql("SELECT idsistema, nombre FROM sistemas", 'saasargentina');
    $sistemas = array();
    while ($temp_array = array_sql($resultado_sql)) {
        $sistemas[] = array('id' => $temp_array['idsistema'], 'valor' => $temp_array['nombre']);
    }

}

ventana_inicio($i18n[45] . $datos['nombre'], '100', array(array('url' => 'saas.php?a=verempresa&id=' . $id, 'a' => 'ver', 'title' => $i18n[31])));
{
    // Datos de la empresa
    contenido_inicio($i18n[49], '50');
    {
        texto('texto', $i18n[44], $datos['idempresa']);
        $estados = array(
            array('id' => 'prueba', 'valor' => $i18n['prueba']),
            array('id' => 'pruebavencida', 'valor' => $i18n['pruebavencida']),
            array('id' => 'activada', 'valor' => $i18n['activada']),
            array('id' => 'vencida', 'valor' => $i18n['vencida']),
            array('id' => 'deshabilitada', 'valor' => $i18n['deshabilitada']),
            array('id' => 'eliminada', 'valor' => $i18n['eliminada']),
            array('id' => 'demo', 'valor' => $i18n['demo']),
        );
        texto('texto', $i18n[35], $datos['servidor']);
        selector_array('estado', $i18n[13], $datos['estado'], 'auto', $estados);
        selector_array('idsistema', $i18n[29], $datos['idsistema'], 'auto', $sistemas);
        entrada('texto', 'mail', $i18n[102], $datos['mail'], 'auto', '320');
        texto('texto', $i18n[28], $datos['nombre']);
        texto('texto', $i18n[21], $configuraciones['usuario']);
        entrada('numeros', 'idcliente', $i18n[43], $datos['idcliente']);
        texto('cuit', $i18n[41], $datos['cuit']);

        texto('fechayhora', $i18n[74], $datos['fechaalta']);
        entrada('fecha', 'fechavencimiento', $i18n[91], $datos['fechavencimiento']);
        entrada('fechayhora', 'fechacontinuidad', $i18n[75], $datos['fechacontinuidad']);
        $ultimocontacto = array(
            array('id' => '', 'valor' => ''),
            array('id' => 'encuesta', 'valor' => 'Encuesta'),
            array('id' => 'continuidad', 'valor' => 'Continuidad'),
            array('id' => 'extension', 'valor' => 'Extensión'),
        );
        entrada('fecha', 'fechadeshabilitada', $i18n[236], $datos['fechadeshabilitada']);
        texto('fechayhora', $i18n[234], $datos['fechaeliminada']);
        texto('fechayhora', $i18n[235], $datos['ultimoacceso']);
        selector_array('ultimocontacto', $i18n[76], $datos['ultimocontacto'], 'auto', $ultimocontacto);

        texto('texto', $i18n[34], $datos['iue']);
        if ($datos['tienda']) {
            texto('texto', $i18n[73], $datos['tienda'], 'auto', ($datos['tienda'] ? URL_TIENDA . '/' . $datos['tienda'] : ''));
        } else {
            texto('texto', $i18n[73], $i18n_funciones[24]);
        }
        texto('texto', $i18n[181], ($datos['soporte'] ? $datos['soporte'] : $i18n['deshabilitado']));

    }
    contenido_fin();

    // Datos de su base de datos
    contenido_inicio($i18n[50], '50');
    {
        if ($datos['estado'] != 'eliminada') {
            texto('texto', $i18n[36], $configuraciones['telefonos']);
            entrada('texto', 'mailbasedatos', $i18n[30], $configuraciones['mail'], 'auto', '320');
            texto('texto', $i18n[37], $configuraciones['domicilio']);
            texto('texto', $i18n[38], $configuraciones['localidad']);
            entrada('texto', 'razonsocial', $i18n[39], $configuraciones['razonsocial'], 'auto', '320');
            selector('idtipoiva', $i18n[40], $configuraciones['idtipoiva'], 'auto', 'tablas_condiciones', 'nombre');
//            texto('texto', $i18n[40], $configuraciones['tipoiva']);
            entrada('cuit', 'cuit', $i18n[41], $configuraciones['cuit'], 'auto', '320');
        } else {
            texto('italica', '', $i18n[92]);
        }
    }
    contenido_fin();

    if ($datos['estado'] != 'eliminada') {

        // Lista de usuarios
        contenido_inicio($i18n[109], '50');
        {
            $datos['usuarios_empresa'] = consulta_sql("SELECT idusuario, nombrereal, mail, estado FROM usuarios WHERE idusuario > 0", $datos['idempresa']);

            while ($usuario = array_sql($datos['usuarios_empresa'])) {
                if (in_array($datos['estado'], array('prueba', 'activada')) && $usuario['estado']) {
                    texto('texto', $usuario['idusuario'] . ' - ' . $usuario['nombrereal'] . ' (' . $usuario['mail'] . ')', $i18n[124], 'auto', 'saas.php?a=altaticket&idusuario=' . $usuario['idusuario']);
                } else {
                    //texto('texto', $usuario['nombrereal'] . ' (' . $usuario['mail'] . ')', $i18n[125],'auto', 'mailto:' . $usuario['mail']);
                    enlaces($usuario['idusuario'] . ' - ' . $usuario['nombrereal'] . ' (' . $usuario['mail'] . ')', [
                        [
                        'tipo' => 'ajax',
                        'valor' => $i18n[183],
                        'id' => $datos['idempresa'].'|'.$usuario['idusuario'],
                        'url' => 'usuarios_reactivar',
                        'confirma' => $i18n[185]
                        ]
                    ]);
                }
            }
        }
        contenido_fin();

        // Funcionalidades adicionales
        contenido_inicio($i18n[218], '50');
        {
            texto('texto', $i18n[219], ($datos['adicionales'] ? $datos['adicionales'] : '0 '));
            texto('texto', $i18n[220], ($datos['ML_cuentas'] ? $datos['ML_cuentas'] : '0 '));
            texto('texto', $i18n[221], round($configuraciones['espacio_utilizado'] / 1024 / 1024 / 1024, 4).$i18n[226].$i18n[232]);
            entrada('texto', 'espacio_archivos', false, $configuraciones['espacio_archivos'], '10', '3');
            marcas('', 'auto', array(array('nombre' => 'emitir_impuestos', 'valor' => $configuraciones['emitir_impuestos'], 'titulo' => $i18n[129])));
            marcas('', 'auto', array(array('nombre' => 'abonos', 'valor' => $configuraciones['abonos'], 'titulo' => $i18n[223])));
        }
        contenido_fin();

    }

    salto_linea();

    // Datos de la registración
    contenido_inicio($i18n[60], '50');
    {
        texto('texto', $i18n[79], $datos['idregistracion']);
        texto('texto', $i18n[29], $datos['sistema_registro']);
        texto('texto', $i18n[40], $datos['idtipoiva']);
        texto('texto', $i18n[21], $datos['nombre_registro']);
        texto('texto', $i18n[18], $datos['empresa']);
        texto('texto', $i18n[80], $datos['telefonos']);
        texto('texto', $i18n[30], $datos['mail_registro']);
        texto('texto', $i18n[38], $datos['localidad']);
        texto('fecha', $i18n[42], $datos['fecha']);
        texto('texto', $i18n[63], $datos['landing']);
    }
    contenido_fin();

    if ($datos['estado'] != 'eliminada') {

        // Datos de logs
        contenido_inicio($i18n[51], '50');
        {
            texto('texto', $i18n[46], $datos['usuarios']);
            texto('texto', $i18n[48], $datos['adicionales']);
            texto('texto', $i18n[8], ($datos['tickets'] ? $datos['tickets'] : '0 '));
            texto('texto', $i18n[54], ($datos_log['cantidad'] ? $datos_log['cantidad'] : $i18n[83]));
            texto('texto', $i18n[52], $datos_log['primero']);
            texto('texto', $i18n[53], $datos_log['ultimo']);
        }
        contenido_fin();

        salto_linea();

        // Datos de estilos
        contenido_inicio($i18n[93], '50', true, false);
        {
            $configuraciones['estilo_logo'] ? texto('texto', $i18n[72], $configuraciones['estilo_logo'], 'auto', URL_S3 . $configuraciones['estilo_logo'] . '/estilo_logo.'.$_SESSION['configuracion_ext_logo']) : texto('texto', $i18n[72], $i18n_funciones[24]);

            $configuraciones['estilo_empresa'] ? texto('texto', $i18n[70], $configuraciones['estilo_empresa'], 'auto', URL_S3 . $configuraciones['estilo_empresa'] . '/estilo_empresa.css') : texto('texto', $i18n[70], $i18n_funciones[24]);

            $configuraciones['estilo_informes'] ? texto('texto', $i18n[71], $configuraciones['estilo_informes'], 'auto', URL_S3 . $configuraciones['estilo_informes'] . '/estilo_informes.css') : texto('texto', $i18n[71], $i18n_funciones[24]);

            $configuraciones['estilo_ventas'] ? texto('texto', $i18n[77], $configuraciones['estilo_ventas'], 'auto', URL_S3 . $configuraciones['estilo_ventas'] . '/estilo_ventas.css') : texto('texto', $i18n[77], $i18n_funciones[24]);

            $configuraciones['estilo_ventas_pdf'] ? texto('texto', $i18n[78], $configuraciones['estilo_ventas_pdf'], 'auto', URL_S3 . $configuraciones['estilo_ventas_pdf'] . '/estilo_ventas_pdf.css') : texto('texto', $i18n[78], $i18n_funciones[24]);

            foreach ($estilosdeventas as $estiloventa) {
                if ($estiloventa['tipoimpresion'] == 'avanzado') {
                    texto('texto', $i18n[85] . $estiloventa['nombre'], $estiloventa['estilo_venta'], 'auto', URL_S3 . $estiloventa['estilo_venta'] . '/estilo_venta.css');
                    texto('texto', $i18n[86] . $estiloventa['nombre'], $estiloventa['estilo_venta_pdf'], 'auto', URL_S3 . $estiloventa['estilo_venta_pdf'] . '/estilo_venta_pdf.css');
                } else {
                    texto('texto', $i18n[87] . $estiloventa['nombre'], $estiloventa['tipoimpresion']);
                }
            }
        }
        contenido_fin();

        // Tipos de ventas
        contenido_inicio($i18n[97], '50', true, false);
        {
            enlaces(false, array(array(
                'tipo'      => 'enlace',
                'valor'     => $i18n[176],
                'imagen'    => 'descargar',
                'url'       => 'configuraciones.php?a=exportar_req&cuit='.$configuraciones['cuit'].'&razonsocial='.$configuraciones['razonsocial'],
                'opciones'  => 'id="exportar_req"'
            )));

            if (!existe_wsfe($configuraciones['cuit'], 'crt')) {
                enlaces(false, array(array(
                    'tipo'      => 'modal',
                    'url'       => 'configuraciones_estilos_alta',
                    'id'        => 'wsfe_crt_empresa',
                    'valor'     => $i18n[177],
                )));

            } elseif (existe_wsfe($configuraciones['cuit'], 'crt')) {
                enlaces(false, array(array(
                    'tipo'      => 'enlace',
                    'valor'     => $i18n[178],
                    'imagen'    => 'descargar',
                    'url'       => 'configuraciones.php?a=exportar_crt&cuit='.$configuraciones['cuit'],
                    'opciones'  => 'id="exportar_crt"'
                )));
            }
            entrada('hidden', 'idempresa', '', $id);
            entrada('cuit', 'puntodeventa', $i18n[179], '', '50', '50');

            texto('numeros', $i18n[94], $configuraciones['crt']);

            foreach ($tiposdeventas as $tipoventa) {
                $ayuda_puntual = 'idtipoventa ' . $tipoventa['idtipoventa'] .
                ', último número ' . $tipoventa['letra'] . completar_numero($tipoventa['puntodeventa'], 5) . '-' . completar_numero($tipoventa['ultimonumero'], 8) .
                ', discrimina ' . $tipoventa['discrimina'] .
                ($tipoventa['muevestock'] ? ', mueve stock' : '') .
                ($tipoventa['muevesaldo'] ? ', mueve saldo' : '') .
                ($tipoventa['operacioninversa'] ? ', hace operacion inversa' : '') .
                ($tipoventa['tienesituacion'] ? ', situacion predeterminada ' . $i18n[$tipoventa['situacion']] : '') .
                ($tipoventa['auto_aprobar'] ? ', tiene auto-aprobar' : '') .
                ', modo de facturación ' . $tipoventa['tipofacturacion'] .
                ', modo de impresión ' . $tipoventa['tipoimpresion'];
                texto('texto', $tipoventa['comportamiento'], $tipoventa['nombre'], 'auto', false, false, $ayuda_puntual);
            }
        }
        contenido_fin();
    }

    // Observaciones
    contenido_inicio($i18n_funciones[71], '100');
    {
        area('observacion', '', $datos['observacion']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[1]), array('valor' => $i18n[3])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    $("input[name=cuit]").keyup(function () {
        if ($("input[name=cuit]").val().toString().length == 13)
            validar_cuit($("input[name=cuit]"));
    });
    $("input[name=cuit]").change(function () {
        validar_cuit($("input[name=cuit]"));
    });
</script>
