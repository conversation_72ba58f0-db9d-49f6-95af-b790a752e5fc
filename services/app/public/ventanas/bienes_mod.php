<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('entero', 'estado'),
        array('entero', 'idtipobien'),
        array('texto', 'nombre'),
        array('texto', 'codigo'),
        array('largo', 'observacion'),
        array('largo', 'obsinterna')
    ));

} else {
    $datos = array_sql(consulta_sql("SELECT idtipobien, idcliente, estado, nombre, codigo, observacion, obsinterna FROM bienes WHERE idbien='".$id."' LIMIT 1"));
    //copiado de bienes alta
    if (!$datos['codigo']) {
        if (!$proximoid = campo_sql(consulta_sql("SELECT idbien FROM bienes ORDER BY idbien DESC LIMIT 1"), 0) + 1)
            $proximoid = 1;
        while (contar_sql(consulta_sql("SELECT idbien FROM bienes WHERE codigo = '".completar_numero($proximoid, 5)."' LIMIT 1")))
            $proximoid++;
        $datos['codigo'] = completar_numero($proximoid, 5);
    }
    //
}

$resultado_sql = consulta_sql("SELECT idbien, nombre FROM bienes WHERE codigo = '".escape_sql($datos['codigo'])."' AND idbien != '$id' LIMIT 1");

if ($boton == $i18n_funciones[22] && contar_sql($resultado_sql)) {
    $temp_array = array_sql($resultado_sql);
    mensajes_alta($i18n[32].'<a href="bienes.php?a=ver&id='.$temp_array['idbien'].'" class="enlace" target="_blank">'.$temp_array['nombre'].'</a>');
    $boton = '';
}

switch ($boton) {
    case $i18n_funciones[22]: // Aceptar
        consulta_sql(
            "UPDATE bienes SET
                estado = '".$datos['estado']."',
                idtipobien = '".$datos['idtipobien']."',
                nombre = '".$datos['nombre']."',
                codigo = '".$datos['codigo']."',
                observacion = '".escape_sql($datos['observacion'])."',
                obsinterna='".escape_sql($datos['obsinterna'])."'
            WHERE idbien='".$id."'
            LIMIT 1
            ");
        fullsearch_update('bienes',$id);
        extras_mod();
        ir_atras();
        break;

    case $i18n_funciones[23]: // Cancelar
        ir_atras();
        break;
}

$cliente = array_sql(consulta_sql(
    "SELECT
        bienes.idcliente,
        clientes.nombre, clientes.contacto, clientes.telefonos, clientes.mail, clientes.domicilio, clientes.observacion, clientes.obsinterna, clientes.obsrecordatorio,
        categorias_localidades.nombre AS localidad
    FROM bienes
        LEFT JOIN clientes ON bienes.idcliente = clientes.idcliente
        LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
    WHERE idbien = '$id'
    LIMIT 1"));

ventana_inicio($i18n[58].$datos['nombre'], '100', array(
    array('tipo' => 'imagen', 'url' => 'bienes.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[88]),
    array('tipo' => 'ajax', 'url' => 'bienes.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[76], 'permiso' => 'bienes_baja', 'opciones' => 'onclick="return confirma('."'$i18n[9]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[83])));
{
    if ($cliente['idcliente']) {

        obsrecordatorio($cliente['obsrecordatorio']);

        // Cliente
        contenido_inicio($i18n[53].': '.$cliente['nombre'], '100', true, false);
        {
            texto('texto', $i18n[41], $cliente['idcliente']);
            texto('texto', $i18n[40], $cliente['contacto']);
            texto('mail', $i18n[48], $cliente['mail']);
            texto('texto', $i18n[42], $cliente['telefonos']);
            texto('texto', $i18n[38], $cliente['domicilio']);
            texto('texto', $i18n[23], $cliente['localidad']);

            if ($cliente['observacion']) {
                observacion($i18n[35], $cliente['observacion']);
            }
            if ($cliente['obsinterna']) {
                observacion($i18n[87], $cliente['obsinterna']);
            }
            if ($cliente['obsrecordatorio']) {
                observacion($i18n[36], $cliente['obsrecordatorio']);
            }
        }
        contenido_fin();
    }

    // Datos básicos
    contenido_inicio($i18n[59]);
    {
        entrada('texto', 'nombre', $i18n[34], $datos['nombre'], '40', '60');
        marcas($i18n[79], '10', array(array('nombre' => 'estado', 'titulo' => $i18n[51], 'valor' => $datos['estado'])));
        entrada('texto', 'codigo', $i18n[20], $datos['codigo'], '25', '60');
        selector_familiar('idtipobien', $i18n[43], $datos['idtipobien'], '25', 'categorias_bienes', true, true, true);
    }
    contenido_fin();

    extras();

    // Observaciones
    contenido_inicio($i18n[35], '100', true, false);
    {
        area('observacion', $i18n[35], $datos['observacion']);
        area('obsinterna', $i18n[87], $datos['obsinterna']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n_funciones[22]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();
?>
<script type="text/javascript" charset="utf-8">
    function validacion_bienes_mod(boton)
    {
        if (boton == '<?php echo $i18n_funciones[22]; ?>' && $("#marco_bienes_mod input[name='nombre']").val() == '') {
<?php script_validacion_flotante("alerta", $i18n[28], "input[name='nombre']"); ?>

            return false;
        } else if (boton == '<?php echo $i18n_funciones[22]; ?>' && $("#marco_bienes_mod input[name='codigo']").val() == '') {
<?php script_validacion_flotante("alerta", $i18n[33], "input[name='codigo']"); ?>

            return false;
        } else

            return true;
    };
</script>
