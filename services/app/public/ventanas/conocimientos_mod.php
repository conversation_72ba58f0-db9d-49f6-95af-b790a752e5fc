<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('texto', 'titulo'),
        array('entero', 'idtipoconocimiento'),
        array('largo', 'texto')
    ));
} else {
    $datos = array_sql(consulta_sql("SELECT * FROM conocimientos WHERE idconocimiento='".$id."' LIMIT 1"));
}

switch ($boton) {
    case $i18n[12]: // Aceptar
        consulta_sql("UPDATE conocimientos SET titulo='".$datos['titulo']."', texto='".$datos['texto']."', idtipoconocimiento='".$datos['idtipoconocimiento']."' WHERE idconocimiento='".$id."' LIMIT 1");
        fullsearch_update('conocimientos',$id);
        extras_mod();
        ir_atras();
        break;

    case $i18n[10]: // Cancelar
        ir_atras();
        break;
}

ventana_inicio($i18n[5], '100', array(
    array('url' => 'conocimientos.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[4]),
    array('url' => 'conocimientos.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[17], 'opciones' => 'onclick="return confirma('."'$i18n[18]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[6])));
{
    // Datos básicos
    contenido_inicio($i18n[8]);
    {
        entrada('texto', 'titulo', $i18n[2], $datos['titulo'], '50', '200');
        selector_familiar('idtipoconocimiento', $i18n[11], $datos['idtipoconocimiento'], '50', 'categorias_conocimientos', true, true, true);
        area('texto', $i18n[3], $datos['texto']);
    }
    contenido_fin();

    extras();

    botones(array(array('valor' => $i18n[12]), array('valor' => $i18n[10])));
}
ventana_fin();
