<?php
$idcompra = recibir_variable('entero', 'idcompra', true);
$idtipocompra = recibir_variable('entero', 'idtipocompra', true);
$idproveedor = recibir_variable('entero', 'idproveedor', true);
$boton = recibir_variable('texto', 'boton', true);
$idservicio = recibir_variable('entero', 'idrelacion', true);
$actualizar_costos = recibir_variable('moneda', 'act_costos', true);
$pedido_faltantes = recibir_variable('texto', 'pedido_faltantes', true);

if ($idcompra) {
    $compra = array_sql(consulta_sql(
        "SELECT condicionventa, vencimiento1, vencimiento2, subtotal, idproveedor, tiporelacion, idrelacion,
            descuento, neto, nogravado, iva, total, observacion, puntodeventa, idcomportamiento
        FROM compras
        WHERE idcompra = '$idcompra'
        LIMIT 1"));
    $idproveedor = $compra['idproveedor'];

} else {
    $compra = array();
}

if ($idproveedor)
    $proveedor = array_sql(consulta_sql("SELECT proveedores.nombre, proveedores.idmoneda, idtipoiva, razonsocial, cuit, domicilio, idlocalidad, categorias_proveedores.iddeposito
        FROM proveedores
        LEFT JOIN categorias_proveedores ON proveedores.idtipoproveedor = categorias_proveedores.idtipoproveedor
        WHERE idproveedor = '".$idproveedor."'"));
else {
    $idproveedor = 1;
    $proveedor = [
        'iddeposito' => 1,
        'idmoneda' => 1,
        'idtipoiva' => 0,
        'razonsocial' => '',
        'cuit' => 0,
        'domicilio' => '',
        'cuit' => 0,
        'idlocalidad' => 0,
    ];
}

if (!$boton) {
    $temp_array = array_sql(consulta_sql(
        "SELECT nombre
        FROM categorias_compras
        WHERE idtipocompra = '$idtipocompra'
        LIMIT 1"));
    $boton = $temp_array['nombre'];
}

$tipocompra = array_sql(consulta_sql(
    "SELECT *
    FROM categorias_compras
    WHERE nombre = '$boton'
    LIMIT 1"));

$numerocompleto = !$tipocompra['esfiscal']
    ? 'CO'.completar_numero(campo_sql(consulta_sql("SELECT idcompra FROM compras ORDER BY idcompra DESC LIMIT 1"), 0) + 1, 8)
    : '';

$discrimina = (($_SESSION['configuracion_discrimina']
    && campo_sql(consulta_sql(
        "SELECT discrimina
        FROM tablas_condiciones
            INNER JOIN proveedores ON tablas_condiciones.idtipoiva = proveedores.idtipoiva
        WHERE idproveedor = '$idproveedor'
        LIMIT 1"), 0))
        ? 'A' : 'C');

if (!$fecha)
    $fecha = date("d-m-Y H:i:s");

if ($boton == $i18n[286] || $boton == $i18n[313] || $boton == $i18n[319]) {
// El Pedido de cotización / Remito sin valorizar siempre llevan discrimina R
// Lo busco en idiomas_proveedores o en idiomas_compras
    $buscar_idcomportamiento = $boton;
    $discrimina = 'R';
} else {
    $buscar_idcomportamiento = $boton . " " . $discrimina;
}

consulta_sql(
    "INSERT INTO compras SET
            estado = 'abierto',
            situacion = '".($tipocompra['tienesituacion'] ? $tipocompra['situacion'] : 'sin_especificar')."',
            iddeposito = '".$proveedor['iddeposito']."',
            idtipocompra = '".$tipocompra['idtipocompra']."',
            idmoneda = '".$proveedor['idmoneda']."',
            idcomportamiento = (SELECT idcomportamiento FROM tablas_comportamientos WHERE nombre LIKE '".$buscar_idcomportamiento."'),
            numerocompleto = '".$numerocompleto."',
            idproveedor = '".$idproveedor."',
            idusuario = '".$_SESSION['usuario_idusuario']."',
            idrelacion = '".$idservicio."',
            tiporelacion = '".($idservicio ? 'servicio' : '')."',
            fecha = '".fecha_sql($fecha)."',
            fechaimputacion = '".fecha_sql($fecha)."',
            condicionventa = '".$compra['condicionventa']."',
            vencimiento1 = '".$compra['vencimiento1']."',
            vencimiento2 = '".$compra['vencimiento2']."',
            subtotal = '".$compra['subtotal']."',
            descuento = '".$compra['descuento']."',
            neto = '".$compra['neto']."',
            nogravado = '".$compra['nogravado']."',
            exento = '".$compra['exento']."',
            iva = '".$compra['iva']."',
            total = '".$compra['total']."',
            observacion = '".escape_sql($compra['observacion'])."',
            muevesaldo = '".$tipocompra['muevesaldo']."',
            muevestock = '".$tipocompra['muevestock']."',
            operacioninversa = '".$tipocompra['operacioninversa']."',
            esfiscal = '".$tipocompra['esfiscal']."',
            idtipoiva = '".$proveedor['idtipoiva']."',
            razonsocial = '".($proveedor['cuit'] && !$proveedor['razonsocial'] ? escape_sql($proveedor['nombre']) : escape_sql($proveedor['razonsocial']))."',
            domicilio = '".escape_sql($proveedor['domicilio'])."',
            cuit = '".$proveedor['cuit']."',
            idlocalidad = '".$proveedor['idlocalidad']."',
            discrimina = '".$discrimina."'"
    );

$idcompra_nuevo = id_sql();

//copio tributos
$resultado_sql = consulta_sql(
    "SELECT *, categorias_tributos.nombre
    FROM tributosxcompras
    INNER JOIN categorias_tributos
    ON tributosxcompras.idtributo = categorias_tributos.idtributo
    WHERE idcompra ='".$idcompra."'");

while ($datos = array_sql($resultado_sql)){
    consulta_sql(
    "INSERT INTO tributosxcompras SET
        idtributo = '".$datos['idtributo']."',
        baseimponible = '".$datos['baseimponible']."',
        alicuota = '".$datos['alicuota']."',
        importe = '".$datos['importe']."',
        idcompra = '".$idcompra_nuevo."'
    ");
}

if ($idcompra) {
    consulta_sql("INSERT INTO comprasxcompras SET
        idcompra = '".$idcompra."',
        idrelacion = '".$idcompra_nuevo."'
    ");

    $resultado_sql = consulta_sql(
        "SELECT idproducto, codigo, cantidad, idunidad, idiva, nombre, costo, descuento, observacion
        FROM productosxcompras
        WHERE idcompra = '".$idcompra."'
        ORDER BY idproductoxcompra");
}

if ($pedido_faltantes) {
    $sucursales = armar_sqls_sucursales();
    $resultado_sql = consulta_sql("SELECT productos.idproducto, codigo, idunidad, idiva, nombre, costo, 0 AS descuento, ".$sucursales['cantidad_faltante'].", observacion
        FROM productos
        ".$sucursales['joinstock']."
        WHERE estado = 1
        AND estadoventa = 1
        AND estadocompra = 1
        AND combo = 0
        AND idproveedor = '".$idproveedor."'
        HAVING cantidad > 0
        ");
}

if ($idcompra || $pedido_faltantes) {
    if (contar_sql($resultado_sql)) {
        $sql_values = array();
        while ($productoxcompra = array_sql($resultado_sql)) {
            $sql_values[] = "(
                '".$idcompra_nuevo."',
                '".$productoxcompra['idproducto']."',
                '".escape_sql($productoxcompra['codigo'])."',
                '".$productoxcompra['cantidad']."',
                '".$productoxcompra['idunidad']."',
                '".$productoxcompra['idiva']."',
                '".escape_sql($productoxcompra['nombre'])."',
                '".$productoxcompra['costo']."',
                '".$productoxcompra['descuento']."',
                '".escape_sql($productoxcompra['observacion'])."'
                )";
        }
        consulta_sql("INSERT INTO productosxcompras (idcompra, idproducto, codigo, cantidad, idunidad, idiva, nombre, costo, descuento, observacion) VALUES " . implode(',', $sql_values));
    }
}

ir_ahora('compras.php?a=mod&id='.$idcompra_nuevo.($actualizar_costos ? '&act_costos=1' : ''));
