<?php
exportar('
    <div id="prn_ventana_cuerpo">
        <div id="prn_ventana_cuerpo_inicio">
        </div> <!--Cierro el div id="prn_ventana_cuerpo_inicio"-->

        <table class="prn_t_ventana_lista" width="100%" id="servicios_ventas_exportar">
            <tr>
                <td colspan="3" class="prn_td_subtitulos">'.$i18n[123].$id.'</td>
            </tr>');

$resultado_sql = consulta_sql("SELECT ventas.*, usuarios.nombrereal AS usuario,
                                        categorias_ventas.letra, categorias_ventas.puntodeventa, categorias_ventas.nombre, categorias_ventas.muevesaldo, categorias_ventas.operacioninversa,
                                        monedas.simbolo
                                FROM ventas
                                    LEFT JOIN usuarios ON ventas.idusuario = usuarios.idusuario
                                    LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
                                    LEFT JOIN clientes ON ventas.idcliente = clientes.idcliente
                                    LEFT JOIN monedas ON clientes.idmoneda = monedas.idmoneda
                                WHERE idrelacion = '".$id."'
                                    AND tiporelacion = 'servicio'
                                ORDER BY fecha ASC");
if (contar_sql($resultado_sql) > 0) {
    exportar('
            <tr class="prn_tr_titulo_lista">
                <th>'.$i18n[44].'</th>
                <th>'.$i18n[125].'</th>
                <th>'.$i18n[126].'</th>
                <th>'.$i18n[127].'</th>
                <th>'.$i18n[222].'</th>
                <th>'.$i18n[128].'</th>
                <th>'.$i18n[134].'</th>
            </tr>');
    $class_fila = 2;
    $saldo = 0;
    while ($venta = array_sql($resultado_sql)) {
        if ($class_fila == 1)
            $class_fila = 2;
        else
            $class_fila = 1;
        exportar('
            <tr class="prn_tr_lista_fila_'.$class_fila.'">
                <td class="prn_td_texto">'.date("d-m-Y H:i", strtotime($venta['fecha'])).'</th>');
        $$venta['numeroventa'] = $venta['letra'].completar_numero($venta['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8);
        exportar('
                <td class="prn_td_texto">'.$venta['nombre'].'</td>
                <td class="prn_td_texto">'.$$venta['numeroventa'].'</td>');
        if ($venta['idusuario']) {
            exportar('
                <td class="prn_td_texto">'.$venta['usuario'].'</td>');
        } else
            exportar('
                <td class="prn_td_texto">'.$i18n[129].'</td>');
        if ($venta['muevesaldo'] && $venta['estado'] == 'cerrado') {
            if ($venta['operacioninversa'])
                $saldo-= $venta['total'];
            else
                $saldo+= $venta['total'];
        }
        exportar('
                <td class="prn_td_texto">'.convertir_numero($venta['descuento'], 'porcentaje').'</td>
                <td class="prn_td_texto">'.convertir_numero($venta['total'], 'moneda', $venta['simbolo']).'</td>
                <td class="prn_td_texto">'.convertir_numero($saldo, 'moneda', $venta['simbolo']).'</td>
            </tr>');

        $resultado_sql2 = consulta_sql("SELECT * FROM ventaspagos WHERE idventa='".$venta['idventa']."'");
        if (contar_sql($resultado_sql2) > 0) {
            while ($pago = array_sql($resultado_sql2)) {
                if ($class_fila == 1)
                    $class_fila = 2;
                else
                    $class_fila = 1;
                exportar('
            <tr class="prn_tr_lista_fila_'.$class_fila.'">
                <td class="prn_td_texto">'.($pago['fecha'] != "0000-00-00 00:00:00" ? date("d-m-Y H:i", strtotime($pago['fecha'])) :  '-').'</td>
                <td class="prn_td_texto">'.$i18n[135].$numeroventa.'</td>
                <td class="prn_td_texto">P'.completar_numero($pago['idventapago'], 8).'</td>');
                if ($venta['idusuario']) {
                    exportar('
                <td class="prn_td_texto">'.$venta['usuario'].'</td>');
                } else
                    exportar('
                <td class="prn_td_texto">'.$i18n[129].'</td>');
                $saldo -= $pago['total'];
                exportar('
                <td class="prn_td_texto"></td>
                <td class="prn_td_texto">'.convertir_numero($pago['total'], 'moneda', $venta['simbolo']).'</td>
                <td class="prn_td_texto">'.convertir_numero($saldo, 'moneda', $venta['simbolo']).'</td>');
            }
        }
    }
    if ($saldo)
        exportar('
                        <tr class="prn_tr_division_lista">
                            <td colspan="7">'.convertir_numero($saldo, 'moneda', $venta['simbolo']).'</td>
                        </tr>
            ');
} else {
    exportar('
            <tr>
                <td class="prn_td_texto" colspan="4">'.$i18n[223].'</td>
            </tr>');
}
exportar('
        </table>
        <div id="prn_ventana_cuerpo_fin"> </div>
    </div> <!--Cierro el <div id="prn_ventana_cuerpo">-->');
