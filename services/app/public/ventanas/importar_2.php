<?php
define('MB_UPLOAD', 4194304);   //4 * 1048576 = 4 mb
require_once __DIR__ . '/../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

// Validaciones del archivo
$validacion = true;
$tipos_csv = array('application/vnd.ms-excel','text/plain','text/csv','text/tsv');
$tipos_archivos_permitidos = $tipos_csv;
$tipos_archivos_permitidos[] = 'application/vnd.ms-excel';
$tipos_archivos_permitidos[] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
$tipos_archivos_permitidos[] = 'application/vnd.oasis.opendocument.spreadsheet';

$random = recibir_variable('texto', 'random', false);
$json_nombre_archivo = herramientas_nombre_archivo($modulo.'_opciones', $random,".json");
$toma_datos_json = false;

if (file_exists($json_nombre_archivo)) {
    $toma_datos_json = true;
    $contenido = file_get_contents($json_nombre_archivo);
    $opciones_json = json_decode($contenido, true);
}

$campo_clave = recibir_variable('texto', 'campo_clave', false);

switch ($boton) {
    case $i18n_funciones[80]: // Continuar

        if (!recibir_variable('texto', 'archivo_subido', false)){
            if (!is_uploaded_file($_FILES['archivo']['tmp_name'])) {
                mensajes_alta($i18n_funciones[122]);
                $validacion = false;

            } elseif (!in_array($_FILES['archivo']['type'], $tipos_archivos_permitidos)) {
                mensajes_alta($i18n_funciones[123]);
                $validacion = false;

            } elseif ($_FILES["archivo"]["size"] > MB_UPLOAD) {
                mensajes_alta($i18n_funciones[124]);
                $validacion = false;

            } else {
                // Muevo el archivo
                $tipo_archivo = 'importar_xls';
                $convertir_archivo = true;
                if (in_array($_FILES['archivo']['type'], $tipos_csv)) {
                    $tipo_archivo = $modulo.'_fuente';
                    $convertir_archivo = false;
                }

                move_uploaded_file($_FILES['archivo']['tmp_name'], herramientas_nombre_archivo($tipo_archivo, $random));

                // Si es XLS convertir a CSV
                if ($convertir_archivo) {

                    try {
                        $spreadsheet = IOFactory::load(herramientas_nombre_archivo($tipo_archivo, $random));
                        $csv_convertido = fopen(herramientas_nombre_archivo($modulo.'_fuente', $random), 'w');

                        foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
                            foreach ($worksheet->getRowIterator() as $row) {
                                $cellIterator = $row->getCellIterator();
                                $cellIterator->setIterateOnlyExistingCells(false);
                                $datos = array();
                                foreach ($cellIterator as $cell) {
                                    if (!is_null($cell)) {
                                        $datos[] = mb_convert_encoding($cell->getCalculatedValue(), 'iso-8859-1');
                                    } else {
                                        $datos[] = "";
                                    }
                                }

                                fputcsv($csv_convertido, $datos, SEPARADOR_CSV);
                            }
                            //tomo la primer hoja del archivo
                            break;
                        }
                    } catch (Exception $e) {
                        mensajes_alta($i18n_funciones[123]);
                        $validacion = false;
                    }
                }

                //detectar la codificacion y dejarla en utf8
                $csv_fuente = fopen(herramientas_nombre_archivo($modulo.'_fuente', $random), 'r');
                $linea = fgets($csv_fuente);

                if (mb_check_encoding($linea, 'iso-8859-1')) {
                    $archivo_completo = file_get_contents(herramientas_nombre_archivo($modulo.'_fuente', $random/*, FILE_USE_INCLUDE_PATH*/));
                    fclose($csv_fuente);
                    $csv_fuente = fopen(herramientas_nombre_archivo($modulo.'_fuente', $random), 'w');
                    $contenido_convertido = mb_convert_encoding($archivo_completo, 'UTF-8', 'iso-8859-1');
                    fwrite($csv_fuente, $contenido_convertido);
                    fclose($csv_fuente);
                }

                //detectar el simbolo separador
                $punto_y_coma = substr_count($linea, ";");
                $coma = substr_count($linea, ",");
                $separador = ";";
                if ($punto_y_coma < $coma)
                    $separador = ",";

            }
        } else {
            $csv_fuente = fopen(herramientas_nombre_archivo($modulo.'_fuente', $random), 'r');
            $linea = fgets($csv_fuente);

            //detectar el simbolo separador
            $punto_y_coma = substr_count($linea, ";");
            $coma = substr_count($linea, ",");
            $separador = ";";
            if ($punto_y_coma < $coma)
                $separador = ",";
        }
        break;

    case $i18n_funciones[26]: // Volver
        // Verificar si el archivo ya subido existe
        if ($csv_fuente = fopen(herramientas_nombre_archivo($modulo.'_fuente', $random), 'r')){
            $linea = fgets($csv_fuente);
            //detectar el simbolo separador
            $punto_y_coma = substr_count($linea, ";");
            $coma = substr_count($linea, ",");
            $separador = ";";
            if ($punto_y_coma < $coma)
                $separador = ",";
        } else {
            $validacion = false;
        }
        break;
}

//Abrir el archivo
// Revisar mínimo 2 líneas y 2 columnas
$can_columnas = 0;
$cant_lineas = 0;
$valores_claves = [];

//valido las columnas:
foreach($columnas as $columna) {
    if (!in_array($columna['nombre'], $valores_claves)) {
        array_push($valores_claves, $columna['nombre']);
    } else {
        mensajes_alta($i18n_funciones[258]);
        $validacion = false;
        break;
    }
}

if (file_exists(herramientas_nombre_archivo($modulo.'_fuente', $random)))
    $archivo = fopen(herramientas_nombre_archivo($modulo.'_fuente', $random), "r");
else
    $validacion = false;

if ($archivo) {
    while (($linea = fgetcsv($archivo, 0, $separador)) !== false  && $cant_lineas < 2) {
        $can_columnas = count($linea);
        ++$cant_lineas;
    }
    fclose($archivo);
    if ($can_columnas < 2 || $cant_lineas < 2 || $cant_lineas > 20000){ //cambio a 2 columnas mínimo
        mensajes_alta($i18n_funciones[125]);
        $validacion = false;
    }
}

if (!$validacion)
    ir_ahora($modulo.'.php?a=importar');

// Obtengo datos de opciones específicas del módulo
foreach ($opciones as $key => $value) {
    $recibir[] = $value['nombre'];
}

if(count($recibir))
    $datos = recibir_matriz($recibir);

// Cargo datos extra del módulo
// Quito datos extras x ahora
/*$extras = array();
$resultado_sql = consulta_sql("SELECT * FROM extrasxmodulos WHERE modulo = '$modulo'");
while ($temp_array = array_sql($resultado_sql)) {
    $extras[] = $temp_array;
}*/

mensajes_efimeros();

ventana_inicio($i18n_funciones[73].' '.strtolower($i18n_funciones[$modulo]).' - '.$paso.'º paso');
{
    contenido_inicio($i18n_funciones[121]);
    {
        // si hay mas de un campo que puede ser clave doy la opcion de seleccionarlo
        $por_defecto = "";
        if ($toma_datos_json && isset($opciones_json["campo_clave"])) {
            $por_defecto = $opciones_json["campo_clave"];
        }
        herramientas_generar_selector_clave($columnas, $por_defecto);

        if ($opciones) {
            //  Opciones avanzadas para la importación
            foreach ($opciones as $key => $opcion) {
                $por_defecto = "";
                if ($toma_datos_json && isset($opciones_json["opciones"][$opcion["nombre"]])) {
                    $por_defecto = $opciones_json["opciones"][$opcion["nombre"]];
                }
                herramientas_generar_opcion($opcion, $por_defecto);
            }
        }
    }
    contenido_fin();

    // genera el js para las validaciones del lado del cliente antes del submit
    herramientas_generar_validacion_js_opciones($opciones);
    contenido_inicio($i18n_funciones[126], ($_SESSION['mobile'] ? false : '100'), false, false, false, 'id="importar-2-form"');
    {
        entrada('hidden', 'random', 'random', $random);
        entrada('hidden', 'paso', 'paso', $paso);
        entrada('hidden', 'separador', 'separador', $separador);

        linea_inicio('titulo', 3);
        {
            celda('texto', $i18n_funciones[127], '33');
            celda('texto', $i18n_funciones[128], '33');
            celda('texto', $i18n_funciones[129], '33');
            celda('texto', '');
        }
        linea_fin();

        //tomo la primera linea del archivo y obtengo los nombres de las columnas
        if (($archivo = fopen(herramientas_nombre_archivo($modulo.'_fuente', $random), "r")) !== false) {
            //cargo los nombres de las columnas para los selectores
            $array_selector_opciones = array();
            $array_selector_opciones[] = array('id' => '', 'valor' => $i18n_funciones[130], 'selected' => true);
            entrada('hidden', 'ayuda_','ayuda_', $i18n_funciones[131]);
            foreach ($columnas as $nombre_columna => $columna) {
                $array_selector_opciones[] = array('id' => $nombre_columna, 'valor' => $columna["nombre"], 'selected' => false);
                $ayuda = "";
                if ($columna["ayuda"]) {
                    $ayuda = $columna["ayuda"];
                }
                entrada('hidden', 'ayuda_'.$nombre_columna,'ayuda_'.$nombre_columna, $ayuda);
            }

            //datos extras
            /*if ($extras){
                foreach ($extras as $extra) {
                    $array_selector_opciones[] = array('id' => 'idextraxmodulo', 'valor' => $extra["nombre"], 'selected' => false);
                }
                //$array_selector_opciones[] = array('id' => '-1', 'valor' => 'Generar como dato extra', 'selected' => false);
                //entrada('hidden', 'ayuda_-1', 'ayuda_-1', "Este dato sera agregado como datos extra");
            }*/

            $linea = fgetcsv($archivo, 0, $separador);
            $columnas_campos = array();
            $i = 0;
            foreach ($linea as $columna) {
                //cargo el nombre de la columna
                linea_inicio();
                {
                    if ($columna) {
                        celda('texto', $columna, '30');
                        //cargo el select de los campos posibles
                        foreach ($array_selector_opciones as $key => $opcion) {
                            $array_selector_opciones[$key]["selected"] = false;
                            if ($toma_datos_json && isset($opciones_json["campos"]['columna_'.$i])){
                                $array_selector_opciones[$key]["selected"] = (strtoupper($opcion["id"]) == strtoupper($opciones_json["campos"]['columna_'.$i]));
                            } else {
                                $array_selector_opciones[$key]["selected"] = (strtoupper($opcion["valor"]) == strtoupper($columna));    //ver acá
                            }
                        }
                        $idcolumna = strtolower(str_replace(' ', '', preg_replace('/[^[:print:]]/', '', $columna)));
                        selector_array('columna_'.$i, '','', '30', $array_selector_opciones, false, 'id=selector_'.$idcolumna.'');
                        celda('largo', '', '30', false, false, 'id=info_'.$idcolumna.'');
                    }
                }
                linea_fin();
                ++$i;
            }

            fclose($archivo);
        } else {
            mensajes_alta($i18n_funciones[188]);
            ir_ahora($modulo.'.php?a=importar');
        }
    }
    contenido_fin();

    //  ->Boton de procesar con advertencia
    botones(array(
        array('tipo' => 'nueva', 'valor' => $i18n_funciones[26]),
        array('tipo' => 'nueva', 'valor' => $i18n_funciones[80], 'opciones' => 'id="continuar"'),
    ));

}
ventana_fin();

?>
<script type="text/javascript">
    //Quito esto para verlo cuando haya ayudas
    $(function () {
        $('#selector_ndeproducto').prop("disabled", true);
        //muestro la ayuda rápida de la columna
        /*$('.entrada_select').change(function(event) {
            $(this).parent().next('div').find('span').html($("input[name=ayuda_"+$("select[name="+jQuery(this).attr("name")+"]").val()+"]").val());
        });*/
    });

    $("#continuar").click(function(){
        if ($('select[name=sep_decimal]').val() == 0){
            alert('<?php echo $i18n_herramientas[16];?>');
            return false;
        }
        if ($('select[name=iddeposito]').val() == 0){
            if (confirm('<?php echo $i18n_herramientas[18];?>')){
                return true;
            } else {
                return false;
            }
        }
    });

    $('select[name="campo_clave"]').change(function(e) {
        if ($('select[name="campo_clave"]').val() == 'codigoproveedor' || $('select[name="campo_clave"]').val() == 'codigo') {
            $('#selector_ndeproducto').prop("disabled", true);
            $('#info_ndeproducto').html("<i>No está permitido actualizar los Nº de productos</i>");
        } else {
            $('#selector_ndeproducto').prop("disabled", false);
            $('#info_ndeproducto').html("");
        }
    });
</script>

