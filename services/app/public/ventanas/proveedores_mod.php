<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('texto', 'nombre'),
        array('texto', 'contacto'),
        array('texto', 'telefonos'),
        array('texto', 'domicilio'),
        array('mail', 'mail'),
        array('entero', 'idtipoproveedor'),
        array('entero', 'idlocalidad'),
        array('entero', 'idtipoiva'),
        array('texto', 'razonsocial'),
        array('largo', 'obsinterna'),
        array('cuit', 'cuit')
    ));
    // TODO: Eliminar esta línea al arreglar el validador de cuit
    $datos['cuit'] = str_replace('-', '', $datos['cuit']);

} else {
    $datos = array_sql(consulta_sql(
        "SELECT proveedores.*,
            categorias_localidades.nombre AS localidad,
            tablas_condiciones.nombre AS tipoiva,
            monedas.nombre AS moneda, monedas.simbolo
        FROM proveedores
            LEFT JOIN categorias_localidades ON proveedores.idlocalidad = categorias_localidades.idlocalidad
            LEFT JOIN tablas_condiciones ON proveedores.idtipoiva = tablas_condiciones.idtipoiva
            LEFT JOIN monedas ON proveedores.idmoneda = monedas.idmoneda
        WHERE idproveedor = '$id'"));
}

switch ($boton) {
    case $i18n[62]: // Aceptar
        if ($datos['mail'] && !filter_var($datos['mail'], FILTER_VALIDATE_EMAIL)) {
            mensajes_alta($i18n[65]);
        }

        if ($datos['cuit']) {
            $existe_sql = consulta_sql("SELECT idproveedor, nombre FROM proveedores WHERE cuit = '".$datos['cuit']."' AND idproveedor != '$id' LIMIT 1");
            if(contar_sql($existe_sql)) {
                $existe_proveedor = array_sql($existe_sql);
                mensajes_alta($i18n[308].'<a href=proveedores.php?a=ver&id='.$existe_proveedor['idproveedor'].' target=_blank>'.$existe_proveedor['nombre'].'</a>');
            }
            if(!$datos['razonsocial']) {
                mensajes_alta($i18n[321]);
            }
        }
        consulta_sql("UPDATE proveedores SET
            nombre = '".$datos['nombre']."',
            contacto = '".$datos['contacto']."',
            telefonos = '".$datos['telefonos']."',
            mail = '".$datos['mail']."',
            domicilio = '".$datos['domicilio']."',
            idtipoproveedor = '".$datos['idtipoproveedor']."',
            idlocalidad = '".$datos['idlocalidad']."',
            idtipoiva = '".$datos['idtipoiva']."',
            razonsocial = '".$datos['razonsocial']."',
            cuit = '".$datos['cuit']."',
            obsinterna = '".$datos['obsinterna']."'
            WHERE idproveedor = '".$id."'");
        fullsearch_update('proveedores',$id);
        extras_mod();

        ir_ahora('proveedores.php?a=ver&id='.$id);
        break;

    case $i18n[205]: // Cancelar
        ir_atras();
        break;
}

ventana_inicio($i18n[67].$datos['nombre'], '100', array(
    array('tipo' => 'imagen', 'url' => 'proveedores.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[285]),
    array('tipo' => 'imagen', 'url' => 'proveedores.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[166], 'permiso' => 'proveedores_baja', 'opciones' => 'onclick="return confirma('."'$i18n[167]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[168])));
{
    // Datos de contacto
    contenido_inicio($i18n[68]);
    {
        entrada('texto', 'nombre', $i18n[73], $datos['nombre'], '50', '60');
        entrada('texto', 'contacto', $i18n[74], $datos['contacto'], '25', '150');
        selector('idtipoproveedor', $i18n[30], $datos['idtipoproveedor'], '25', 'categorias_proveedores', 'nombre', true, true, true);
        entrada('email', 'mail', $i18n[76], $datos['mail'], '25', '320');
        entrada('texto', 'telefonos', $i18n[75], $datos['telefonos'], '25', '150');
        entrada('texto', 'domicilio', $i18n[77], $datos['domicilio'], '25', '150');
        entrada('hidden', 'idlocalidad', false, $datos['idlocalidad']);
        entrada('texto', 'localidad', $i18n[78], $datos['localidad'], '25', '150', false, 'onFocus="seleccionarLocalidades();"', 'categorias_localidades');
    }
    contenido_fin();

    // Datos de facturación
    contenido_inicio($i18n[69]);
    {
        entrada('cuit', 'cuit', $i18n[49], $datos['cuit'], '50', false, false, 'autocomplete="off"');
        texto('texto', $i18n[86], '<br>'.$datos['razonsocial'].' ', '25', false, false, false, 'id="razonsocial_texto"'); // Pongo un espacio para que aparezca siempre
        texto('texto', $i18n[79], '<br>'.$datos['tipoiva'].' ', '25', false, false, false, 'id="tipoiva_texto"'); // Pongo un espacio para que aparezca siempre

        entrada('hidden', 'idtipoiva', false, $datos['idtipoiva']);
        entrada('hidden', 'razonsocial', false, $datos['razonsocial']);

        salto_linea();

        bloque_inicio('afip_responde');
        {
            texto('italica', false, $i18n[309], '50', false, 'info');
            enlaces(false, array(
                array('tipo' => 'modal', 'modulo' => 'clientes',
                    'url' => 'afip_no_responde', 'id' => $id, 'valor' => $i18n[310])
                ), '50');
        }
        bloque_fin();
        bloque_inicio('afip_validando', 'style="display: none;"');
        {
            texto('italica', false, $i18n[312], '100', false, 'ajax');
        }
        bloque_fin();
        bloque_inicio('afip_no_responde', 'style="display: none;"');
        {
            texto('italica', false, $i18n[311], '50', false, 'alerta');
            enlaces(false, array(
                array('tipo' => 'modal', 'modulo' => 'clientes',
                    'url' => 'afip_no_responde', 'id' => $id, 'valor' => $i18n[310])
                ), '50');
        }
        bloque_fin();
    }
    contenido_fin();

    contenido_inicio($i18n[325]);
    {
        texto('titulo', $i18n[325], $datos['moneda'].' ('.$datos['simbolo'].')');
    }
    contenido_fin();

    extras();

    // Observaciones
    contenido_inicio($i18n[221], '100', true, false);
    {
        area('obsinterna', $i18n[90], $datos['obsinterna']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[62]), array('valor' => $i18n[205])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    // Necesito la tabla condiciones en JS
    var tablas_condiciones = <?php echo tabla_json('tablas_condiciones'); ?>;

    function validacion_proveedores_mod(boton)
    {
        var mail = $("#marco_proveedores_mod input[name='mail']").val();

        if (boton == "<?php echo $i18n[62]; ?>" && $("#marco_proveedores_mod input[name='nombre']").val() == '') {
            <?php script_validacion_flotante("alerta", $i18n[24], "input[name='nombre']"); ?>
            if (mail == '') {
                <?php script_validacion_flotante("informacion", $i18n[25], "input[name='mail']"); ?>
            }
            return false;

        } else if (boton == '<?php echo $i18n[62]; ?>' && mail != '' && !$("input[name='mail']").valid()) {
            <?php script_validacion_flotante("alerta", $i18n[65], "input[name='mail']"); ?>
            return false;

        } else {
            return true;
        }
    };
</script>
