<?php
require_once PATH_SCRIPTS."manual/txtafip/WrapperBD.php";
require_once PATH_SCRIPTS."manual/txtafip/generadores/GeneradorArchivoCompras.php";
require_once PATH_SCRIPTS."manual/txtafip/generadores/GeneradorArchivoAlicuotasCompras.php";
require_once PATH_SCRIPTS."manual/txtafip/generadores/GeneradorArchivoArba6.php";

$txt = recibir_variable('texto', 'txt', true);
if (!in_array($txt, ['rg3685', 'libroiva', 'arba-retencion']))
    ir_inicio();

$enlace = recibir_variable('texto', 'tipo', true);
if (in_array($enlace, array('compras', 'alicuotas_compras'))) {
    $datos['periodo'] = recibir_variable('texto', 'periodo', true);
    $temp_array = explode('-', $datos['periodo']);
    $datos['desde'] = date("Y-m-d", strtotime('first day of '.$temp_array[1].'-'.$temp_array[0]));
    $datos['hasta'] = date("Y-m-d", strtotime('last day of '.$temp_array[1].'-'.$temp_array[0]));

} else if ($enlace == 'arba-retencion') {
    $datos['periodo'] = recibir_variable('texto', 'periodo', true);
    $temp_array = explode('-', $datos['periodo']);
    $datos['quincena'] = recibir_variable('texto', 'quincena', true);
    if ($datos['quincena'] == 1) {
        $datos['desde'] = date("Y-m-d", strtotime('first day of '.$temp_array[1].'-'.$temp_array[0]));
        $datos['hasta'] = date("Y-m-15", strtotime('last day of '.$temp_array[1].'-'.$temp_array[0]));
    } else {
        $datos['desde'] = date("Y-m-16", strtotime('first day of '.$temp_array[1].'-'.$temp_array[0]));
        $datos['hasta'] = date("Y-m-d", strtotime('last day of '.$temp_array[1].'-'.$temp_array[0]));
    }
}

$enlace_descarga = "compras.php?a=$a&txt=$txt";

switch ($enlace) {
    case 'compras':
        $nombre_archivo = $txt == 'rg3685'
            ? "Importación de Comprobantes de Compras del período " . $datos['periodo'] . " (" . $_SESSION['configuracion_cuit'] . ").txt"
            : 'LIBRO_IVA_DIGITAL_COMPRAS_CBTE.txt';
        $bd = new WrapperBD();
        $generador = new GeneradorArchivoCompras($bd);
        $generador->generar($datos['desde'], $datos['hasta']);
        $generador->descargarTxt($nombre_archivo);
        exit();
        break;

    case 'alicuotas_compras':
        $nombre_archivo = $txt == 'rg3685'
            ? "Importación de Alicuotas de Compras del período " . $datos['periodo'] . " (" . $_SESSION['configuracion_cuit'] . ").txt"
            : 'LIBRO_IVA_DIGITAL_COMPRAS_ALICUOTAS.txt';
        $bd = new WrapperBD();
        $generador = new GeneradorArchivoAlicuotasCompras($bd);
        $generador->generarAlicuota($datos['desde'], $datos['hasta']);
        $generador->descargarTxt($nombre_archivo);
        exit();
        break;

    case 'arba-retencion':
        $temp_array = explode('-', $datos['periodo']);
        // AR-CUIT-PERIODO-ACTIVIDAD-LOTE_MD5
        $nombre_archivo = 'AR-'.$_SESSION['configuracion_cuit'].'-'.$temp_array[1].$temp_array[0].$datos['quincena'].'-6-LOTE1';
        $bd = new WrapperBD();
        $generador = new GeneradorArchivoArba6($bd); // Mensuales Método Devengado (Actividad )
        $generador->generar($datos['desde'], $datos['hasta']);
        $generador->descargarZipMd5($nombre_archivo);
        exit();
        break;

    default:
        $datos = array('periodo' => '0');
        break;
}


switch ($txt) {
    case 'rg3685':
        $i18n_titulo_ventana = $i18n[213];
        $i18n_italica = $i18n[203].' '.$i18n_funciones[99];
        $enlaces = [
            ['url' => 'https://www.afip.gob.ar/comprasyventas',
                'valor' => $i18n[217], 'opciones' => 'target="_blank"'],
            ['url' => 'https://www.afip.gob.ar/Aplicativos/oProgramasImpositivos/RegimenInfComprasVentas.asp',
                'valor' => $i18n[254], 'opciones' => 'target="_blank"'],
        ];
        $i18n_italica_descarga = $i18n[253];
        $i18n_descargas = $i18n[212];

        $descargas = array(
            array('valor' => $i18n[210] . ' de Compras', 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=compras"),
            array('valor' => $i18n[211] . ' de Compras', 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=alicuotas_compras"),
        );
        break;

    case 'libroiva':
        $i18n_titulo_ventana = $i18n_funciones[261];
        $i18n_italica = $i18n[314].' '.$i18n_funciones[99];
        $enlaces = [
            ['url' => 'https://www.afip.gob.ar/libro-iva-digital', 'valor' => $i18n[315], 'opciones' => 'target="_blank"'],
        ];
        $i18n_italica_descarga = $i18n[319];
        $i18n_descargas = $i18n[316];

        $descargas = array(
            array('valor' => $i18n[317], 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=compras"),
            array('valor' => $i18n[318], 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=alicuotas_compras"),
        );
        break;

    case 'arba-retencion':
        $i18n_titulo_ventana = $i18n_funciones[301];
        $i18n_italica = $i18n[320].$i18n[321].' '.$i18n_funciones[99];
        $enlaces = [
            ['url' => 'https://web.arba.gov.ar/agentes#presentacion-de-ddjj', 'valor' => $i18n[322], 'opciones' => 'target="_blank"'],
        ];
        $i18n_italica_descarga = $i18n[323];
        $i18n_descargas = $i18n[324];

        $descargas = array(
            array('valor' => $i18n[321].' '.$i18n[325], 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=arba-retencion&quincena=1"),
            array('valor' => $i18n[321].' '.$i18n[326], 'opciones' => 'target="_blank"', 'url' => $enlace_descarga."&tipo=arba-retencion&quincena=2"),
        );
        break;
}

ventana_inicio($i18n_titulo_ventana);
{
    contenido_inicio($i18n[214]);
    {
        texto('italica', false, $i18n_italica);
        enlaces(false, $enlaces);

        $fechas = array_sql(consulta_sql("SELECT
            (SELECT fechaimputacion FROM compras WHERE fechaimputacion != 0 AND estado = 'cerrado' ORDER BY fechaimputacion ASC LIMIT 1) AS desde,
            (SELECT fechaimputacion FROM compras WHERE fechaimputacion != 0 AND estado = 'cerrado' ORDER BY fechaimputacion DESC LIMIT 1) AS hasta
            "));
        $meses = listar_meses($fechas['desde'], $fechas['hasta']);
        $array_selector_opciones = array(
            array('valor' => 'Seleccionar período', 'id' => '0')
            );
        for ($i = count($meses); $i; $i--) {
            $mes = $meses[$i-1];
            $array_selector_opciones[] = array('id' => $mes, 'valor' => $mes . ' ('.$i18n_funciones['mes_'.$mes[0].$mes[1]].')');
        }
        selector_array('periodo', $i18n[216], $datos['periodo'], '25', $array_selector_opciones);
    }
    contenido_fin();

    contenido_inicio(false, '100', false, false, false, 'id="enlaces" style="display: none;"');
    {
        texto('italica', false, $i18n_italica_descarga, 'auto', false, 'info');
        enlaces($i18n_descargas, $descargas);
    }
    contenido_fin();

}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    $("select[name='periodo']").change(function() {
        if ($("select[name='periodo'] option:selected").val() != 0) {
            var periodo = $("select[name='periodo'] option:selected").val();
            $("#enlaces a").each(function (a) {
                $(this).attr('href', $(this).attr('href') + '&periodo=' + periodo);
            })
            $("#enlaces").fadeOut(function() {
                $("#enlaces").fadeIn();
            });

        } else {
            $("#enlaces").fadeOut();
        }
    });
</script>
