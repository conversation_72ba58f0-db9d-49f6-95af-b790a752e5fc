<?php
$MP_idtipocaja = contar_sql(consulta_sql("SELECT categorias_cajas.idtipocaja
    FROM categorias_cajas
    INNER JOIN tienda ON tienda.MP_idtipocaja = categorias_cajas.idtipocaja
    WHERE idtienda = '".$id."'
    LIMIT 1
"));

if ($boton) {
    $datos = recibir_matriz(array(
        array('texto', 'tienda_nombre'),
        array('entero', 'ML_estado'),
        array('texto', 'ML_idtipoventa'),
        array('entero', 'MP_estado'),
        array('entero', 'idtipocaja'),
        array('entero', 'idconcepto'),
        array('entero', 'idiva'),
        array('entero', 'idplantilla_venta_ML'),
        array('entero', 'puntodeventa'),
        array('entero', 'iddeposito'),
        array('entero', 'idtipocliente')
    ));
    $datos['MP_idtipocaja'] = $datos['idtipocaja'];
    $datos['MP_idconcepto'] = $datos['idconcepto'];
    $datos['ML_idiva'] = $datos['idiva'];
    $datos['ML_idtipocliente'] = $datos['idtipocliente'];
    consulta_sql("UPDATE variables SET valor = '".$datos['puntodeventa']."' WHERE variable = 'puntodeventa_predeterminado_ml_facturacion_masiva'");
} else {
    $datos = array_sql(consulta_sql("SELECT * FROM tienda WHERE idtienda = '".$id."' LIMIT 1"));
    $selector_punto_de_venta[] = array('id' => 0, 'valor' => $i18n[245]);
    $resultado_sql = consulta_sql("SELECT puntodeventa FROM categorias_ventas WHERE estado = '1'");
    while ($tipoventa = array_sql($resultado_sql)) {
        if (array_search($tipoventa['puntodeventa'], array_column($selector_punto_de_venta, 'id')) === false ) {
            $selector_punto_de_venta[] = array('id' => $tipoventa['puntodeventa'], 'valor' => $i18n[193].' '. $tipoventa['puntodeventa']);
        }
    }
}

$tienda = array_sql(consulta_sql("SELECT tienda_nombre, ML_access_token, ML_user_id, idplantilla_venta_ML FROM tienda WHERE idtienda = '$id' LIMIT 1"));

switch ($boton) {
    case $i18n_funciones[22]: // Aceptar
        if (!$datos['tienda_nombre']) {
            mensajes_alta($i18n[602]);
            break;
        }

        if (contar_sql(consulta_sql("SELECT idempresa FROM tiendas WHERE idempresa != '".$_SESSION['empresa_idempresa']."' AND ML_user_id = '".$tienda['ML_user_id']."'", 'saasargentina'))) {
            $datos['ML_estado'] = 0;
            mensajes_alta($i18n[623]);
            break;
        }

        // Si es la primera vez que entran a configurar la integración y no seleccionan un tipo de venta existente se crea uno nuevo
        global $bd_link;
        if ($datos['ML_estado'] && !$datos['ML_idtipoventa']) {
            $puntodeventa = campo_sql(consulta_sql("SELECT MAX(puntodeventa) + 1 AS puntodeventa FROM categorias_ventas WHERE idcomportamiento = '".($_SESSION['configuracion_idtipoiva'] == '1' ? '110' : '114')."'"));

            consulta_sql("INSERT INTO categorias_ventas SET
                    idcomportamiento = '".($_SESSION['configuracion_idtipoiva'] == '1' ? '110' : '114')."',
                    nombre = 'Pedido desde MercadoLibre',
                    letra = 'P',
                    puntodeventa = '".$puntodeventa."',
                    ultimonumero = '0',
                    muevestock = '0',
                    muevesaldo = '1',
                    operacioninversa = '0',
                    tienesituacion = '1',
                    situacion = 'pendiente',
                    auto_aprobar = '1',
                    tipofacturacion = 'interno',
                    tipoimpresion = 'predeterminado',
                    discrimina = '".($_SESSION['configuracion_idtipoiva'] == '1' ? 'B' : 'C')."'
                ");
            $datos['ML_idtipoventa'] = id_sql($bd_link);
        }

        if ($datos['MP_estado'] && !$datos['MP_idtipocaja']) {
            $datos['MP_idtipocaja'] = crear_caja_mp();
        }

        consulta_sql("UPDATE tienda SET
                tienda_nombre = '".$datos['tienda_nombre']."',
                ML_estado = '".$datos['ML_estado']."',
                ML_idtipoventa = '".$datos['ML_idtipoventa']."',
                ML_iddeposito_full = '".$datos['iddeposito']."',
                ML_idiva = '".$datos['ML_idiva']."',
                ML_idtipocliente = '".$datos['ML_idtipocliente']."',
                MP_estado = '".$datos['MP_estado']."',
                MP_idtipocaja = '".$datos['MP_idtipocaja']."',
                MP_idconcepto = '".$datos['MP_idconcepto']."',
                idplantilla_venta_ML = '".$datos['idplantilla_venta_ML']."'
                WHERE idtienda = '".$id."'
            ");
        ir_atras();
        break;

    case $i18n_funciones[23]: // Cancelar
        ir_atras();
        break;
}

// Armo opciones de los selectores
if (!$datos['ML_idtipoventa'])
    $selector_ML_tipodeventas = array(array('id' => 0, 'valor' => 'Pedido desde MercadoLibre'));
else
    $selector_ML_tipodeventas = array();
$resultado_sql = consulta_sql(
    "SELECT idtipoventa, nombre
    FROM categorias_ventas
    WHERE estado = '1'
        AND tipofacturacion = 'interno'
        AND discrimina IN ('B', 'C')
        AND operacioninversa = '0'");
while ($temp_array = array_sql($resultado_sql)) {
    $selector_ML_tipodeventas[] = array('id' => $temp_array['idtipoventa'], 'valor' => $temp_array['nombre']);
}

if (!$MP_idtipocaja)
    $selector_MP_tipocaja = array(array('id' => 0, 'valor' => 'MercadoPago'));
else
    $selector_MP_tipocaja = array();
$resultado_sql = consulta_sql(
    "SELECT idtipocaja, nombre
    FROM categorias_cajas
    WHERE estado = '1'
        AND tipo = 'banco'");
while ($temp_array = array_sql($resultado_sql)) {
    $selector_MP_tipocaja[] = array('id' => $temp_array['idtipocaja'], 'valor' => $temp_array['nombre']);
}

$puntodeventa = campo_sql(consulta_sql("SELECT valor FROM variables WHERE variable = 'puntodeventa_predeterminado_ml_facturacion_masiva'"));

mensajes_efimeros();

ventana_inicio(str_replace('{{tienda_nombre}}', $tienda['tienda_nombre'], $i18n[260]));
{
    // Integración con MercadoLibre
    contenido_inicio($i18n[533]);
    {

        if ($id) {
            texto('texto', $i18n[606], $tienda['ML_user_id']);
        }
        entrada('texto', 'tienda_nombre', $i18n[24], $tienda['tienda_nombre'], '25', '60');

        imagen($_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/logo-mercadolibre-codos.png', 'id="logo-mercadolibre"');
        imagen($_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/logo-mercadopago-codos.png', 'id="logo-mercadopago" style="margin-top: -5px;"');

        salto_linea();

        enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_ML_autorizar', 'id' => $id, 'valor' => $i18n[355])), '50');

        bloque_inicio('ML_resultado');
        {
            if(!$tienda['ML_access_token']){
                texto('italica', '', $i18n[534], 'auto', false, 'no', false, 'id="ML_noestado_noautorizado"');
                texto('italica', '', $i18n[535], 'auto', false, 'info', false, 'id="ML_noestado_noautorizadoinfo"');
            } else {
                texto('italica', '', $i18n[536], 'auto', false, 'ok', false, 'id="ML_okestado_okautorizado"');
            }
        }
        bloque_fin();
    }
    contenido_fin();

    // Autogeneración de ventas
    bloque_inicio('ML_autorizada');
    {
        contenido_inicio($i18n[387], '50', false, false, $i18n[374]);
        {
            marcas('', '50', array(
                array('nombre' => 'ML_estado', 'titulo' => $i18n[358], 'valor' => $datos['ML_estado'])
            ));

            if ($datos['ML_estado'])
                texto('italica', '', $i18n[538], 'auto', false, 'ok', false, 'id="ML_ventas_okautorizado"');
            else
                texto('italica', '', $i18n[537], 'auto', false, 'no', false, 'id="ML_ventas_noautorizado"');

            if ($_SESSION['configuracion_idtipoiva'] == 1) {
                selector_array('ML_idtipoventa', $i18n[360], $datos['ML_idtipoventa'], '100', $selector_ML_tipodeventas);
                selector('idiva', $i18n[523], $datos['ML_idiva'], '50', 'tablas_ivas', 'nombre', false, false, false, $i18n[525]);
            } else {
                selector_array('ML_idtipoventa', $i18n[360], $datos['ML_idtipoventa'], '50', $selector_ML_tipodeventas);
            }

            selector('idtipocliente', $i18n[46], $datos['ML_idtipocliente'], '100', 'categorias_clientes', 'nombre', false, true, true);
            selector('iddeposito', $i18n[646], $datos['ML_iddeposito_full'], '100', 'depositos', 'nombre', false, false);

            //ML email automático venta
            marcas('', '100', array(array('nombre' => 'idplantilla_venta_ML', 'titulo' => $i18n[528], 'valor' => $tienda['idplantilla_venta_ML'], 'opciones' => 'id="mail"')));

            salto_linea();

            bloque_inicio('ML_mail');
            {
                $resultado_sql = consulta_sql("SELECT * FROM plantillas");
                if (contar_sql($resultado_sql)) {
                    $array_selector_opciones = array(
                        array('id' => '', 'valor' => $i18n[592]),
                    );

                    while ($plantilla = array_sql($resultado_sql)) {
                        $array_selector_opciones[] = array('id' => $plantilla['idplantilla'], 'valor' => $plantilla['asunto']);
                    }
                    selector_array('idplantilla_venta_ML', $i18n[611], $tienda['idplantilla_venta_ML'], '50', $array_selector_opciones);

                } else {
                    texto('texto', false, $i18n_funciones[104], '50');
                    if ($_SESSION['perfil_configuraciones_tablas']
                        && !$_SESSION['sistema_gratis']) {
                        enlaces(false, array(
                            array('url' => 'configuraciones.php?a=plantillas', 'valor' => $i18n_funciones[103], 'opciones' => 'target="_blank"')
                        ), '100');
                    }
                }

                $array_selector_opciones = array();
                if (filter_var($_SESSION['configuracion_mail'], FILTER_VALIDATE_EMAIL)) {
                    $array_selector_opciones[] = array('id' => 0, 'valor' => $_SESSION['empresa_nombre'] . ' via SaaS Argentina &lt;' . $_SESSION['configuracion_mail'] . '&gt;');
                }

                texto('italica', '', $i18n[610], 'auto');
            }
            bloque_fin();
        }
        contenido_fin();
    }
    bloque_fin();

    // Autogeneración de recibos de pagos
    bloque_inicio('MP_autorizada');
    {
        contenido_inicio($i18n[388], '50', false, false, $i18n[389]);
        {
             marcas('', '66', array(
                array('nombre' => 'MP_estado', 'titulo' => $i18n[358], 'valor' => $datos['MP_estado'])
            ));

            if($datos['MP_estado'])
                texto('italica', '', $i18n[539], 'auto', false, 'ok', false, 'id="MP_pagos_okautorizado"');
            else
                texto('italica', '', $i18n[540], 'auto', false, 'no', false, 'id="MP_pagos_noautorizado"');

            selector_array('idtipocaja', $i18n[362], $datos['MP_idtipocaja'], '50', $selector_MP_tipocaja);
            selector_familiar('idconcepto', $i18n[363], $datos['MP_idconcepto'], '50', 'categorias_conceptos', true, true, true);
        }
        contenido_fin();
    }
    bloque_fin();

    /* Oculto funcionalidad hasta próxima actualización
    contenido_inicio($i18n[636], '100');
    {
        selector_array('puntodeventa', $i18n[637], $puntodeventa, '50', $selector_punto_de_venta, $i18n[640]);
        texto('italica', false, $i18n[642], false, false, 'info');
        observacion(false, $i18n[629], false, 'info');
    }
    contenido_fin();
    */

    botones(array(array('valor' => $i18n_funciones[22]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();

?>
<script>

$(function () {

    var logo_ml = $("#logo-mercadolibre");
    var logo_mp = $("#logo-mercadopago");
    logo_ml.removeClass("imagen");
    logo_mp.removeClass("imagen");
    logo_ml.parent().css({
        "cursor": "pointer",
    })
    logo_mp.parent().css({
        "cursor": "pointer",
        "margin-left": "250px",
        "margin-top": "-80px",
    })

    ML_resultado();
    MP_resultado();
    ML_mail();

    $("#logo-mercadolibre").click(function () {
        window.open('https://www.mercadolibre.com.ar', '_blank');
    });

    $("#logo-mercadopago").click(function () {
        window.open('https://www.mercadopago.com.ar', '_blank');
    });

    $("input[name='ML_estado']").click(function () {
        ML_resultado();
    });

    $("input[name='MP_estado']").click(function () {
        MP_resultado();
    });

    $("input[name='idplantilla_venta_ML']").click(function () {
        ML_mail();
    });

});
</script>
