<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('largo', 'texto')
    ));
}

switch ($boton) {
    case $i18n_funciones[22]: // Aceptar
        strip_tags(str_replace(array('&nbsp;', ' ', '\n', '\r'), '', $datos['texto']));
        $fecha = date("Y-m-d H:i:s");

        consulta_sql("UPDATE saas_tickets SET estado='abierto' WHERE idticket='".$id."' LIMIT 1", 'admin');
        consulta_sql("INSERT INTO saas_textosxtickets (idticket, idusuario, fecha, texto) VALUES ('".$id."', '".$_SESSION['usuario_idusuario']."', '".$fecha."', '".$datos['texto']."')", 'admin');

        consulta_sql("UPDATE tickets SET estado='abierto' WHERE idticket='".$id."' LIMIT 1");
        consulta_sql("INSERT INTO textosxtickets (idticket, idusuario, fecha, texto) VALUES ('".$id."', '".$_SESSION['usuario_idusuario']."', '".$fecha."', '".$datos['texto']."')");

        // Aviso por mail que hay soporte
        $ticket = array_sql(consulta_sql("SELECT idempresa, idusuario, idusuario_saas, empresa_nombre, usuario_nombre, titulo FROM saas_tickets WHERE idticket = '$id'", 'admin'));
        switch ($ticket['idusuario_saas']) {
            case 3: $mail_soporte = MAIL_DESARROLLO; break;
            case 196: $mail_soporte = MAIL_ADMIN; break;
            default: $mail_soporte = MAIL_INFO; break;
        }
        $mail = 'Consulta de ayuda modificada por el usuario:<br>
            N° empresa: '.$ticket['idempresa'].'<br>
            Empresa: '.$ticket['empresa_nombre'].'<br>
            N° usuario: '.$ticket['idusuario'].'<br>
            Usuario: '.$ticket['usuario_nombre'].'<br>
            Fecha: '.$fecha.'<br>
            Asunto: '.$ticket['titulo'].'<br>
            Texto:<br>
            '.$datos['texto'].'<br>
            <br>
            <a href="'.URL_SAAS.'/saas.php?a=modticket&id='.$id.'">Ver consulta</a><br>';
        email_queue(MAIL_SERVIDOR, $mail_soporte, 'Consulta de ayuda Nº '.$id.' modificada', $mail);

        ir_atras();
        break;

    case $i18n_funciones[23]: // Cancelar
        ir_atras();
        break;
}

$ticket = array_sql(consulta_sql("SELECT * FROM tickets WHERE idticket='".$id."' LIMIT 1"));
$resultado_sql = consulta_sql("SELECT * FROM textosxtickets WHERE idticket='".$id."' ORDER BY fecha");

if (contar_sql($resultado_sql) > 1) {
    $temp_boton_baja = array('a' => 'baja_no', 'title' => $i18n[17]);
} else {
    $temp_boton_baja = array('tipo' => 'ajax', 'url' => 'tickets.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[14], 'opciones' => 'onclick="return confirma('."'$i18n[15]'".')"');
}

ventana_inicio($i18n[7], '100', array(
    array('url' => 'tickets.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[11]),
    $temp_boton_baja,
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[10])));
{
    contenido_inicio();
    {
        $temp_estado = (($ticket['estado'] == 'pausado') ? 'comenzado' : $ticket['estado']);
        texto('texto', $i18n[19], $i18n[$ticket['estado']], 'auto', false, $temp_estado);
        texto('texto', $i18n[18], $ticket['titulo']);
    }
    contenido_fin();

    contenido_inicio($i18n[8]);
    {
        while ($textoxticket = array_sql($resultado_sql)) {
            $temp_titulo = '['.mostrar_fecha('fechayhora', $textoxticket['fecha']).']&nbsp;&nbsp;';
            if ($textoxticket['idusuario'] == $_SESSION['usuario_idusuario'])
                $temp_titulo.= $_SESSION['usuario_nombre'];
            else
                $temp_titulo.= $i18n[20];
            observacion($temp_titulo, $textoxticket['texto']);
        }
        area('texto', '('.mostrar_fecha('fechayhora', date("Y-m-d H:i")).') '.$_SESSION['usuario_nombre'].':', $datos['texto']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n_funciones[22]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();
