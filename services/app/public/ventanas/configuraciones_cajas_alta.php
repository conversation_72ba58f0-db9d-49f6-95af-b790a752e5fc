<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('texto', 'nombre'),
        array('texto', 'compartida'),
        array('texto', 'tipo'),
        array('entero', 'estado')
    ));
} else {
    $datos = array();
}

switch ($boton) {
    case $i18n[78]: //Agregar
        consulta_sql("INSERT INTO categorias_cajas SET
            nombre = '".$datos['nombre']."',
            compartida = '".$datos['compartida']."',
            tipo = '".$datos['tipo']."'");
        $idtipocaja = id_sql();

        consulta_sql("INSERT INTO cajas SET
            idtipocaja = '$idtipocaja',
            fechaapertura = '".fecha_sql('ahora')."'");
        $idcaja = id_sql();

        consulta_sql("UPDATE categorias_cajas SET
            idcaja = '$idcaja'
            WHERE idtipocaja = '$idtipocaja'");
        insertar_saldo('cajas', $idcaja);

        ir_atras();
        break;

    case $i18n[66]: //Cancelar
        ir_atras();
        break;
}

mensajes_efimeros();

ventana_inicio($i18n[230]);
{
    // Datos básicos
    contenido_inicio($i18n[89]);
    {
        entrada('texto', 'nombre', $i18n[84], $datos['nombre'], '40', '60');
        selector_array('tipo', $i18n[513], $datos['tipo'], '20',
            array(
            array('id' => 'efectivo', 'valor' => $i18n['efectivo']),
            array('id' => 'banco', 'valor' => $i18n['banco']),
            array('id' => 'cheque', 'valor' => $i18n['cheque']),
            array('id' => 'retencion', 'valor' => $i18n['retencion'])
        ));
        marcas($i18n[226], '20', array(
            array('titulo' => $i18n[168], 'valor' => $datos['compartida'], 'nombre' => 'compartida', 'ayuda_puntual' => $i18n[166])));
        marcas($i18n[518], '20', array(
            array('titulo' => $i18n[519], 'valor' => $datos['estado'], 'nombre' => 'estado', 'ayuda_puntual' => $i18n[520])));
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[78]), array('valor' => $i18n[66])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    function validacion_configuraciones_cajas_alta(boton)
    {
        if (boton == '<?php echo $i18n[78]; ?>' && $("#marco_configuraciones_cajas_alta input[name='nombre']").val() == '') {
<?php script_validacion_flotante('alerta', $i18n[119], "input[name='nombre']"); ?>

            return false;
        } else {
            return true;
        }
    };
</script>
