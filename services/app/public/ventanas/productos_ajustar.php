<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('entero', 'idrubro'),
        array('entero', 'idproveedor'),
        array('porcentaje', 'porcentaje'),
        array('moneda', 'moneda'),
        array('texto', 'modo'),
        array('texto', 'solapa'),
        array('moneda', 'ultimodolar'),
        array('moneda', 'nuevodolar'),
        array('entero', 'idlistaprecio'),
        array('texto', 'composicion_modo')
    ));
} else {
    $datos = array();
    $datos['ultimodolar'] = campo_sql(consulta_sql("SELECT valor FROM variables WHERE variable = 'ultimodolar' LIMIT 1"), 0);
}

$selector_modo = obtener_modo_ajuste();
$listas = obtener_listas(true);
$listasFiltradas = array_filter($listas, function ($lista) {
    return $lista['id'] !== '';
});
$id_monedas = array_unique(array_column($listasFiltradas, 'idmoneda'));
if (count($id_monedas) > 1) {
    // Temporariamente sólo listas de precios en pesos
    $listas = array_filter($listas, function ($lista) {
        return $lista['idmoneda'] == 1;
    });
}

switch ($boton) {
    case $i18n[12]:    // Aceptar
        if (($datos['solapa'] == 'porcentaje' && !$datos['porcentaje'])
            || ($datos['solapa'] == 'moneda' && !$datos['moneda'])
            || ($datos['solapa'] == 'dolar' && ((float) $datos['ultimodolar'] == 0 || (float) $datos['nuevodolar'] == 0))) {
            mensajes_alta($i18n[122]);
            $boton = false;
        } elseif ($datos['idlistaprecio'] && !contar_sql(consulta_sql("SELECT idproducto FROM precios WHERE idlista = '".$datos['idlistaprecio']."'"))) {
            mensajes_alta($i18n[194]);
            $boton = false;
        } else {

            $where = array();
            if ($datos['idrubro']) {
                $where[] = "productos.idrubro = '" . $datos['idrubro'] . "'";
            }
            if ($datos['idproveedor']) {
                $where[] = "productos.idproveedor = '" . $datos['idproveedor'] . "'";
            }
            if ($datos['solapa'] != 'combo' && $datos['modo'] == 'costo_precio' || $datos['modo'] == 'costo_utilidad') {
                $where[] = "productos.costo > 0";
            }
            if (count($id_monedas) > 1) {
                // Temporariamente sólo en pesos
                $where[] = "productos.idmoneda = 1";
            }

            switch ($datos['solapa']) {
                case 'porcentaje':
                    $calculo = " * " . ($datos['porcentaje'] / 100 + 1);
                    break;
                case 'moneda':
                    $calculo = " + " . $datos['moneda'];
                    break;
                case 'dolar':
                    $calculo = " * " . ($datos['nuevodolar'] / $datos['ultimodolar']);
                    break;
                case 'combo':
                    $combos = ajustar_combo();
                    break;
            }

            if ($datos['solapa'] == 'combo') {
                foreach ($combos as $idcombo => $combo) {
                    $combo['idcombo'] = $idcombo;
                    ajuste_masivo($datos, $combo, $where);
                }
            } else {
                ajuste_masivo($datos, $calculo, $where);
            }
            consulta_sql("UPDATE variables SET valor = '{$datos['nuevodolar']}' WHERE variable = 'ultimodolar' LIMIT 1");
            mensajes_alta($i18n[108], 'Confirmacion');
            ir_atras();
        }
        break;

    case $i18n[13]:    // Cancelar
        ir_atras();
        break;
}

ventana_inicio($i18n[94]);
{
    // Productos afectados
    contenido_inicio($i18n[95], '100', false, false, $i18n[110]);
    {
        if (count($id_monedas) > 1) {
            texto('info', false, 'El ajuste se realizará únicamente en productos y listas de precios en Pesos.', 'auto', false, 'alerta');
            bajo_linea();
        }

        selector_familiar('idrubro', $i18n[8], '', '25', 'categorias_rubros', false, $i18n[96], true);
        selector('idproveedor', $i18n[109], '', '20', 'proveedores', 'nombre', false, $i18n[97]);
        selector_array('idlistaprecio', $i18n[188], false, '20', $listas);
        bloque_inicio('modo');
        {
            selector_array('modo', $i18n[101], false, '35', $selector_modo);
        }
        bloque_fin();
    }
    contenido_fin();

    solapas_inicio(array(array('nombre' => $i18n[98]), array('nombre' => $i18n[107]), array('nombre' => $i18n[118]), array('nombre' => $i18n[201])));
    {
        entrada('hidden', 'solapa', '', '', 'auto', false, false, 'id="solapa"');

        // Por porcentaje
        solapa_inicio($i18n[98]);
        {
            contenido_inicio();
            {
                entrada('porcentaje', 'porcentaje', $i18n[99], '', '18');
                bajo_linea();
                texto('italica', false, $i18n[119], '80');
            }
            contenido_fin();
        }
        solapa_fin();

        // Por importe
        solapa_inicio($i18n[107]);
        {
            contenido_inicio();
            {
                entrada('monedanegativa', 'moneda', $i18n[100], '', '18');
                bajo_linea();
                texto('italica', false, $i18n[120], '80');
            }
            contenido_fin();
        }
        solapa_fin();

        // Por cotización del dolar
        solapa_inicio($i18n[118]);
        {
            contenido_inicio();
            {
                entrada('moneda', 'ultimodolar', $i18n[123], $datos['ultimodolar'], '18');
                entrada('moneda', 'nuevodolar', $i18n[124], '', '18');
                texto('italica', false, $i18n[121], '62');
            }
            contenido_fin();
        }
        solapa_fin();

        // Por composición
        solapa_inicio($i18n[201]);
        {
            contenido_inicio();
            {
                $temp_selector_composicion = array(
                    array('id' => 'composicion_utilidad', 'valor' => $i18n[256]),
                    array('id' => 'composicion_precio', 'valor' => $i18n[257]),
                );
                selector_array('composicion_modo', $i18n[255], false, '50', $temp_selector_composicion);
                texto('italica', false, $i18n[202], 'auto', false, false, false, 'id="texto-composicion"');
                texto('texto', false, $i18n[203], false, false, 'alerta');
            }
            contenido_fin();
        }
        solapa_fin();
    }
    solapas_fin();

    include 'backup.php';

    botones(array(array('valor' => $i18n[12], 'opciones' => 'id = "aceptar-proceso" disabled'), array('valor' => $i18n[13])));
}
ventana_fin();
?>
<script type="text/javascript" charset="utf-8">
    var idcombo = '<?php if($datos["combo"]) echo $id; ?>';

    function validacion_productos_ajustar(boton) {
        if ($("input[name=porcentaje]").is(":visible"))
            $("#solapa").val('porcentaje');
        else if ($("input[name=nuevodolar]").is(":visible"))
            $("#solapa").val('dolar');
        else if ($("input[name=moneda]").is(":visible"))
            $("#solapa").val('moneda');
        else
            $("#solapa").val('combo');

        if (boton == "<?php echo $i18n[12];?>") {
            return confirm("<?php echo $i18n[102];?>");
        } else {
            return true;
        }
    }

    $("li").click(function() {
        if ($(this).text() == '<?php echo $i18n[201]; ?>') {
            $("#modo").hide();
            if (isProcesoChecked()) {
                $("input[value=<?php echo $i18n[12];?>]").prop("disabled", false);
            }
        } else {
            $("#modo").show();
            checkComposicionPrecio();
        }
    });

    $("select[name='modo']").change(function() {
        checkComposicionPrecio();
    });

    $("select[name='idlistaprecio']").change(function() {
        checkComposicionPrecio();
    });

    $("select[name='composicion_modo']").change(function() {
        if ($("select[name='composicion_modo'] option:selected").val() === 'composicion_precio') {
            $("#texto-composicion .italica").html('<?php echo $i18n[258]; ?>');
        } else {
            $("#texto-composicion .italica").html('<?php echo $i18n[202]; ?>');
        }
    });

    const isProcesoChecked = () => {
        let proceso = $("#proceso").attr('checked');
        return proceso ? true : false;
    }

    const checkComposicionPrecio = (mensaje = true) => {
        if ($("select[name='modo'] option:selected").val() === 'precio_costo' && !$("select[name='idlistaprecio'] option:selected").val()) {
            if (mensaje) {
                alert('<?php echo $i18n[195];?>');
            }
            $("input[value=<?php echo $i18n[12];?>]").prop("disabled", true);
            return false;
        } else if (isProcesoChecked()) {
            $("input[value=<?php echo $i18n[12];?>]").prop("disabled", false);
            return true;
        }
        return false;
    }
</script>
