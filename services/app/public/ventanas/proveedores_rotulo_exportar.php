<?php
require __DIR__.'/../../../acc/acc.php';
require '../librerias/funciones_modelo.php';
require '../librerias/funciones_exportar.php';
recuperar_sesion();
$bd_link = conectar_db();
sesion();

$id = recibir_variable('texto', 'id', true);
$a = 'prn';
$proveedor = array_sql(consulta_sql("SELECT proveedores.nombre, cuit, contacto, domicilio, telefonos, proveedores.idlocalidad,
    categorias_localidades.idprovincia, categorias_localidades.nombre AS localidad, categorias_localidades.codigopostal,
    tablas_provincias.nombre AS provincia
    FROM proveedores
    LEFT JOIN categorias_localidades ON proveedores.idlocalidad = categorias_localidades.idlocalidad
    LEFT JOIN tablas_provincias ON categorias_localidades.idprovincia=tablas_provincias.idprovincia
    WHERE idproveedor = '".$id."' LIMIT 1"));

exportar('<html>
<head>
    <title>Impresión del rótulo para el proveedor '.$proveedor['nombre'].'</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
</head>

<body onload="window.print();">
<div style="text-align: center;">
<p><font size="+4">
<b><u>DESTINATARIO</u></b><br>
'.$proveedor['nombre'].'<br>
'.$proveedor['contacto'].'<br>
');

if ($proveedor['cuit'])
    exportar('CUIT: ' . convertir_numero($proveedor['cuit'], 'cuit-dni').'<br>'
);
exportar($proveedor['domicilio'].'<br>
');
if ($proveedor['idlocalidad'])
    exportar($proveedor['localidad'].'<br>
');
if ($proveedor['idprovincia'])
    exportar($proveedor['provincia'].'<br>
');
if ($proveedor['codigopostal'])
    exportar($proveedor['codigopostal'].'<br>
');
if ($proveedor['telefonos'])
    exportar($proveedor['telefonos'].'<br>
');
exportar('
<hr>
</font><font size="+3">
<b><u>REMITENTE</u></b><br>
'.$_SESSION['usuario_nombrereal'].'<br>
'.$_SESSION['empresa_nombre'].'<br>
');
if ($_SESSION['configuracion_cuit'])
    exportar('CUIT: ' . convertir_numero($_SESSION['configuracion_cuit'], 'cuit-dni').'<br>
');
exportar($_SESSION['configuracion_domicilio'].'<br>
');
if ($_SESSION['configuracion_idlocalidad'])
    exportar($_SESSION['configuracion_localidad'].'<br>
');
if ($_SESSION['configuracion_provincia'])
    exportar($_SESSION['configuracion_provincia'].'<br>
');
if ($_SESSION['configuracion_telefonos'])
    exportar($_SESSION['configuracion_telefonos'].'<br>
');
exportar('
</font></p>
</div>
</body>
</html>');
