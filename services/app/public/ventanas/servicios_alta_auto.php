<?php
$idcliente = recibir_variable('entero', 'idcliente', true);

if (!contar_sql(consulta_sql("SELECT estado FROM clientes WHERE idcliente='".$idcliente."' AND estado='1' LIMIT 1"))) {
    mensajes_alta($i18n[79]);
    ir_atras();

} else {
    guardar_sql('servicios', [
        'idcliente' => $idcliente,
        'idusuario' => $_SESSION['usuario_idusuario'],
        'idtiposervicio' => $_SESSION['control_ultimotiposervicio'],
        'prioridad' => '3',
        'fechasolicitado' => isset($_GET['fechasolicitado']) ? recibir_variable('fecha', 'fechasolicitado', true) : fecha_sql('ahora'),
        'titulo' => isset($_GET['titulo']) ? escape_sql(recibir_variable('texto', 'titulo', true)) : '',
    ]);

    $id = id_sql();
    fullsearch_insert('servicios',$id);
    ir_ahora('servicios.php?a=mod&id='.$id);
}
