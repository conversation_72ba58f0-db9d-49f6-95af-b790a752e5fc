<?php

switch ($boton) {
    case $i18n[233]: // Procesar atenciones
        $datos = recibir_matriz(array(
        array('texto', 'eliminar_ventas'),
        array('texto', 'atencion_ventas'),
        array('texto', 'atencion_servicios'),
        array('texto', 'atencion_bienes')
    ));

        if ($_SESSION['perfil_ventas_alta'] && $datos['atencion_ventas']) {
            $array_sql = array_all_sql(consulta_sql(
                "SELECT idventa
                FROM ventas
                WHERE estado = 'abierto' AND idcliente = '$id'
                AND idtipoventa IN (SELECT idtipoventa FROM categorias_ventas WHERE tipofacturacion != 'electronico')"));
            //Me parece como imposible no recorrer el array de la query
            foreach ($array_sql as $temp_array_sql) {
                $idventas[] = $temp_array_sql['idventa'];
            }
            $ventas = implode(', ', $idventas);
            if ($ventas) {
                consulta_sql("DELETE FROM productosxventas WHERE idventa IN (".$ventas.")");
                consulta_sql("DELETE FROM tributosxventas WHERE idventa IN (".$ventas.")");
                consulta_sql("DELETE FROM ventasxventas WHERE idventa IN (".$ventas.")");
                consulta_sql("DELETE FROM ventasxventas WHERE idrelacion IN (".$ventas.")");
                consulta_sql("DELETE FROM ventas WHERE idventa IN (".$ventas.")");
                $resultado_sql = consulta_sql("SELECT idventapago FROM ventaspagos WHERE idventa IN (".$ventas.")");
                while ($pago = array_sql($resultado_sql)) {
                    consulta_sql("UPDATE ventaspagos SET idventa = '0' WHERE idventapago = '".$pago['idventapago']."' LIMIT 1");
                    consulta_sql("UPDATE ventasxclientes SET numero = '' WHERE idtipoventa = '0' AND id = '".$pago['idventapago']."' LIMIT 1");
                }
            }
            consulta_sql("UPDATE ventas SET estado='anulado' WHERE idcliente = '$id' AND estado='abierto'");
        }

        if ($_SESSION['perfil_ventas_alta'] && $datos['eliminar_ventas']) {
            $array_sql = array_all_sql(consulta_sql(
                "SELECT idventa
                FROM ventas
                WHERE estado = 'abierto' AND idcliente = '$id'"));
            foreach ($array_sql as $temp_array_sql) {
                $idventas[] = $temp_array_sql['idventa'];
            }
            $ventas = implode(', ', $idventas);
            if ($ventas) {
                consulta_sql("DELETE FROM productosxventas WHERE idventa IN (".$ventas.")");
                consulta_sql("DELETE FROM ventasxventas WHERE idventa IN (".$ventas.")");
                consulta_sql("DELETE FROM ventasxventas WHERE idrelacion IN (".$ventas.")");
                consulta_sql("DELETE FROM ventas WHERE idventa IN (".$ventas.")");
                $resultado_sql = consulta_sql("SELECT idventapago FROM ventaspagos WHERE idventa IN (".$ventas.")");
                while ($pago = array_sql($resultado_sql)) {
                    consulta_sql("UPDATE ventaspagos SET idventa = '0' WHERE idventapago = '".$pago['idventapago']."' LIMIT 1");
                    consulta_sql("UPDATE ventasxclientes SET numero = '' WHERE idtipoventa = '0' AND id = '".$pago['idventapago']."' LIMIT 1");
                }
            }
        }

        if ($_SESSION['perfil_servicios_mod'] && $datos['atencion_servicios']) {
            consulta_sql("UPDATE servicios SET estado='5' WHERE idcliente = '$id' AND (estado='1' OR estado='2' OR estado='4')"
                .(!$_SESSION['perfil_servicios_mod_todos']
                    ? " AND idusuario = '".$_SESSION['usuario_idusuario']."'"
                    : ""
                ));
        }

        if ($_SESSION['perfil_bienes_mod'] && $datos['atencion_bienes']) {
            consulta_sql("UPDATE bienesxservicios SET fechafin='".date("Y-m-d H:i")."' WHERE fechafin='0000-00-00 00:00:00' AND idservicio IN (SELECT idservicio FROM servicios WHERE idcliente = '$id')");
        }
        ir_ahora('clientes.php?a=ver&id='.$id);
        break;
}

controlar_saldo('clientes', $id);

$cliente = array_sql(consulta_sql(
    "SELECT clientes.*,
        categorias_localidades.nombre AS localidad, categorias_localidades.codigopostal,
        tablas_condiciones.nombre AS tipoiva,
        tablas_provincias.nombre AS provincia,
        listas.idlista, listas.nombre AS lista, saldos.saldo,
        usuarios.nombre AS usuario,
        monedas.nombre AS moneda, monedas.simbolo
    FROM clientes
        LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
        LEFT JOIN tablas_condiciones ON clientes.idtipoiva = tablas_condiciones.idtipoiva
        LEFT JOIN categorias_clientes ON clientes.idtipocliente = categorias_clientes.idtipocliente
        LEFT JOIN listas ON categorias_clientes.idlista = listas.idlista
        LEFT JOIN saldos ON saldos.tiporelacion = 'clientes' AND clientes.idcliente = saldos.idrelacion
        LEFT JOIN tablas_provincias ON categorias_localidades.idprovincia = tablas_provincias.idprovincia
        LEFT JOIN usuarios ON clientes.idusuario = usuarios.idusuario
        LEFT JOIN monedas ON clientes.idmoneda = monedas.idmoneda
    WHERE idcliente = '$id'
   "));
$tipocliente = array_sql(consulta_sql("SELECT * FROM categorias_clientes WHERE idtipocliente = '".$cliente['idtipocliente']."' LIMIT 1"));
$tienda = array_sql(consulta_sql("SELECT ML_estado, MP_estado, ML_access_token, ML_user_id FROM tienda LIMIT 1"));

$cliente = controlar_moneda('clientes', $cliente);

obsrecordatorio($cliente['obsrecordatorio']);

mensajes_efimeros();

ventana_inicio($i18n[35].$nombre, '100', array(
    array('tipo' => 'imagen', 'url' => 'clientes.php?a=mod&id='.$id, 'a' => 'mod', 'title' => $i18n[34], 'permiso' => 'clientes_mod'),
    array('tipo' => 'imagen', 'url' => 'clientes.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[124], 'permiso' => 'clientes_baja', 'opciones' => 'onclick="return confirma('."'$i18n[171]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[169])));
{
    // Datos de contacto
    contenido_inicio($i18n[68], '50');
    {
        $temp_estado = (($cliente['estado']) ? 'habilitado' : 'deshabilitado');
        texto('texto', $i18n[38], $i18n[$temp_estado], 'auto', false, $temp_estado);
        texto('texto', $i18n[11], $id);
        texto('texto', $i18n[41], $cliente['nombre']);
        texto('texto', $i18n[42], $cliente['contacto']);
        texto('mail', $i18n[44], $cliente['mail']);
        texto('texto', $i18n[43], $cliente['telefonos'], 'auto', false, ($cliente['telefonos_verificado'] ? 'habilitado' : false));
        texto('texto', $i18n[45], $cliente['domicilio']);
        texto('texto', $i18n[46], $cliente['localidad']);
        texto('texto', $i18n[324], $cliente['provincia']);
        texto('texto', $i18n[325], ($cliente['codigopostal'] ? $cliente['codigopostal'] : $i18n[108]));
        enlaces('', array(array('valor' => $i18n[295], 'url' => 'ventanas/clientes_rotulo_exportar.php?id='.$id, 'opciones' => 'target="_blank"')));
    }
    contenido_fin();

    // Datos de facturación
    contenido_inicio($i18n[69], '50');
    {
        texto('texto', $i18n[47], $cliente['tipoiva']);

        if (strlen($cliente['cuit']) == 11) {
            texto('texto', $i18n[48], $cliente['razonsocial']);
            texto('cuit', $i18n[49], $cliente['cuit'], 'auto', false, false, false,
            ($cliente['cuit'] ? ['clipboard' => $cliente['cuit']] : []));
            texto('url', false, $i18n[189], 'auto', 'https://seti.afip.gob.ar/padron-puc-constancia-internet/ConsultaConstanciaAction.do');

        } else if ($cliente['dni']) {
            texto('texto', $i18n[48], $cliente['razonsocial']);
            texto('texto', $i18n[337], tipoDocs($cliente['tipodoc']));
            texto('text', $i18n[183], $cliente['dni']);
        }
    }
    contenido_fin();

    // Categoría del cliente
    contenido_inicio($i18n[50].': '.$tipocliente['nombre'], '50');
    {
        texto('texto', $i18n[319], $cliente['lista']);
        if ($tipocliente['cuentacorriente']) {
            $temp_cuentacorriente = $i18n[52];
            if ($tipocliente['maxcuentacorriente'] > 0) {
                $temp_cuentacorriente = $temp_cuentacorriente.' ('.$i18n[54].': $ '.formatear_numero($tipocliente['maxcuentacorriente']).')';
            } else {
                $temp_cuentacorriente = $temp_cuentacorriente.' ('.$i18n[218].')';
            }
        } else {
            $temp_cuentacorriente = $i18n[53];
        }
        texto('texto', $i18n[51], $temp_cuentacorriente);

        if ($tipocliente['descuento']) {
            $temp_descuento = $tipocliente['descuento'].'%';
        } else {
            $temp_descuento = $i18n[56];
        }

        if ($tipocliente['pagacosto'] == 1) {
            $temp_descuento = $temp_descuento.' ('.$i18n[57].')';
        }
        texto('texto', $i18n[55], $temp_descuento);
    }
    contenido_fin();

    salto_linea();

    // Vendedor
    contenido_inicio($i18n[334], '50');
    {
        texto('texto', $i18n[118], $cliente['usuario']);
    }
    contenido_fin();

    contenido_inicio($i18n[330], '50');
    {
        texto('titulo', $i18n[330], $cliente['moneda'].' ('.$cliente['simbolo'].')');
    }
    contenido_fin();


    if ($_SESSION['modulo_ML'] && ($tienda['ML_estado'] || $tienda['MP_estado'])) {
        // Integración con MercadoLibre
        contenido_inicio($i18n[184], '50', false, false, false, false, 'mini-icono-ml');
        {
            texto('url', $i18n[185], $cliente['ML_user_id'], 'auto', ($cliente['ML_user_id'] ? 'https://perfil.mercadolibre.com.ar/profile/showProfile?id='.$cliente['ML_user_id'].'&role=buyer' : ''));
            texto('url', $i18n[186], $cliente['ML_nickname'], 'auto', ($cliente['ML_nickname'] ? 'http://perfil.mercadolibre.com.ar/'.$cliente['ML_nickname'] : ''));
            texto('url', $i18n[321], $cliente['ML_user_id2'], 'auto', ($cliente['ML_user_id2'] ? 'https://perfil.mercadolibre.com.ar/profile/showProfile?id='.$cliente['ML_user_id2'].'&role=buyer' : ''), false, $i18n[321]);
            if (!$tienda['ML_access_token'] || !$tienda['ML_user_id']) {
                enlaces('', array(
                    array('valor' => $i18n[299], 'url' => 'configuraciones.php?a=ml')
                ));
            }
        }
        contenido_fin();
    }

    extras_ver();

    // Observaciones de la categoría del cliente
    if ($tipocliente['observacion']) {
        contenido_inicio($i18n[58]);
        {
            observacion('', $tipocliente['observacion']);
        }
        contenido_fin();
    }

    // Observaciones
    if ($cliente['observacion']) {
        contenido_inicio($i18n[59]);
        {
            observacion(false, $cliente['observacion']);
        }
        contenido_fin();
    }

    // Observaciones internas
    if ($cliente['obsinterna']) {
        contenido_inicio($i18n[60]);
        {
            observacion(false, $cliente['obsinterna']);
        }
        contenido_fin();
    }

    // Advertencias y pendientes para el próximo servicio
    if ($cliente['obsrecordatorio']) {
        contenido_inicio($i18n[61]);
        {
            observacion(false, $cliente['obsrecordatorio']);
        }
        contenido_fin();
    }

    $imagenes_sql = consulta_sql("SELECT iua FROM archivos WHERE tipo = 'imagen' AND modulo = 'clientes' AND publico = '1' AND id = '$id' LIMIT 6");
    if (contar_sql($imagenes_sql)) {
        contenido_inicio($i18n[61]);
        {
            while ($imagen = array_sql($imagenes_sql))
                miniatura(URL_S3.$imagen['iua'].'/miniatura');
        }
        contenido_fin();
    }

    // Situación del cliente
    contenido_inicio($i18n[225], '50');
    {
        $temp_situacion = array_sql(consulta_sql(
            "SELECT
                (SELECT COALESCE(SUM(ventasxclientes.total), 0) FROM ventasxclientes WHERE idcliente = '$id' AND idtipoventa > '0') AS facturado,
                (SELECT fecha FROM ventas WHERE idcliente = '$id' ORDER BY fecha ASC LIMIT 1) AS desde,
                (SELECT fecha FROM ventas WHERE idcliente = '$id' ORDER BY fecha DESC LIMIT 1) AS hasta"));
        if ($temp_situacion['desde']) {
            texto('fecha', $i18n[228], $temp_situacion['desde']);
            texto('fecha', $i18n[229], $temp_situacion['hasta']);
        }

        texto('moneda', $i18n[232], ($temp_situacion['facturado'] ? $temp_situacion['facturado'] : 0), false, false, false, false, false, $cliente['simbolo']);
        texto('moneda', $i18n[226], $cliente['saldo'], false, false, false, false, false, $cliente['simbolo']);
    }
    contenido_fin();

    // Atención rápida
    contenido_inicio($i18n[234]);
    {
        $mostrar_boton = false;
        if ($_SESSION['modulo_ventas'] && $_SESSION['perfil_ventas_ver']) {
            $resultado_sql = consulta_sql("SELECT idventa, numero, categorias_ventas.letra, categorias_ventas.puntodeventa FROM ventas INNER JOIN categorias_ventas ON ventas.idtipoventa=categorias_ventas.idtipoventa WHERE idcliente = '$id' AND ventas.estado='abierto'");
            if (contar_sql($resultado_sql)) {
                $mostrar_boton = true;
                $temp_enlaces = array();
                while ($temp_array = array_sql($resultado_sql)) {
                    $temp_enlaces[] = array('valor' => $temp_array['letra'].completar_numero($temp_array['puntodeventa'], 5).'-'.completar_numero($temp_array['numero'], 8), 'url' => 'ventas.php?a=ver&id='.$temp_array['idventa']);
                }
                enlaces($i18n[227], $temp_enlaces);
                if ($_SESSION['perfil_ventas_alta']) {
                    marcas('', 'auto', array(array('nombre' => 'atencion_ventas', 'titulo' => $i18n[236], 'ayuda_puntual' => $i18n[301])));
                    marcas('', 'auto', array(array('nombre' => 'eliminar_ventas', 'titulo' => $i18n[300])));
                }
            }
        }

        if ($_SESSION['modulo_servicios'] && $_SESSION['perfil_servicios_ver']) {
            $resultado_sql = consulta_sql("SELECT idservicio FROM servicios WHERE idcliente = '$id' AND (estado='1' OR estado='2' OR estado='4')");
            if (contar_sql($resultado_sql)) {
                $mostrar_boton = true;
                $temp_enlaces = array();
                while ($temp_array = array_sql($resultado_sql)) {
                    $temp_enlaces[] = array('valor' => $temp_array['idservicio'], 'url' => 'servicios.php?a=ver&id='.$temp_array['idservicio']);
                }
                enlaces($i18n[230], $temp_enlaces);
                if ($_SESSION['perfil_servicios_mod']) {
                    marcas('', 'auto', array(array('nombre' => 'atencion_servicios', 'titulo' => $i18n[237],
                        'ayuda_puntual' => (!$_SESSION['perfil_servicios_mod_todos'] ? $i18n[302] : '')
                        )));
                }
            }
        }

        if ($_SESSION['modulo_bienes'] && $_SESSION['perfil_bienes_ver']) {
            $resultado_sql = consulta_sql("SELECT bienesxservicios.idservicio, nombre FROM bienesxservicios INNER JOIN servicios ON bienesxservicios.idservicio=servicios.idservicio WHERE servicios.idcliente = '$id' AND bienesxservicios.fechafin='0000-00-00 00:00:00'");
            if (contar_sql($resultado_sql)) {
                $mostrar_boton = true;
                $temp_enlaces = array();
                while ($temp_array = array_sql($resultado_sql)) {
                    $temp_enlaces[] = array('valor' => $temp_array['nombre'], 'url' => 'servicios.php?a=ver&id='.$temp_array['idservicio']);
                }
                enlaces($i18n[231], $temp_enlaces);
                if ($_SESSION['perfil_bienes_mod']) {
                    marcas('', 'auto', array(array('nombre' => 'atencion_bienes', 'titulo' => $i18n[238])));
                }
            }
        }
        if ($mostrar_boton)
            botones(array(array('valor' => $i18n[233])));
    }
    contenido_fin();
}
ventana_fin();
