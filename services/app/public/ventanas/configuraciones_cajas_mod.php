<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('texto', 'nombre'),
        array('texto', 'compartida'),
        array('texto', 'tipo'),
        array('entero', 'estado'),
        array('fecha', 'fechacierre')
    ));
} else {
    $datos = array_sql(consulta_sql(
        "SELECT nombre, compartida, tipo, estado, cajas.fechacierre
        FROM categorias_cajas
        LEFT JOIN cajas
        ON cajas.idtipocaja = categorias_cajas.idtipocaja
        WHERE categorias_cajas.idtipocaja='".$id."'
        LIMIT 1
    "));
}

switch ($boton) {
    case $i18n[65]: //Aceptar
        consulta_sql("UPDATE categorias_cajas SET nombre = '".$datos['nombre']."', compartida = '".$datos['compartida']."', tipo = '".$datos['tipo']."', estado = '".$datos['estado']."' WHERE idtipocaja = '".$id."' LIMIT 1");
        if($datos['estado'] != '1' && $datos['fechacierre'] == 0){  //Si deshabilito, pongo fecha de cierre en la tabla campos, si no lo tiene
            consulta_sql("UPDATE cajas SET fechacierre = DATE('".date("Y-m-d H:i:s")."') WHERE idtipocaja = '".$id."'");
        }
        ir_atras();
        break;

    case $i18n[66]: //Cancelar
        ir_atras();
        break;
}

mensajes_efimeros();

ventana_inicio($i18n[190].$datos['nombre']);
{
    // Datos básicos
    contenido_inicio($i18n[89]);
    {
        entrada('hidden', 'fechacierre', false, $datos['fechacierre']);
        entrada('texto', 'nombre', $i18n[84], $datos['nombre'], '40', '60');
        selector_array('tipo', $i18n[513], $datos['tipo'], '20',
            array(
            array('id' => 'efectivo', 'valor' => $i18n['efectivo']),
            array('id' => 'banco', 'valor' => $i18n['banco']),
            array('id' => 'cheque', 'valor' => $i18n['cheque']),
            array('id' => 'retencion', 'valor' => $i18n['retencion'])
        ));
        marcas($i18n[518], '20', array(
            array('titulo' => $i18n[519], 'valor' => $datos['estado'], 'nombre' => 'estado', 'ayuda_puntual' => $i18n[520])));
        marcas($i18n[226], '20', array(
            array('titulo' => $i18n[168], 'valor' => $datos['compartida'], 'nombre' => 'compartida', 'ayuda_puntual' => $i18n[166])));
    }
    contenido_fin();
    botones(array(array('valor' => $i18n[65]), array('valor' => $i18n[66])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    function validacion_configuraciones_cajas_mod(boton)
    {
        if (boton == '<?php echo $i18n[65]; ?>' && $("#marco_configuraciones_cajas_mod input[name='nombre']").val() == '') {
<?php script_validacion_flotante('alerta', $i18n[119], "input[name='nombre']"); ?>

            return false;
        } else {
            return true;
        }
    };
</script>
