<?php
$filename = 'backup_'.$_SESSION['empresa_idempresa'].'_'.$modulo.'.sql';

$backupok = recibir_variable('texto', 'backup', true);
$vaciarok = recibir_variable('texto', 'vaciar', true);

if (!file_exists(PATH_ARCHIVOS.$filename)) {
    $cmd =  "mysqldump"
        . " -u" .   $_SESSION['servidor_user'].$_SESSION['empresa_idempresa']
        . " -p" .   md5($_SESSION['servidor_pass'].$_SESSION['empresa_idempresa'])
        . " -h " .  $_SESSION['servidor_host']
        . " "   .   $_SESSION['servidor_bd'].$_SESSION['empresa_idempresa']
        . " " .     $modulo . " > " . PATH_ARCHIVOS . $filename;
    passthru($cmd);
    if($modulo == 'productos'){
        $filename_precios = 'backup_'.$_SESSION['empresa_idempresa'].'_precios.sql';
        $cmd =  "mysqldump"
        . " -u" .   $_SESSION['servidor_user'].$_SESSION['empresa_idempresa']
        . " -p" .   md5($_SESSION['servidor_pass'].$_SESSION['empresa_idempresa'])
        . " -h " .  $_SESSION['servidor_host']
        . " " .     $_SESSION['servidor_bd'].$_SESSION['empresa_idempresa']
        . " precios > " . PATH_ARCHIVOS . $filename_precios;
        passthru($cmd);

        $filename_stock = 'backup_'.$_SESSION['empresa_idempresa'].'_stock.sql';
        $cmd =  "mysqldump"
        . " -u" .   $_SESSION['servidor_user'].$_SESSION['empresa_idempresa']
        . " -p" .   md5($_SESSION['servidor_pass'].$_SESSION['empresa_idempresa'])
        . " -h " .  $_SESSION['servidor_host']
        . " " .     $_SESSION['servidor_bd'].$_SESSION['empresa_idempresa']
        . " stock > " . PATH_ARCHIVOS . $filename_stock;
        passthru($cmd);
    }
}

$backup = date ("d/m/Y H:i:s.", filemtime(PATH_ARCHIVOS.$filename));

salto_linea();
bajo_linea();

//Resguardo CSV
contenido_inicio($i18n_funciones[118], '66');
{
    texto('italica', false, $i18n_funciones[115].($ventana == 'productos_ajustar' ? $i18n_funciones[314] : $i18n_funciones[315]).$i18n_funciones[316]);
    if ($modulo == 'productos') {
        selector('idlista', $i18n_funciones[152], '', '25', 'listas', false, false, false);
        selector('iddeposito', $i18n_funciones[153], '', '25', 'depositos', false, false, false);
        salto_linea();
        bajo_linea();
    }
    enlaces(false, array(array(
        'tipo'      => 'enlace',
        'valor'     => $i18n_funciones[117] . $modulo,
        'imagen'    => 'descargar',
        'opciones'  => 'id="url_exportar"',
        'url'       => URL_SCRIPTS.'/?script=exportar&modulo='.$modulo,
    )));
    marcas('', '100', array(array('nombre' => 'publico', 'titulo' => $i18n_funciones[116], 'valor' => '0', 'opciones' => 'id="proceso"')));
}
contenido_fin();

//Backup y restore SQL
contenido_inicio('<img class="com_masinfo_logo_afip" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/grabar.png">' . ' ' . $i18n_funciones[140], '33');
{
    texto('italica', false, $i18n_funciones[141] . $backup);
    if ($backupok)
        script_flotante('confirmacion', $i18n_funciones[144], '7000');

    enlaces(false, array(array(
        'tipo'      => 'url',
        'valor'     => $i18n_funciones[142] . $modulo,
        'url'       => URL_SCRIPTS.'/?script=restaurar&modulo='.$modulo,
        'opciones'  => 'id = "restaurar"',
        )));
}
contenido_fin();

if ($modulo == 'productos' && $ventana != 'productos_ajustar') {
    //Vaciar base de datos
    contenido_inicio($i18n_funciones[154] . $modulo, '33');
    {
        texto('italica', false, $i18n_funciones[155] . $modulo);
        if ($vaciarok)
            script_flotante('confirmacion', $i18n_funciones[160], '7000');

        enlaces(false, array(array(
            'tipo'      => 'url',
            'valor'     => $i18n_funciones[156],
            'url'       => URL_SCRIPTS.'/?script=vaciar&modulo='.$modulo,
            'opciones'  => 'id = "vaciar"',
        )));
    }
    contenido_fin();
}

?>
<script>
    var a = "<?php echo $a; ?>";

    $("#restaurar").click(function() {
        return confirma("<?php echo $i18n_funciones[143] . $modulo; ?>");
    });

    $("#vaciar").click(function() {
        if(confirma("<?php echo $i18n_funciones[157] . $modulo . '. ' . $i18n_funciones[158]; ?>")){
            desbloquear();
            return confirma("<?php echo $i18n_funciones[157] . $modulo . '. ' . $i18n_funciones[158]; ?>");
        } else {
            return false;
        }
    });

    $("select").change(function() {
        var idlista = $("select[name='idlista'] option:selected").val();
        var iddeposito = $("select[name='iddeposito'] option:selected").val();
        $("#url_exportar").attr('href', '<?php echo URL_SCRIPTS."/?script=exportar&modulo=".$modulo; ?>&idlista='+idlista+'&iddeposito='+iddeposito);
    });

    $("#proceso").click(function() {
        if ($(this).attr('checked') && (((a === 'ajustar' || a === 'ajustar2') && checkComposicionPrecio(false)) || a === 'importar')) { // TODO: quitar ajustar2
            $("#aceptar-proceso").prop( "disabled", false);
            $("#archivo").show();
        } else {
            $("#aceptar-proceso").prop( "disabled", true);
            $("#archivo").hide();
        }
    });
</script>
