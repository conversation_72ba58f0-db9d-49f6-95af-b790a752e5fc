<?php

$idtienda = recibir_variable('entero', 'idtienda', true);
$tiendas_sql = consulta_sql("SELECT * FROM tienda WHERE ML_access_token != '' ORDER BY idtienda");
$tienda = array_sql(consulta_sql(
    "SELECT * FROM tienda
    WHERE ML_access_token != ''
    ".($idtienda ? "AND idtienda = '$idtienda'" : "")
    ."ORDER BY idtienda LIMIT 1"));

if ($boton && $boton = $i18n_funciones['agregar']) {
    $marcas = recibir_marcas('pago_');
    $marcas_en_cola = [];
    foreach ($marcas as $marca) {
        $marcas_en_cola[] = $marca;
        $datos = [
            "topic"             => "payments",
            "application_id"    =>  ML_APP_ID,
            "user_id"           =>  $tienda['ML_user_id'],
            "resource"          =>  '/collections/'.$marca,
            "attempts"          =>  1,
            "sent"              =>  gmDate("Y-m-d\TH:i:s.000\Z"),
            "received"          =>  gmDate("Y-m-d\TH:i:s.000\Z"),
        ];
        queue_ml('notificaciones/mercadopago', json_encode($datos, JSON_UNESCAPED_SLASHES));
    }
    mensajes_alta($i18n[332].implode(', ', $marcas_en_cola), 'Confirmacion');
    ir_atras();
}

if ($tienda['ML_access_token'] && $tienda['MP_estado']) {
    $ventaspagos = array_all_sql(consulta_sql(
        "SELECT idventapago, ventaspagos.idcliente, fecha, MP_operation_id, clientes.nombre AS cliente,
            monedas.simbolo
        FROM ventaspagos
        LEFT JOIN clientes ON ventaspagos.idcliente = clientes.idcliente
        LEFT JOIN monedas ON ventaspagos.idmoneda = monedas.idmoneda
        WHERE fecha >= '".date("Y-m-d H:i:s")."' - INTERVAL ".(HORAS_PEDIDOS_ML*2)." HOUR
    "), 'MP_operation_id');

    $tienda['ML_access_token'] = refrescar_ml($tienda);

    if (!$tienda['ML_access_token']) {
        mensajes_alta($i18n_funciones[215]);
        ir_atras();
    }

    desconectar_db();

    $mp = new MP ($tienda['ML_access_token']);

    $offset = 50;
    $pagos = $mp->get(
        "/v1/payments/search",
        array(
            "begin_date"    => "NOW-".HORAS_PEDIDOS_ML."HOURS",
            "end_date"      => "NOW",
            "range"         => "date_created",
            "sort"          => "date_created",
            "criteria"      => "asc",
            "limit"         => 50,
            "status"        => "approved"
        )
    );

    if (isset($pagos['response']['paging']['total'])) {
        $total = $pagos['response']['paging']['total'];

        while ($total > $offset) {
            $temp_pagos = $mp->get(
            "/v1/payments/search",
                array(
                    "begin_date"    => "NOW-".HORAS_PEDIDOS_ML."HOURS",
                    "end_date"      => "NOW",
                    "range"         => "date_created",
                    "sort"          => "date_created",
                    "criteria"      => "asc",
                    "limit"         => 50,
                    "status"        => "approved",
                    "offset"        => $offset
                )
            );
            $pagos['response']['results'] = array_merge($pagos['response']['results'], $temp_pagos['response']['results']);
            $offset += 50;
        }
    } else {
        $pagos['response']['results'] = '';
    }
    echo '<script>
        desbloquear();
        </script>';
}

ventana_inicio($i18n[328]);
{

    if (contar_sql($tiendas_sql) > 1) {
        contenido_inicio();
        {
            $tiendas = array();
            puntero_sql($tiendas_sql);
            while ($temp_array = array_sql($tiendas_sql)) {
                $tiendas[] = array('id' => $temp_array['idtienda'], 'valor' => $temp_array['tienda_nombre']);
            }
            selector_array('idtienda', $i18n[323], $idtienda, '25', $tiendas, false, 'onchange="recargar_ventana_mercado('."'pagos_mp'".')"');

        }
        contenido_fin();
    }

    contenido_inicio();
    {
        linea_inicio('titulo');
        {
            marcas_todas('ventas_todas');
            celda('imagen', '', 'imagen');
            celda('texto', $i18n[52], '20');    // Fecha
            celda('texto', $i18n[183], '15');    // Operación de MP
            celda('texto', $i18n[35], '20');    // Cliente
            celda('texto', $i18n_funciones['ventaspagos_singular'], '20');    // Pago
            celda('texto', $i18n[7], '15');     // Precio final
        }
        linea_fin();

        foreach ($pagos['response']['results'] as $pago) {

            linea_inicio('fila', '', '', 'id="linea_'.$pago['id'].'"');
            {
                if (!array_key_exists($pago['id'], $ventaspagos)) {
                    $temp_estado = 'no';
                    $idcliente = '';
                    $cliente = $pago['payer']['first_name'].' '.$pago['payer']['last_name'];
                    $fecha = new DateTime($pago['date_created']);
                    $fecha = $fecha->format('d-m-Y H:i');
                    $idventapago = '';
                    $numero_ventapago = '-';

                } else {
                    $temp_estado = 'ok';
                    $idcliente = $ventaspagos[$pago['id']]['idcliente'];
                    $cliente = $ventaspagos[$pago['id']]['cliente'];
                    $fecha = $ventaspagos[$pago['id']]['fecha'];
                    $idventapago = $ventaspagos[$pago['id']]['idventapago'];
                    $numero_ventapago = 'RP'.completar_numero($idventapago, 8);
                }

                //$url_pago = 'https://www.mercadopago.com.ar/activities/'.$pago['id'].'/detail?type=fee_payment';

                marca('pago_'.$pago['id'], '', 'imagen', ($temp_estado == 'ok' ? 'disabled="disabled"' : ''));
                entrada('hidden', 'user_id_'.$pago['id'], '', $pago['payer']['id']);
                entrada('hidden', 'fecha'.$pago['id'], '', $pago['date_created']);
                celda('imagen', $i18n[$temp_estado], 'imagen', $temp_estado, '', 'title="'.($temp_estado == 'ok' ?  $i18n[334] : $i18n[333]).'"');
                celda('fechayhora', $fecha, '20');
                celda('texto', $pago['id'], '15');
                celda('texto', $cliente, '20', false, ($idcliente ? 'clientes.php?a=ver&id='.$idcliente : ''));
                celda('texto', $numero_ventapago, '20', false, ($idventapago ? 'ventas.php?a=verpago&id='.$idventapago : ''));
                celda('moneda', $pago['transaction_amount'], '15', false, false, false, false, $ventaspagos[$pago['id']]['simbolo']);
            }
            linea_fin();
        }

        if (!$tienda['MP_estado']) {
            texto('italica', '', $i18n[329], 'auto', false, 'info', false);
        } else if (!$total) {
            texto('italica', '', $i18n[330], 'auto', false, 'info', false);

        } else {
            texto('italica', '', $i18n[331], 'auto', false, 'info', false);
        }

    if ($total)
        botones(array(array('valor' => $i18n_funciones['agregar'])));
    }
    contenido_fin();

}
ventana_fin();
