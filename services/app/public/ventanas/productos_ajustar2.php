<?php
if ($boton) {
$datos = recibir_matriz(array(
        array('entero', 'idrubro'),
        array('entero', 'idproveedor'),
        array('porcentaje', 'porcentaje'),
        array('moneda', 'moneda_importe'),
        array('porcentaje', 'moneda_utilidad'),
        array('texto', 'modo'),
        array('texto', 'solapa'),
        array('moneda', 'ultimodolar'),
        array('moneda', 'nuevodolar'),
        array('entero', 'idlistaprecio'),
        array('texto', 'composicion_modo')
    ));
} else {
    $datos = array();
    $datos['ultimodolar'] = campo_sql(consulta_sql("SELECT valor FROM variables WHERE variable = 'ultimodolar' LIMIT 1"), 0);
}

switch ($boton) {
    case $i18n[12]:    // Aceptar
        if (($datos['solapa'] == 'porcentaje' && !$datos['porcentaje'])
            || ($datos['solapa'] == 'importe' && !$datos['moneda_importe'])
            || ($datos['solapa'] == 'utilidad' && !$datos['moneda_utilidad'])
            || ($datos['solapa'] == 'dolar' && ((float) $datos['ultimodolar'] == 0 || (float) $datos['nuevodolar'] == 0))
        ) {
            mensajes_alta($i18n[122]);
            $boton = false;
        } elseif ($datos['idlistaprecio'] && !contar_sql(consulta_sql("SELECT idproducto FROM precios WHERE idlista = '" . $datos['idlistaprecio'] . "'"))) {
            mensajes_alta($i18n[194]);
            $boton = false;
        } else {

            $where = array();
            if ($datos['idrubro']) {
                $where[] = "productos.idrubro = '" . $datos['idrubro'] . "'";
            }
            if ($datos['idproveedor']) {
                $where[] = "productos.idproveedor = '" . $datos['idproveedor'] . "'";
            }
            if (($datos['solapa'] != 'combo' && ($datos['modo'] == 'costo_precio' || $datos['modo'] == 'costo_utilidad'))) {
                $where[] = "productos.costo > 0";
            }

            switch ($datos['solapa']) {
                case 'porcentaje':
                    $calculo = " * " . ($datos['porcentaje'] / 100 + 1);
                    break;
                case 'importe':
                    $calculo = " + " . $datos['moneda_importe'];
                    break;
                case 'utilidad':
                    $calculo = $datos['moneda_utilidad'];
                    break;
                case 'dolar':
                    $calculo = " * " . ($datos['nuevodolar'] / $datos['ultimodolar']);
                    break;
                case 'combo':
                    $combos = ajustar_combo();
                    break;
            }

            if ($datos['solapa'] == 'combo') {
                foreach ($combos as $idcombo => $combo) {
                    $combo['idcombo'] = $idcombo;
                    ajuste_masivo($datos, $combo, $where);
                }
            } else {
                ajuste_masivo($datos, $calculo, $where);
            }
            consulta_sql("UPDATE variables SET valor = '{$datos['nuevodolar']}' WHERE variable = 'ultimodolar' LIMIT 1");
            mensajes_alta($i18n[108], 'Confirmacion');
            ir_atras();
        }
        break;

    case $i18n[13]:    // Cancelar
        ir_atras();
        break;
}

$listas = obtener_listas(true);
$selector_modo = obtener_modo_ajuste();

ventana_inicio($i18n[94]);
{
    // Productos afectados
    contenido_inicio($i18n[95], '100', false, false, $i18n[110]);
    {
        selector_familiar('idrubro', $i18n[8], '', '33', 'categorias_rubros', false, $i18n[96], true);
        selector('idproveedor', $i18n[109], '', '33', 'proveedores', 'nombre', false, $i18n[97]);
        selector_array('idlistaprecio', $i18n[188], false, '33', $listas);
    }
    contenido_fin();

    contenido_inicio($i18n[269]);
    {
        solapas_inicio([
            ['nombre' => $i18n[98]], ['nombre' => $i18n[107]], ['nombre' => $i18n[118]], ['nombre' => $i18n[201]], ['nombre' => $i18n[270]]
        ]);
        {
            entrada('hidden', 'solapa', '', '', 'auto', false, false, 'id="solapa"');
            entrada('hidden', 'modo', '', '', 'auto');

            // Por porcentaje
            solapa_inicio($i18n[98]);
            {
                contenido_inicio();
                {
                    selector_array('modo', $i18n[101], false, '40', $selector_modo);
                    salto_linea();
                    bajo_linea();
                    entrada('porcentaje', 'porcentaje', $i18n[99], '', '18');
                    bajo_linea();
                    texto('italica', false, $i18n[119], '80');
                }
                contenido_fin();
            }
            solapa_fin();

            // Por importe
            solapa_inicio($i18n[107]);
            {
                contenido_inicio();
                {
                    selector_array('modo', $i18n[101], false, '40', $selector_modo);
                    salto_linea();
                    bajo_linea();
                    entrada('monedanegativa', 'moneda_importe', $i18n[100], '', '18');
                    bajo_linea();
                    texto('italica', false, $i18n[120], '80');
                }
                contenido_fin();
            }
            solapa_fin();

            // Por cotización del dolar
            solapa_inicio($i18n[118]);
            {
                contenido_inicio();
                {
                    selector_array('modo', $i18n[101], false, '40', $selector_modo);
                    salto_linea();
                    bajo_linea();
                    entrada('moneda', 'ultimodolar', $i18n[123], $datos['ultimodolar'], '18');
                    entrada('moneda', 'nuevodolar', $i18n[124], '', '18');
                    texto('italica', false, $i18n[121], '62');
                }
                contenido_fin();
            }
            solapa_fin();

            // Por composición
            solapa_inicio($i18n[201]);
            {
                contenido_inicio();
                {
                    $temp_selector_composicion = array(
                        array('id' => 'composicion_utilidad', 'valor' => $i18n[256]),
                        array('id' => 'composicion_precio', 'valor' => $i18n[257]),
                    );
                    selector_array('composicion_modo', $i18n[255], false, '50', $temp_selector_composicion);
                    texto('italica', false, $i18n[202], 'auto', false, false, false, 'id="texto-composicion"');
                    texto('texto', false, $i18n[203], false, false, 'alerta');
                }
                contenido_fin();
            }
            solapa_fin();

            // Por utilidad
            solapa_inicio($i18n[270]);
            {
                contenido_inicio();
                {
                    $temp_selector_utilidad = [
                        ['id' => 'utilidad_precio', 'valor' => $i18n[271]],
                        ['id' => 'utilidad_costo', 'valor' => $i18n[272]],
                    ];
                    selector_array('modo', $i18n[101], false, '50', $temp_selector_utilidad);
                    salto_linea();
                    bajo_linea();
                    entrada('moneda', 'moneda_utilidad', $i18n[275], '', '18');
                    bajo_linea();
                    texto('italica', false, $i18n[273], '80');
                }
                contenido_fin();
            }
            solapa_fin();
        }
        solapas_fin();
    }
    contenido_fin();

    include 'backup.php';

    botones(array(array('valor' => $i18n[12], 'opciones' => 'id = "aceptar-proceso" disabled'), array('valor' => $i18n[13])));
}
ventana_fin();
?>
<script type="text/javascript" charset="utf-8">
    var idcombo = '<?php if ($datos["combo"]) echo $id; ?>';

    function getModo() {
        let modo = '';
        if ($("input[name=porcentaje]").is(":visible")) {
            $("#solapa").val('porcentaje');
            modo = 'por_porcentaje';
        } else if ($("input[name=nuevodolar]").is(":visible")) {
            $("#solapa").val('dolar');
            modo = 'por_cotización_del_dólar';
        } else if ($("input[name=moneda_utilidad]").is(":visible")) {
            $("#solapa").val('utilidad');
            modo = 'por_utilidad';
        } else if ($("input[name=moneda_importe]").is(":visible")) {
            $("#solapa").val('importe');
            modo = 'por_importe';
        } else {
            $("#solapa").val('combo');
        }
        return modo;
    }

    function validacion_productos_ajustar2(boton) { // TODO: renombrar ajustar2
        let modo = getModo();
        if (modo) {
            let selectedValue = $('#'+modo+' select[name="modo"]').val();
            $('#modo').val(selectedValue);
            $("select[name='modo'] option:selected").prop("disabled", true);
        }

        if (boton == "<?php echo $i18n[12]; ?>") {
            return confirm("<?php echo $i18n[102]; ?>");
        } else {
            return true;
        }
    }

    $("li").click(function() {
        if ($(this).text() == '<?php echo $i18n[276]; ?>') {
            alerta('<?php echo $i18n[268]; ?>', 'informacion')
        }
    });

    $("select[name='modo']").change(function() {
        checkComposicionPrecio();
    });

    $("select[name='idlistaprecio']").change(function() {
        checkComposicionPrecio();
    });

    $("select[name='composicion_modo']").change(function() {
        if ($("select[name='composicion_modo'] option:selected").val() === 'composicion_precio') {
            $("#texto-composicion .italica").html('<?php echo $i18n[258]; ?>');
        } else {
            $("#texto-composicion .italica").html('<?php echo $i18n[202]; ?>');
        }
    });

    const isProcesoChecked = () => {
        let proceso = $("#proceso").attr('checked');
        return proceso ? true : false;
    }

    const checkComposicionPrecio = (mensaje = true) => {
        let modo = getModo();
        let selectModo = $("#"+modo+" select[name='modo'] option:selected").val();

        if ((selectModo === 'precio_costo' || selectModo === 'utilidad_costo') && !$("select[name='idlistaprecio'] option:selected").val()) {
            if (mensaje) {
                alert('<?php echo $i18n[195]; ?>');
            }
            $("input[value=<?php echo $i18n[12]; ?>]").prop("disabled", true);

            return false;
        } else if (isProcesoChecked()) {
            $("input[value=<?php echo $i18n[12]; ?>]").prop("disabled", false);
            return true;
        }
        return false;
    }
</script>