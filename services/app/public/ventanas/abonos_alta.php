<?php
// if ($boton) {
//     $datos = recibir_matriz(array(
        array('entero', 'idcliente'),
        array('texto', 'clientes_id'),
        array('entero', 'idtipobien'),
        array('texto', 'nombre'),
        array('texto', 'codigo'),
        array('largo', 'observacion'),
        array('largo', 'obsinterna')
    ));
//     if ($datos['clientes_id'])
//         $datos['idcliente'] = $datos['clientes_id'];
//     else
//         $datos['idcliente'] = 0;

// } else {
//     $datos = array('idcliente' => 0);
// }

// if (!$datos['codigo']) {
//     if (!$proximoid = campo_sql(consulta_sql("SELECT idbien FROM bienes ORDER BY idbien DESC LIMIT 1"), 0) + 1)
//         $proximoid = 1;
//     while (contar_sql(consulta_sql("SELECT idbien FROM bienes WHERE codigo = '".completar_numero($proximoid, 5)."' LIMIT 1")))
//         $proximoid++;
//     $datos['codigo'] = completar_numero($proximoid, 5);

// } else {
//     $resultado_sql = consulta_sql("SELECT idbien, nombre FROM bienes WHERE codigo = '".$datos['codigo']."' AND idbien != '$id' LIMIT 1");

//     if ($boton == $i18n_funciones[27] && contar_sql($resultado_sql)) {
//         $temp_array = array_sql($resultado_sql);
//         mensajes_alta($i18n[32].'<a href="bienes.php?a=ver&id='.$temp_array['idbien'].'" class="enlace" target="_blank">'.$temp_array['nombre'].'</a>');
//         $boton = '';
//     }
// }

// switch ($boton) {
//     case $i18n_funciones[27]: //Agregar
//         consulta_sql("INSERT INTO bienes SET
//             idcliente = '".$datos['idcliente']."',
//             idtipobien = '".$datos['idtipobien']."',
//             nombre = '".$datos['nombre']."',
//             codigo = '".$datos['codigo']."',
//             observacion='".$datos['observacion']."',
//             obsinterna='".$datos['obsinterna']."'
//             ");
//         $id = id_sql();
//         extras_alta();
//         ir_ahora('bienes.php?a=ver&id='.$id);
//         break;

//     case $i18n_funciones[23]: // Cancelar
//         ir_atras();
//         break;
// }

ventana_inicio($i18n[1]);
{
    // Datos básicos
    contenido_inicio($i18n[5]);
    {
        entrada('texto', 'nombre', $i18n[2], $datos['nombre'], '50', '200');
        entrada('texto', 'codigo', $i18n[3], $datos['codigo'], '25', '60');
    }
    contenido_fin();

    // Datos de ejecución
    contenido_inicio($i18n[6], '100', false, false, $i18n[9]);
    {
        marcas('', 'auto', array(
            array('nombre' => 'estado', 'titulo' => $i18n[7], 'valor' => $datos['estado'])
            ));
        salto_linea();

        entrada('fecha', 'fechainicio', $i18n[10], $datos['fechainicio'], '15');
        entrada('fecha', 'fechafin', $i18n[11], $datos['fechafin'], '15');

        entrada('numeros', 'dia', $i18n_funciones['día'], $datos['dia'], '10');
        entrada('numeros', 'mes', $i18n_funciones['mes'], $datos['mes'], '10');
        entrada('numeros', 'año', $i18n_funciones['año'], $datos['año'], '10');

        marcas($i18n[12], '40', array(
            array('nombre' => 'lunes', 'titulo' => $i18n_funciones['lunes'], 'valor' => $datos['lunes']),
            array('nombre' => 'martes', 'titulo' => $i18n_funciones['martes'], 'valor' => $datos['martes']),
            array('nombre' => 'miércoles', 'titulo' => $i18n_funciones['miércoles'], 'valor' => $datos['miércoles']),
            array('nombre' => 'jueves', 'titulo' => $i18n_funciones['jueves'], 'valor' => $datos['jueves']),
            array('nombre' => 'viernes', 'titulo' => $i18n_funciones['viernes'], 'valor' => $datos['viernes']),
            array('nombre' => 'sábado', 'titulo' => $i18n_funciones['sábado'], 'valor' => $datos['sábado']),
            array('nombre' => 'domingo', 'titulo' => $i18n_funciones['domingo'], 'valor' => $datos['domingo']),
            ));
    }
    contenido_fin();

    // // Seleccione un cliente
    // contenido_inicio($i18n[25], '100', false, false, $i18n[26]);
    // {
    //     seleccionador('clientes');
    // }
    // contenido_fin();

    extras();

    // Observaciones
    contenido_inicio($i18n[8], '100', true, false);
    {
        area('observacion', $i18n[8], $datos['observacion']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n_funciones[27]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();
?>
<script type="text/javascript" charset="utf-8">
    function validacion_bienes_alta(boton)
    {
        if (boton == '<?php echo $i18n_funciones[27]; ?>' && $("#marco_bienes_alta input[name='nombre']").val() == '') {
<?php script_validacion_flotante("alerta", $i18n[28], "input[name='nombre']"); ?>

            return false;
        } else

            return true;
    };
</script>
