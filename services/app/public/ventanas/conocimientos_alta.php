<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('texto', 'titulo'),
        array('entero', 'idtipoconocimiento'),
        array('largo', 'texto')
    ));
} else {
    $datos = array('titulo' => '', 'idtipoconocimiento' => '', 'texto' => '');
}

switch ($boton) {
    case $i18n[9]: // Agregar
        consulta_sql("INSERT INTO conocimientos (titulo,texto,idtipoconocimiento) VALUES ('".$datos['titulo']."','".$datos['texto']."','".$datos['idtipoconocimiento']."')");
        $id = id_sql();
        fullsearch_insert('conocimientos',$id);
        extras_alta();
        ir_ahora('conocimientos.php');
        break;

    case $i18n[10]: // Cancelar
        ir_atras();
        break;
}

ventana_inicio($i18n[7]);
{
    // Datos básicos
    contenido_inicio($i18n[8]);
    {
        entrada('hidden', 'id_oculto', '', $datos['idconocimiento'], '30');
        entrada('texto', 'titulo', $i18n[2], $datos['titulo'], '50', '200');
        selector_familiar('idtipoconocimiento', $i18n[11], $datos['idtipoconocimiento'], '50', 'categorias_conocimientos', true, true, true);
        area('texto', $i18n[3], $datos['texto']);
    }
    contenido_fin();

    extras();

    botones(array(array('valor' => $i18n[9]), array('valor' => $i18n[10])));
}
ventana_fin();
