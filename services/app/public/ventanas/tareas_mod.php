<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('largo', 'texto'),
        array('texto', 'prioridad'),
        array('entero', 'idusuario')
    ));
} else {
    $datos = array_sql(consulta_sql("SELECT * FROM tareas WHERE idtarea='".$id."' LIMIT 1"));
}

switch ($boton) {
    case $i18n[27]: //Aceptar
        if ($datos && $datos['idusuario'] && $datos['prioridad'] && $datos['texto']) {
            consulta_sql("UPDATE tareas SET idusuario='".$datos['idusuario']."', prioridad='".$datos['prioridad']."', texto='".$datos['texto']."' WHERE idtarea='".$id."' LIMIT 1");
        }
        ir_atras();
        break;

    case $i18n[29]: //Cancelar
        ir_atras();
        break;
}

ventana_inicio($i18n[31], '100', array(
    array('url' => 'tareas.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[25]),
    array('url' => 'tareas.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[23], 'opciones' => 'onclick="return confirma('."'$i18n[43]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[26])));
{
    contenido_inicio($i18n[32]);
    {
        if ($_SESSION['perfil_tareas_alta_todos']) {
            selector('idusuario', $i18n[34], $datos['idusuario'], '33', 'usuarios', 'nombre', false, false, true);
        } else {
            selector_array('idusuario', $i18n[34], false, '33', array(array('id' => $_SESSION['usuario_idusuario'], 'valor' => $_SESSION['usuario_nombre'], 'selected' => 'selected')));
        }
        selector_array('prioridad', $i18n[35], $datos['prioridad'], '33', array(array('id' => '1', 'valor' => $i18n[36]), array('id' => '2', 'valor' => $i18n[37]), array('id' => '3', 'valor' => $i18n[38]), array('id' => '4', 'valor' => $i18n[39]), array('id' => '5', 'valor' => $i18n[40])));
        salto_linea();
        area('texto', $i18n[33], $datos['texto']);
    }
    contenido_fin();
    botones(array(array('valor' => $i18n[27]), array('valor' => $i18n[29])));
}
ventana_fin();
