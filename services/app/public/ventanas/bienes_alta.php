<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('entero', 'idcliente'),
        array('texto', 'clientes_id'),
        array('entero', 'idtipobien'),
        array('texto', 'nombre'),
        array('texto', 'codigo'),
        array('largo', 'observacion'),
        array('largo', 'obsinterna')
    ));
    if ($datos['clientes_id'])
        $datos['idcliente'] = $datos['clientes_id'];
    else
        $datos['idcliente'] = 0;

} else {
    $datos = array('idcliente' => 0);
}

if (!$datos['codigo']) {
    if (!$proximoid = campo_sql(consulta_sql("SELECT idbien FROM bienes ORDER BY idbien DESC LIMIT 1"), 0) + 1)
        $proximoid = 1;
    while (contar_sql(consulta_sql("SELECT idbien FROM bienes WHERE codigo = '".completar_numero($proximoid, 5)."' LIMIT 1")))
        $proximoid++;
    $datos['codigo'] = completar_numero($proximoid, 5);

} else {
    $resultado_sql = consulta_sql("SELECT idbien, nombre FROM bienes WHERE codigo = '".$datos['codigo']."' AND idbien != '$id' LIMIT 1");

    if ($boton == $i18n_funciones[27] && contar_sql($resultado_sql)) {
        $temp_array = array_sql($resultado_sql);
        mensajes_alta($i18n[32].'<a href="bienes.php?a=ver&id='.$temp_array['idbien'].'" class="enlace" target="_blank">'.$temp_array['nombre'].'</a>');
        $boton = '';
    }
}

switch ($boton) {
    case $i18n_funciones[27]: //Agregar
        consulta_sql("INSERT INTO bienes SET
            idcliente = '".$datos['idcliente']."',
            idtipobien = '".$datos['idtipobien']."',
            nombre = '".$datos['nombre']."',
            codigo = '".$datos['codigo']."',
            observacion = '".escape_sql($datos['observacion'])."',
            obsinterna='".escape_sql($datos['obsinterna'])."'
            ");
        $id = id_sql();
        fullsearch_insert('bienes',$id);
        extras_alta();
        ir_ahora('bienes.php?a=ver&id='.$id);
        break;

    case $i18n_funciones[23]: // Cancelar
        ir_atras();
        break;
}

ventana_inicio($i18n[30]);
{
    // Seleccione un cliente
    contenido_inicio($i18n[25], '100', false, false, $i18n[26]);
    {
        seleccionador('clientes');
    }
    contenido_fin();

    // Datos básicos
    contenido_inicio($i18n[82]);
    {
        entrada('texto', 'nombre', $i18n[34], $datos['nombre'], '50', '60');
        entrada('texto', 'codigo', $i18n[20], $datos['codigo'], '25', '60');
        selector_familiar('idtipobien', $i18n[43], $datos['idtipobien'], '25', 'categorias_bienes', true, true, true);
    }
    contenido_fin();

    extras();

    // Observaciones
    contenido_inicio($i18n[35], '100', true, false);
    {
        area('observacion', $i18n[35], $datos['observacion']);
        area('obsinterna', $i18n[87], $datos['obsinterna']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n_funciones[27]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();
?>
<script type="text/javascript" charset="utf-8">
    function validacion_bienes_alta(boton)
    {
        if (boton == '<?php echo $i18n_funciones[27]; ?>' && $("#marco_bienes_alta input[name='nombre']").val() == '') {
<?php script_validacion_flotante("alerta", $i18n[28], "input[name='nombre']"); ?>

            return false;
        } else

            return true;
    };
</script>
