<?php

switch ($boton ? $boton : $a) {
    case $i18n_funciones['cancelar']:
        ir_atras();
        break;

    case $i18n_funciones['aceptar']:
        $datos = recibir_matriz(array(
        array('moneda', 'total'),
        array('fecha', 'fecha'),
        array('entero', 'idformapago'),
        array('entero', 'idcaja'),
        array('entero', 'idconcepto'),
        array('largo', 'observacion'),
        array('texto', 'tipo'),
        array('texto', 'tipocheque'),
        array('fecha', 'fechacobro'),
        array('entero', 'idbanco'),
        array('entero', 'numero'),
        array('texto', 'titular'),
        array('texto', 'mensaje'),
        array('entero', 'idtributo'),
        array('entero', 'retencion_puntodeventa'),
        array('entero', 'retencion_numero'),
        array('largo', 'retencion_observacion'),
        array('entero', 'idrelacion'),
        array('entero', 'idcheque'),
        array('entero', 'idretencion'),
        array('texto', 'saldo'),
        array('entero', 'idproveedor'),
        array('entero', 'idmoneda')
    ));

        // Puse esto acá, porque si ingreso un valor > saldo, al volver del break, no obtengo el id nuevamente
        $idcompra = $datos['idcompra'] = recibir_variable('entero', 'idcompra', false);

        if ($datos['idcompra']) {
            $compra = array_sql(consulta_sql(
                "SELECT compras.estado, compras.situacion, compras.fecha, compras.total, compras.numerocompleto, tiporelacion, idrelacion, compras.idmoneda,
                    (SELECT saldo FROM saldos WHERE tiporelacion = 'compras' AND idrelacion = compras.idcompra LIMIT 1) AS saldo,
                    (SELECT simbolo FROM monedas WHERE idmoneda = compras.idmoneda LIMIT 1) AS simbolo,
                    (SELECT total FROM compraspagos WHERE idcomprapago = '$id' LIMIT 1) AS total_anterior,
                    (SELECT idmoneda FROM compraspagos WHERE idcomprapago = '$id' LIMIT 1) AS idmoneda_anterior
                 FROM compras
                 WHERE idcompra = '{$datos['idcompra']}'"));

            if (($datos['idmoneda'] == $compra['idmoneda']
                && ($datos['total'] - $compra['total_anterior']) > $compra['saldo'])
                || ($datos['idmoneda'] != $compra['idmoneda']
                    && (cotizacion($compra['idmoneda'], $datos['idmoneda'], $datos['total']) - cotizacion($compra['idmoneda'], $compra['idmoneda_anterior'], $compra['total_anterior'])) > $compra['saldo'])) {

                mensajes_alta($i18n[80].$compra['simbolo'].' '.number_format($compra['saldo'], 2, '.', ''));
                $datos['total'] = $datos['saldo'];
                $a = 'modpago';
                ?>
                    <script>
                        seleccionar_tipo_caja();
                    </script>
                <?php
                break;
            }
        }

        if ($_SESSION['perfil_idperfil'] != '1') {
            $cajas_sql = consulta_sql("SELECT * FROM categorias_cajas
                LEFT JOIN perfilesxcajas ON categorias_cajas.idtipocaja=perfilesxcajas.idtipocaja
                WHERE estado='1' AND (categorias_cajas.compartida='1'
                    OR (idperfil='".$_SESSION['perfil_idperfil']."' AND mod_='1'))
                    AND idcaja = '".$datos['idcaja']."'
            ");

            if (!$cajas_sql)
                ir_atras($i18n[290]);
        }

        // PREPARAR NUEVOS DATOS
        $datos['idusuario'] = $_SESSION['usuario_idusuario'];
        $datos['tiporelacion'] = 'proveedorpago';
        $comprapago = array_sql(consulta_sql(
            "SELECT idcomprapago, total, idproveedor, idformapago,
                (SELECT idcaja FROM movimientosxcajas WHERE tiporelacion = 'proveedorpago' AND idrelacion = '$id') AS idcaja
            FROM compraspagos
            WHERE idcomprapago = '$id'"));

        $devolucion = $comprapago['total'] < 0
            ? true
            : false;
        $datos['idproveedor'] = $comprapago['idproveedor'];
        // PARCHE: No dejo modificar la forma de pago en esta actualización
        $datos['idformapago'] = $comprapago['idformapago'];

        $tipoformapago = campo_sql(consulta_sql(
            "SELECT tipo FROM tablas_formasdepago WHERE idformapago = '".$datos['idformapago']."' LIMIT 1"));
        $formapago_anterior = array_sql(consulta_sql("SELECT tiporelacion, idrelacion FROM compraspagos WHERE idcomprapago = '".$id."' LIMIT 1")); //averiguo forma de pago anterior
        $datos['tipocheque'] = campo_sql(consulta_sql("SELECT tipo FROM cheques WHERE idcheque = '".$formapago_anterior['idrelacion']."' LIMIT 1"));
        $datos['idrelacion'] = $formapago_anterior['idrelacion'];

        if ($datos['total'] <= 0) {   // Val x total para todos
                ir_atras($i18n[77]);
                break;
        }

        if ($tipoformapago == 'cheque')
        {
            if ($formapago_anterior['tiporelacion'] == $tipoformapago) {
                if (!contar_sql(consulta_sql("SELECT idcheque, numero FROM cheques WHERE numero = '".$datos['numero']."' AND idcheque != '".$formapago_anterior['idrelacion']."' LIMIT 1")))
                {
                    if ($datos['tipocheque'] == 'propio') {
                        if (!$datos['numero']) {  // Val x número
                            ir_atras($i18n[279]);
                            break;
                        } else {
                            if (!$datos['fechacobro'] || !strtotime($datos['fechacobro'])) {  //Val x fecha
                                ir_atras($i18n[251]);
                                break;
                            } else {
                                if (!$datos['titular']) { //Val x titular
                                    ir_atras($i18n[252]);
                                    break;
                                } else {
                                    consulta_sql("UPDATE cheques SET
                                            fechacobro = '".fecha_sql($datos['fechacobro'])."',
                                            idbanco = '".$datos['idbanco']."',
                                            numero = '".$datos['numero']."',
                                            titular = '".$datos['titular']."'
                                        WHERE idcheque = '".$formapago_anterior['idrelacion']."'
                                        LIMIT 1"
                                    );
                                    $datos['idrelacion'] = $formapago_anterior['idrelacion']; //PARCHE, sino me trae 0 en el UPDATE de compraspagos
                                }
                            }
                        }
                    } else if ($datos['tipocheque'] == 'tercero') {
                        //desconcilio el cheque anterior
                        consulta_sql("UPDATE cheques SET estado = 0 WHERE idcheque = '".$formapago_anterior['idrelacion']."'");
                        consulta_sql("UPDATE movimientosxcajas SET conciliacion = 0, fechaconciliacion = '' WHERE tiporelacion = '".$formapago_anterior['tiporelacion']."' AND idrelacion = '".$formapago_anterior['idrelacion']."'");

                        /* qué hago si
                        1_ en vez de pagar con cheque de tercero, quiero pagar con uno propio.
                        2_ cómo de-selijo el cheque conciliado
                        3_ si vuelvo a elegir el mismo cheque
                        4_ si elijo pagar con un cheque propio, es un nuevo ALTA, no un MOD :/ LPM!
                        predeterminé en comprobantes JS que si mod un cheque de terceros, sea la solapa predeterminada 'en cartera' y que elimine datos de los campos de propio...
                        */

                        //y concilio el nuevo cheque
                        $idventapago = campo_sql(consulta_sql("SELECT idventapago FROM ventaspagos WHERE idrelacion = '".$datos['idrelacion']."'"));
                        consulta_sql("UPDATE movimientosxcajas SET conciliacion = 1, fechaconciliacion = '".date("Y-m-d H:i:s")."' WHERE tiporelacion = '".$datos['tiporelacion']."' AND  idrelacion = '".$idventapago."' AND tiporelacion = 'clientepago'");
                        consulta_sql("UPDATE cheques SET estado = 1 WHERE idcheque = '".$datos['idrelacion']."' LIMIT 1");
                    }
                } else {
                    ir_atras($i18n[264]);  // Alerta x cheque duplicado
                    break;
                }
            } else if ($formapago_anterior['tiporelacion'] != 'cheque') { //si antes era otra cosa y ahora es cheque, insert cheque
                    consulta_sql("INSERT INTO cheques SET
                        tipo = '".$datos['tipo']."',
                        fechacobro = '".fecha_sql($datos['fechacobro'])."',
                        idbanco = '".$datos['idbanco']."',
                        numero = '".$datos['numero']."',
                        titular = '".$datos['titular']."'
                    ");
                    $datos['tipo'] = 'cheque';
                    $datos['idrelacion'] = id_sql();

                }
        } elseif ($tipoformapago == 'retencion') {
            if ($formapago_anterior['tiporelacion'] == $tipoformapago) {  //si era retención y sigue siendo retención
                consulta_sql("UPDATE retenciones SET
                    idtributo = '".$datos['idtributo']."',
                    puntodeventa = '".$datos['retencion_puntodeventa']."',
                    numero = '".$datos['retencion_numero']."',
                    observacion = '".$datos['retencion_observacion']."'
                    WHERE idretencion = '".$datos['idrelacion']."'
                    LIMIT 1
                ");
            } else if ($formapago_anterior['tiporelacion'] != 'retencion') { //si antes era otra cosa y ahora es retencion, insert retención
                consulta_sql("INSERT INTO retenciones SET
                    idtributo = '".$datos['idtributo']."',
                    puntodeventa = '".$datos['retencion_puntodeventa']."',
                    numero = '".$datos['retencion_numero']."',
                    observacion = '".$datos['retencion_observacion']."'
                ");
                $datos['tipo'] = 'retencion';
                $datos['idrelacion'] = id_sql();
            }
        } else {
            //por si antes era otra cosa que no sea cheque o retención o cambia a otra cosa que nos ea cheque y retención, por las dudas, vació variables
            $datos['tiporelacion'] = '';
            $datos['idrelacion'] = '';
        }

        //delete si antes era retención o cheque (con su respectivo mensaje)
        if ($formapago_anterior['tiporelacion'] != $tipoformapago) {
            if ($formapago_anterior['tiporelacion'] == 'retencion') {
                consulta_sql("DELETE FROM retenciones WHERE idretencion = '".$formapago_anterior['idrelacion']."' LIMIT 1");
            } else if ($formapago_anterior['tiporelacion'] == 'cheque') {
                $datos['idmensaje'] = campo_sql(consulta_sql("SELECT idmensaje FROM cheques WHERE idcheque = '".$formapago_anterior['idrelacion']."' LIMIT 1"));
                if ($datos['idmensaje'] != 0) {
                    consulta_sql("DELETE FROM mensajes WHERE idmensaje = '".$datos['idmensaje']."' LIMIT 1");
                }
                consulta_sql("DELETE FROM cheques WHERE idcheque = '".$formapago_anterior['idrelacion']."' LIMIT 1");
            }
        }

        //Mensajes MOD ------ falta si es de tercero
        if ($datos['tipocheque'] == 'propio') {
            $datos['proveedor'] = campo_sql(consulta_sql("SELECT nombre FROM proveedores WHERE idproveedor = '".$datos['idproveedor']."' LIMIT 1"));
            $mensaje = '<p>' .
                $i18n[260] . '<a href="proveedores.php?a=ver&id=' . $datos['idproveedor'] . '">' . $datos['proveedor']  .'</a><br>'
                .$i18n[261] . '<a href="cheques.php?a=ver&id=' . $datos['idrelacion'] . '">' . $datos['numero'] .'</a><br>'
                .$i18n[262] . $datos['total'] .'<br>'
                .$i18n[263] . $datos['fechacobro'] . '</p>';
            //si tiene mensaje, lo pisa
            if ($datos['idmensaje']) {
                consulta_sql(
                    "UPDATE mensajes SET
                    texto = '".escape_sql($mensaje)."'
                    WHERE idmensaje = '".$datos['idmensaje']."'
                ");
            } else if ($datos['mensaje']) { //si no tiene mensaje, crea uno
                $dias_antes = $datos['mensaje'] - 1;
                $fechamensaje = fecha_sql($datos['fechacobro']);
                $fechamensaje = date("Y-m-d", strtotime("$fechamensaje -$dias_antes day"));
                consulta_sql(
                 "INSERT INTO mensajes SET
                    idusuario = '".$datos['idusuario']."',
                    tipo = 'Recordatorio',
                    fecha = '".$fechamensaje."',
                    texto = '".escape_sql($mensaje)."'
                ");
                $datos['idmensaje'] = id_sql();
                consulta_sql("UPDATE cheques SET idmensaje = '".$datos['idmensaje']."' WHERE idcheque = '".$datos['idrelacion']."' LIMIT 1");
            }
        }

        $buscar_monedas = [
            'proveedores' => $datos['idproveedor'],
            'cajas' => $datos['idcaja'],
        ];
        if ($datos['idcompra'])
            $buscar_monedas['compras'] = $datos['idcompra'];
        if ($compra['tiporelacion'] == 'servicio')
            $buscar_monedas['clientes'] = campo_sql(consulta_sql(
                "SELECT idcliente FROM servicios WHERE idservicio = '{$compra['idrelacion']}'"));
        $idmonedas = idmonedas($buscar_monedas);

        //UPDATE SQL
        consulta_sql("UPDATE compraspagos SET
                total = '".($datos['total'] * ($devolucion ? -1 : 1))."',
                idmoneda = '".$datos['idmoneda']."',
                idformapago = '".$datos['idformapago']."',
                fecha = '".fecha_sql($datos['fecha'])."',
                observacion = '".escape_sql($datos['observacion'])."',
                tiporelacion = '".$tipoformapago."',
                idrelacion = '".$datos['idrelacion']."'
            WHERE idcomprapago = '".$id."'
            LIMIT 1
        ");
        consulta_sql("UPDATE comprasxproveedores SET
                fecha = '".fecha_sql($datos['fecha'])."',
                total = '".cotizacion($idmonedas['proveedores'], $datos['idmoneda'], $datos['total'] * ($devolucion ? -1 : 1))."'
            WHERE idtipocompra = '".($devolucion ? -1 : 0)."'
            AND id = '".$id."'
            LIMIT 1
        ");
        //Es compras movimientosxcajas se invierte el signo
        consulta_sql("UPDATE movimientosxcajas SET
                fecha = '".fecha_sql($datos['fecha'])."',
                total = '".cotizacion($idmonedas['cajas'], $datos['idmoneda'], $datos['total'] * ($devolucion ? 1 : -1))."',
                idconcepto = '".$datos['idconcepto']."',
                idcaja = '".$datos['idcaja']."'
            WHERE tiporelacion = 'proveedorpago'
            AND idrelacion = '".$id."'
            LIMIT 1
        ");

        //Actualizar saldo
        $a = 'modpago';
        $diferencia = ($devolucion ? -1 : 1) * (abs($comprapago['total']) - abs($datos['total']));
        if ($datos['idcompra']) {
            actualizar_saldo('compras', $datos['idcompra'], cotizacion($idmonedas['compras'], $datos['idmoneda'], $diferencia));
            if ($compra['tiporelacion'] == 'servicio' && $compra['idrelacion'])
                actualizar_saldo('comprasxservicios', $compra['idrelacion'], cotizacion($idmonedas['clientes'], $datos['idmoneda'], $diferencia));
        }
        actualizar_saldo('proveedores', $datos['idproveedor'], cotizacion($idmonedas['proveedores'], $datos['idmoneda'], $diferencia));
        if ($comprapago['idcaja'] == $datos['idcaja']) {
            actualizar_saldo('cajas', $datos['idcaja'], cotizacion($idmonedas['cajas'], $datos['idmoneda'], $diferencia));
        } else {
            actualizar_saldo('cajas', $comprapago['idcaja'], cotizacion($idmonedas['cajas'], $datos['idmoneda'], ($devolucion ? -1 : 1) * abs($comprapago['total'])));
            actualizar_saldo('cajas', $datos['idcaja'], cotizacion($idmonedas['cajas'], $datos['idmoneda'], ($devolucion ? 1 : -1) * abs($datos['total'])));
        }

        // ACTUALIZO CONTROLES
        actualizar_controles(array(
            'ultimoformapagocompra' => $datos['idformapago'],
            'ultimoconceptocompra' => $datos['idconcepto'],
            'ultimotipocajacompra' => campo_sql(consulta_sql("SELECT idtipocaja FROM cajas WHERE idcaja = '".$datos['idcaja']."' LIMIT 1"), 0),
        ));

        consulta_sql("UPDATE proveedores SET idconcepto = '".$datos['idconcepto']."' WHERE idproveedor = '".$datos['idproveedor']."'");

        ir_atras();
        break;

    case $i18n_funciones['agregar']:
        // RECIBO Y VALIDO DATOS
        $datos = recibir_matriz(array(
        array('moneda', 'total'),
        array('fecha', 'fecha'),
        array('entero', 'idformapago'),
        array('entero', 'idcaja'),
        array('entero', 'idconcepto'),
        array('largo', 'observacion'),
        array('texto', 'tipo'),
        array('texto', 'tipocheque'),
        array('fecha', 'fechacobro'),
        array('entero', 'idbanco'),
        array('entero', 'numero'),
        array('texto', 'titular'),
        array('entero', 'idmensaje'),
        array('texto', 'mensaje'),
        array('entero', 'idtributo'),
        array('entero', 'retencion_puntodeventa'),
        array('entero', 'retencion_numero'),
        array('largo', 'retencion_observacion'),
        array('texto', 'devolucion'),
        array('entero', 'idproveedor'),
        array('entero', 'idventa'),
        array('entero', 'idrelacion'),
        array('texto', 'saldo'),
        array('entero', 'idnumerocomprapago'),
        array('entero', 'idmoneda')
    ));

        // Redondeo para que la comparación sea correcta
        $datos['saldo'] = redondeo($datos['saldo']);

        // Puse esto acá, porque si ingreso un valor > saldo, al volver del break, no obtengo el id nuevamente
        $idcompra = $datos['idcompra'] = recibir_variable('entero', 'idcompra', false);

        if ($datos['idcompra']) {
            $compra = array_sql(consulta_sql(
                "SELECT compras.total, compras.idmoneda,
                    (SELECT saldo FROM saldos WHERE tiporelacion = 'compras' AND idrelacion = compras.idcompra LIMIT 1) AS saldo,
                    (SELECT simbolo FROM monedas WHERE idmoneda = compras.idmoneda LIMIT 1) AS simbolo
                FROM compras
                WHERE idcompra = '{$datos['idcompra']}'"));

            if (($datos['idmoneda'] == $compra['idmoneda'] && $datos['total'] > $compra['saldo'])
                || ($datos['idmoneda'] != $compra['idmoneda']
                    && cotizacion($compra['idmoneda'], $datos['idmoneda'], $datos['total']) > $compra['saldo'])) {

                mensajes_alta($i18n[80].$compra['simbolo'].' '.redondeo($datos['saldo']));
                $a = 'altapago';
                ?>
                    <script>
                        seleccionar_tipo_caja();
                    </script>
                <?php
                break;
            }
        }

        if ($_SESSION['perfil_idperfil'] != '1') {
            $cajas_sql = consulta_sql("SELECT * FROM categorias_cajas
                LEFT JOIN perfilesxcajas ON categorias_cajas.idtipocaja=perfilesxcajas.idtipocaja
                WHERE estado='1' AND (categorias_cajas.compartida='1'
                    OR (idperfil='".$_SESSION['perfil_idperfil']."' AND alta='1'))
                    AND idcaja = '".$datos['idcaja']."'
            ");

            if (!$cajas_sql)
                ir_atras($i18n[290]);
        }

        // PREPARAR NUEVOS DATOS
        $datos['idusuario'] = $_SESSION['usuario_idusuario'];
        $datos['tiporelacion'] = 'proveedorpago';

        $devolucion = $datos['devolucion']
            ? true
            : false;

        $tipoformapago = campo_sql(consulta_sql(
            "SELECT tipo FROM tablas_formasdepago WHERE idformapago = '".$datos['idformapago']."' LIMIT 1"));

        // EMPIEZO CON LOS INSERT SQL
        if ($datos['total'] <= 0) {   // Val x total para todos
                ir_atras($i18n[77]);
                break;
        }
        if ($tipoformapago == 'cheque')
        {
            //el nombre del proveedor lo voy a usar para mensajes, lo pongo acá, ya que es común para cheques, después vemos si lo movemos
            $datos['proveedor'] = campo_sql(consulta_sql("SELECT nombre FROM proveedores WHERE idproveedor = '".$datos['idproveedor']."' LIMIT 1"));

            if ($datos['idrelacion']) { //Si es en cartera
                $numerochequeseleccionado = campo_sql(consulta_sql("SELECT numero FROM cheques WHERE idcheque = '".$datos['idrelacion']."' LIMIT 1"));
                $datos['numero'] = $numerochequeseleccionado;
                $datos['tipocheque'] = 'tercero';
                $datos['conciliacion'] = 1;
                $datos['fechaconciliacion'] = date('d-m-Y');//hoy
                $datos['idventapago'] = campo_sql(consulta_sql("SELECT idventapago FROM ventaspagos WHERE idrelacion = '".$datos['idrelacion']."' AND tiporelacion = 'cheque'"));

                consulta_sql("UPDATE movimientosxcajas SET
                    conciliacion = '".$datos['conciliacion']."',
                    idconciliacion = '".$datos['idventapago']."',
                    fechaconciliacion = '".fecha_sql($datos['fechaconciliacion'])."'
                    WHERE idrelacion = '".$datos['idventapago']."'
                    AND tiporelacion = 'clientepago'
                ");

                consulta_sql("UPDATE cheques SET estado = 1 WHERE idcheque = '".$datos['idrelacion']."' LIMIT 1");  //concilio el cheque
            } else {
                if (!$datos['numero']) {  // Val x número
                    ir_atras($i18n[279]);
                    break;
                } else {
                    if (!$datos['fechacobro'] || !strtotime($datos['fechacobro'])) {  //Val x fecha
                        ir_atras($i18n[251]);
                        break;
                    } else {
                        if (!$datos['titular']) { //Val x titular
                            ir_atras($i18n[252]);
                            break;
                        } else {
                            if (!contar_sql(consulta_sql("SELECT idcheque, numero FROM cheques WHERE numero = '".$datos['numero']."' LIMIT 1"))) { // Validación x cheque duplicado
                                $datos['tipocheque'] = 'propio'; //Parche para acá y para insert into mensajes... Ya ni sé de dónde traerlo "correctamente"
                                consulta_sql("INSERT INTO cheques SET
                                    tipo = '".$datos['tipocheque']."',
                                    fechacobro = '".fecha_sql($datos['fechacobro'])."',
                                    idbanco = '".$datos['idbanco']."',
                                    numero = '".$datos['numero']."',
                                    titular = '".$datos['titular']."',
                                    idmensaje = '".$datos['idmensaje']."'
                                ");
                                $datos['tipo'] = 'cheque';
                                $datos['idrelacion'] = id_sql();

                                // ACTUALIZO CONTROLES
                                actualizar_controles(array(
                                    'ultimobanco' => $datos['idbanco'],
                                    'ultimocheque' => $datos['numero'],
                                    'ultimotipocajacompra' => campo_sql(consulta_sql("SELECT idtipocaja FROM cajas WHERE idcaja = '".$datos['idcaja']."' LIMIT 1"), 0),
                                    ));

                            } else {
                                ir_atras($i18n[264]);    //Alerta duplicado
                                break;
                            }
                        }
                    }
                }
            }

        } elseif ($tipoformapago == 'retencion') {
                consulta_sql("INSERT INTO retenciones SET
                    idtributo = '".$datos['idtributo']."',
                    puntodeventa = '".$datos['retencion_puntodeventa']."',
                    numero = '".$datos['retencion_numero']."',
                    observacion = '".$datos['retencion_observacion']."'
                ");
                $datos['tipo'] = 'retencion';
                $datos['idrelacion'] = id_sql();
        }

        consulta_sql("INSERT INTO compraspagos SET
            idproveedor = '".$datos['idproveedor']."',
            idusuario = '".$datos['idusuario']."',
            idmoneda = '".$datos['idmoneda']."',
            idcompra = '".$datos['idcompra']."',
            idformapago = '".$datos['idformapago']."',
            fecha = '".fecha_sql($datos['fecha'])."',
            closed_at = NOW(),
            total = '".($datos['total'] * ($devolucion ? -1 : 1))."',
            observacion = '".escape_sql($datos['observacion'])."',
            tiporelacion = '".$tipoformapago."',
            idrelacion = '".$datos['idrelacion']."',
            idnumerocomprapago = '".$datos['idnumerocomprapago']."'
        ");
        $datos['idcomprapago'] = id_sql();

        if ($devolucion) {
            $datos['idtipocompra'] = -1;
            $datos['detalle'] = $i18n[239]. 'DP'.completar_numero($datos['idcomprapago'], 8);

        } else {
            $datos['idtipocompra'] = 0;
            $datos['detalle'] = $i18n[117]. 'OP'.completar_numero($datos['idcomprapago'], 8);
        }

        if ($tipoformapago == 'cheque') {
            $datos['detalle'] = $datos['detalle']. ' | ' .$i18n[280]. ' ' .$datos['numero'];
        }

        if ($datos['idcompra']) {
            $compra = array_sql(consulta_sql(
                "SELECT compras.estado, compras.situacion, compras.fecha, compras.total, compras.numerocompleto, compras.idrelacion, compras.tiporelacion, compras.operacioninversa, compras.idmoneda,
                    (SELECT SUM(total) FROM compraspagos WHERE compraspagos.idcompra = compras.idcompra) AS pagos,
                    categorias_compras.nombre AS tipocompra,
                    usuarios.nombre AS usuario
                FROM compras
                    LEFT JOIN categorias_compras ON compras.idtipocompra = categorias_compras.idtipocompra
                    LEFT JOIN usuarios ON compras.idusuario = usuarios.idusuario
                WHERE idcompra = '".$datos['idcompra']."'
                LIMIT 1"));
            $datos['numerocompra'] = $compra['numerocompleto'];
            $compra['saldo'] = redondeo($compra['total'] - $compra['pagos']);
        }

        if (!$datos['idnumerocomprapago']) {
            consulta_sql("UPDATE compraspagos SET idnumerocomprapago = '".$datos['idcomprapago']."' WHERE idcomprapago = '".$datos['idcomprapago']."'");
            $datos['idnumerocomprapago'] = $datos['idcomprapago'];
        }

        $buscar_monedas = [
            'proveedores' => $datos['idproveedor'],
            'cajas' => $datos['idcaja'],
        ];
        if ($datos['idcompra'])
            $buscar_monedas['compras'] = $datos['idcompra'];
        if ($compra['tiporelacion'] == 'servicio')
            $buscar_monedas['clientes'] = campo_sql(consulta_sql(
                "SELECT idcliente FROM servicios WHERE idservicio = '{$compra['idrelacion']}'"));
        $idmonedas = idmonedas($buscar_monedas);

        consulta_sql("INSERT INTO comprasxproveedores SET
            idproveedor = '".$datos['idproveedor']."',
            idtipocompra = '".$datos['idtipocompra']."',
            id = '".$datos['idcomprapago']."',
            fecha = '".fecha_sql($datos['fecha'])."',
            total = '".cotizacion($idmonedas['proveedores'], $datos['idmoneda'], $datos['total'] * ($devolucion ? -1 : 1))."',
            numero = '".escape_sql($datos['numerocompra'])."'
        ");

        consulta_sql("INSERT INTO movimientosxcajas SET
            idcaja = '".$datos['idcaja']."',
            idusuario = '".$datos['idusuario']."',
            idconcepto = '".$datos['idconcepto']."',
            fecha = '".fecha_sql($datos['fecha'])."',
            total = '".cotizacion($idmonedas['cajas'], $datos['idmoneda'], $datos['total'] * ($devolucion ? 1 : -1))."',
            detalle = '".$datos['detalle']."',
            tiporelacion = '".$datos['tiporelacion']."',
            idrelacion = '".$datos['idcomprapago']."'
        ");
        $idmovimientoxcaja = id_sql();

        //concilio movimiento generado por la entrega de cheque en cartera
        if ($tipoformapago == 'cheque' && $datos['tipocheque'] == 'tercero') {
            consulta_sql("UPDATE movimientosxcajas SET
                conciliacion = '".$datos['conciliacion']."',
                idconciliacion = '".$datos['idventapago']."',
                fechaconciliacion = now()
                WHERE idmovimientoxcaja = '".$idmovimientoxcaja."'
            ");
        }

        //Mensajes
        if ($datos['tipocheque'] == 'propio' && $datos['mensaje']) //si es propio y tiene mensaje seleccionado
        {
            $dias_antes = $datos['mensaje'] - 1;
            $fechamensaje = fecha_sql($datos['fechacobro']);
            $fechamensaje = date("Y-m-d", strtotime("$fechamensaje -$dias_antes day"));
            $mensaje = '<p>' .
                $i18n[260] . '<a href="proveedores.php?a=ver&id=' . $datos['idproveedor'] . '">' . $datos['proveedor']  .'</a><br>'
                .$i18n[261] . '<a href="cheques.php?a=ver&id=' . $datos['idrelacion'] . '">' . $datos['numero'] .'</a><br>'
                .$i18n[262] . $datos['total'] .'<br>'
                .$i18n[263] . $datos['fechacobro'] . '</p>';
            consulta_sql(
                "INSERT INTO mensajes SET
                idusuario = '".$datos['idusuario']."',
                tipo = 'Recordatorio',
                fecha = '".$fechamensaje."',
                texto = '".escape_sql($mensaje)."'
            ");
            $datos['idmensaje'] = id_sql();
            consulta_sql("UPDATE cheques SET idmensaje = '".$datos['idmensaje']."' WHERE idcheque = '".$datos['idrelacion']."' LIMIT 1");

        } else if ($datos['tipocheque'] == 'tercero') {
            $cheque = array_sql(consulta_sql("SELECT cheques.idmensaje, cheques.numero, mensajes.* FROM cheques LEFT JOIN mensajes ON cheques.idmensaje = mensajes.idmensaje WHERE idcheque = '".$datos['idrelacion']."'"));
            //ver qué va en el idcomprapago de un cheque de tercero conciliado
            $mensaje = '<p>' .
                $i18n[272] . '<a href="proveedores.php?a=ver&id=' . $datos['idproveedor'] . '">' . $datos['proveedor']  .'</a><br>'
                .$i18n[273] . '<a href="cheques.php?a=ver&id=' . $datos['idrelacion'] . '">' . $datos['numero'] .'</a><br>'
                .$i18n[274] . $datos['fechaconciliacion'] . '</p>';

            if ($cheque['idmensaje']) {   //si tengo mensaje, concateno
                $mensaje = $mensaje . '<hr>' . $cheque['texto'];
                consulta_sql(
                    "UPDATE mensajes SET
                    texto = '".escape_sql($mensaje)."'
                    WHERE idmensaje = '".$cheque['idmensaje']."'
                    LIMIT 1
                ");
            } else {    //si no tengo mensaje, hago insert
                consulta_sql(
                    "INSERT INTO mensajes SET
                    idusuario = '".$datos['idusuario']."',
                    tipo = 'Recordatorio',
                    fecha = now(),
                    texto = '".escape_sql($mensaje)."'
                ");
                $datos['idmensaje'] = id_sql();
                consulta_sql("UPDATE cheques SET idmensaje = '".$datos['idmensaje']."' WHERE idcheque = '".$datos['idrelacion']."' LIMIT 1");
            }
        }

        // Actualizar saldos
        $diferencia = ($devolucion ? 1 : -1) * $datos['total'];
        if ($datos['idcompra']) {
            actualizar_saldo('compras', $datos['idcompra'], cotizacion($idmonedas['compras'], $datos['idmoneda'], $diferencia));
            if ($compra['tiporelacion'] == 'servicio')
                actualizar_saldo('comprasxservicios', $compra['idrelacion'], cotizacion($idmonedas['clientes'], $datos['idmoneda'], $diferencia));
        }
        actualizar_saldo('proveedores', $datos['idproveedor'], cotizacion($idmonedas['proveedores'], $datos['idmoneda'], $diferencia));
        actualizar_saldo('cajas', $datos['idcaja'], cotizacion($idmonedas['cajas'], $datos['idmoneda'], $diferencia));

        // ACTUALIZO CONTROLES
        actualizar_controles(array(
            'ultimoformapagocompra' => $datos['idformapago'],
            'ultimoconceptocompra' => $datos['idconcepto'],
            'ultimotipocajacompra' => campo_sql(consulta_sql("SELECT idtipocaja FROM cajas WHERE idcaja = '".$datos['idcaja']."' LIMIT 1"), 0),
        ));

        consulta_sql("UPDATE proveedores SET idconcepto = '".$datos['idconcepto']."' WHERE idproveedor = '".$datos['idproveedor']."'");

        // NOS VAMOS
        if ($datos['saldo'] - $datos['total'] == 0 || $datos['saldo'] < $datos['total']) { //Si completo el pago, o es un pago a cuenta
            ir_atras();
        } else {
            ir_ahora('compras.php?a=verpago&id='.$datos['idnumerocomprapago']);
        }
        break;

    case 'altapago':
        $idproveedor = recibir_variable('entero', 'idproveedor', true);
        $idcompra = recibir_variable('entero', 'idcompra', true);
        $devolucion = recibir_variable('texto', 'devolucion', true);
        $idnumerocomprapago = recibir_variable('entero', 'idnumerocomprapago', true);

        $datos = array(
            'idproveedor' => $idproveedor,
            'idcompra' => $idcompra,
            'fecha' => ($idnumerocomprapago ? campo_sql(consulta_sql("SELECT fecha FROM compraspagos WHERE idcomprapago = $idnumerocomprapago ORDER BY idcomprapago DESC LIMIT 1")): fecha_sql('ahora')),
            'idconcepto' => campo_sql(consulta_sql(
                "SELECT idconcepto
                FROM proveedores
                WHERE idproveedor = $idproveedor
                LIMIT 1"), 0),
            'idformapago' => $_SESSION['control_ultimoformapagocompra'],
            'idnumerocomprapago' => $idnumerocomprapago,
            'idcaja' => campo_sql(consulta_sql(
                "SELECT idcaja
                FROM categorias_cajas
                WHERE idtipocaja = (SELECT ultimotipocajacompra FROM controles WHERE idusuario = '".$_SESSION['usuario_idusuario']."')
                LIMIT 1"), 0),
        );

    break;

    case 'modpago':
        $datos = array_sql(consulta_sql(
        "SELECT compraspagos.idproveedor, compraspagos.total, compraspagos.fecha, compraspagos.idformapago, compraspagos.idcompra, compraspagos.observacion, compraspagos.idmoneda,
            compraspagos.tiporelacion, compraspagos.idrelacion, movimientosxcajas.idcaja, movimientosxcajas.idconcepto,
            cajas.idtipocaja, cajas.fechacierre,
            categorias_cajas.nombre AS caja,
            monedas.simbolo
        FROM compraspagos
            LEFT JOIN movimientosxcajas ON (compraspagos.idcomprapago = movimientosxcajas.idrelacion AND movimientosxcajas.tiporelacion = 'proveedorpago')
            LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
            LEFT JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
            LEFT JOIN monedas ON compraspagos.idmoneda = monedas.idmoneda
        WHERE idcomprapago = '$id' AND movimientosxcajas.tiporelacion = 'proveedorpago'
        LIMIT 1"));

        $devolucion = $datos['total'] < 0
            ? true
            : false;

        if ($datos['fechacierre'] != '0000-00-00 00:00:00') {
            if ($_SESSION['perfil_idperfil'] == '1' || contar_sql(consulta_sql("SELECT idtipocaja FROM perfilesxcajas WHERE idperfil = '".$_SESSION['perfil_idperfil']."' AND idtipocaja='".$datos['idtipocaja']."' AND mod_cerrada='1' LIMIT 1")))
                script_flotante('informacion', $i18n[125]);
            else
                ir_atras($i18n[124]);
        }

        if ($datos['idcompra']) {
            $compra = array_sql(consulta_sql(
                "SELECT compras.estado, compras.situacion, compras.fecha, compras.total, compras.numerocompleto, compras.idmoneda,
                    (SELECT SUM(total) FROM compraspagos WHERE compraspagos.idcompra = compras.idcompra AND idcomprapago != '$id') AS pagos,
                    categorias_compras.nombre AS tipocompra,
                    usuarios.nombre AS usuario
                 FROM compras
                    LEFT JOIN categorias_compras ON compras.idtipocompra = categorias_compras.idtipocompra
                    LEFT JOIN usuarios ON compras.idusuario = usuarios.idusuario
                 WHERE idcompra = '".$datos['idcompra']."'
                 LIMIT 1"));
            $compra['saldo'] = redondeo($compra['total'] - $compra['pagos']);

            if ($boton == $i18n_funciones[22]
                && $compra['idmoneda'] == $datos['idmoneda']
                && (number_format($compra['saldo'], 2, '.', '') - number_format($datos['total'], 2, '.', '') < 0)) {
                mensajes_alta($i18n[80].number_format($compra['saldo'], 2, '.', ''));
                $boton = '';
            }
        }


        // Si es una cotización anterior, no se puede modificar
        if ($_SESSION['modulo_multimoneda'] && es_cotizacion_anterior('compraspagos', $id))
            ir_ahora('compras.php?a=verpago&id='.$id, $i18n[379]);

        if ($datos['tiporelacion'] == 'cheque') {
            $cheque = array_sql(consulta_sql(
                "SELECT cheques.*,
                    (SELECT fecha FROM mensajes WHERE mensajes.idmensaje = cheques.idmensaje) AS fecha_mensaje,
                    tablas_bancos.nombre
                FROM cheques
                    LEFT JOIN tablas_bancos ON tablas_bancos.idbanco = cheques.idbanco
                WHERE idcheque = '".$datos['idrelacion']."'
                LIMIT 1")
            );
            if ($cheque['estado'] == 1) { //bloqueo de cheque conciliado en MOD
                ir_atras($i18n[278]);
                break;
            }

        } else if ($datos['tiporelacion'] == 'retencion') {
            $retencion = array_sql(consulta_sql(
                "SELECT retenciones.*,
                    categorias_tributos.nombre AS tributo
                FROM retenciones
                    LEFT JOIN categorias_tributos ON categorias_tributos.idtributo = retenciones.idtributo
                WHERE idretencion = '".$datos['idrelacion']."'
                LIMIT 1")
            );
        }
    break;

    default:
        mostrar_error('Error en default', true);
        break;
}

// DATOS FIJOS PARA LA VISTA
$proveedor = array_sql(consulta_sql(
    "SELECT proveedores.*,
        categorias_localidades.nombre AS localidad,
        tablas_condiciones.nombre AS tipoiva
    FROM proveedores
        LEFT JOIN categorias_localidades ON proveedores.idlocalidad = categorias_localidades.idlocalidad
        LEFT JOIN tablas_condiciones ON proveedores.idtipoiva = tablas_condiciones.idtipoiva
    WHERE idproveedor = '".$datos['idproveedor']."'
    LIMIT 1")
);

if ($datos['idcompra']) {
    $compra = array_sql(consulta_sql(
        "SELECT compras.estado, compras.situacion, compras.fecha, compras.total, compras.numerocompleto, compras.idmoneda,
            (SELECT SUM(total) FROM compraspagos WHERE compraspagos.idcompra = compras.idcompra) AS pagos,
            categorias_compras.nombre AS tipocompra,
            usuarios.nombre AS usuario
         FROM compras
            LEFT JOIN categorias_compras ON compras.idtipocompra = categorias_compras.idtipocompra
            LEFT JOIN usuarios ON compras.idusuario = usuarios.idusuario
         WHERE idcompra = '".$datos['idcompra']."'
         LIMIT 1"));
    $numerocompra = $compra['numerocompleto'];
    $compra['saldo'] = redondeo($compra['total'] - $compra['pagos']);

    if ($a == 'altapago')
        $datos['total'] = $compra['saldo'];
}

if ($_SESSION['perfil_idperfil'] == '1')
    $cajas_sql = consulta_sql("SELECT * FROM categorias_cajas
        INNER JOIN cajas ON categorias_cajas.idcaja = cajas.idcaja
        WHERE estado='1'
        AND fechacierre = '0000-00-00 00:00:00' ORDER by categorias_cajas.idcaja = '".$datos['idcaja']."'
    ");
else
    $cajas_sql = consulta_sql("SELECT * FROM categorias_cajas
        LEFT JOIN perfilesxcajas ON categorias_cajas.idtipocaja = perfilesxcajas.idtipocaja
        INNER JOIN cajas ON categorias_cajas.idcaja = cajas.idcaja
        WHERE estado = '1'
        AND cajas.fechacierre = '0000-00-00 00:00:00'
        AND (categorias_cajas.compartida = '1' OR (idperfil = '".$_SESSION['perfil_idperfil']."' AND alta='1'))
        ORDER by categorias_cajas.idcaja = '".$datos['idcaja']."'
    ");
$cajas = array();
$formasdepago_disponibles = array();
while ($array_tipo_caja = array_sql($cajas_sql)) {
    $cajas[] = array(
        'id' => $array_tipo_caja['idcaja'],
        'valor' => $array_tipo_caja['nombre'],
        'datasets' => array(
            'data-tipoformapago' => $array_tipo_caja['tipo'],
        )
    );
    if (!in_array($array_tipo_caja['tipo'], $formasdepago_disponibles))
        $formasdepago_disponibles[] = "'".$array_tipo_caja['tipo']."'";
}

if (!count($cajas)) {
    ir_atras($i18n[33]);
}

$formasdepago = array();
$formasdepago_sql = consulta_sql("SELECT *
        FROM tablas_formasdepago
        WHERE tipo IN (".implode(',', $formasdepago_disponibles).")
    ");
while ($formadepago = array_sql($formasdepago_sql)) {
    $formasdepago[] = array(
        'id' => $formadepago['idformapago'],
        'valor' => $formadepago['nombre'],
        'datasets' => array(
            'data-tipoformapago' => $formadepago['tipo']
        )
    );
}

$ultimos = array_sql(consulta_sql("SELECT ultimobanco, ultimocheque FROM controles WHERE idusuario = '".$_SESSION['usuario_idusuario']."'"));
if ($a == 'altapago') {
    $cheque['numero'] = filter_var($ultimos['ultimocheque'], FILTER_SANITIZE_NUMBER_FLOAT) + 1;
}
$cheques_sql = consulta_sql(
    "SELECT cheques.*, tablas_bancos.nombre, ventaspagos.total
        FROM cheques
        LEFT JOIN tablas_bancos ON cheques.idbanco = tablas_bancos.idbanco
        LEFT JOIN ventaspagos ON idrelacion = idcheque
        WHERE estado = 0
        AND tipo = 'tercero'
        AND ventaspagos.tiporelacion IN ('cheque', 'retencion')
        ORDER by fechacobro, total
    ");

$cheques_terceros = array(array('id' => '0', 'valor' => $i18n['sin_especificar']));
if ($datos['tipo'] != 'propio' && $a == 'modpago') { //por si tengo que elegir el mismo cheque
    $cheques_terceros[] = array(
        'id' => $datos['idcheque'],
        'valor' => $i18n[52] . ': ' .$cheque['fechacobro'] . ' | $ ' .$datos['total'] .' | ' . $i18n[270] . ': ' . $cheque['numero'] . ' | ' . $i18n[242] . ': ' . $cheque['nombre'] . '  | '. $i18n[244] . ': ' . $cheque['titular'],
        'datasets' => array(
            'data-totalcheque' => $datos['total']
        )
    );
}

while ($cheque_sql = array_sql($cheques_sql)) {
    $cheques_terceros[] = array(
        'id' => $cheque_sql['idcheque'],
        'valor' => $i18n[52] . ': ' .$cheque_sql['fechacobro'] . ' | $ ' .$cheque_sql['total'] .' | ' . $i18n[270] . ': ' . $cheque_sql['numero'] . ' | ' . $i18n[242] . ': ' . $cheque_sql['nombre'] . '  | '. $i18n[244] . ': ' . $cheque_sql['titular'],
        'datasets' => array(
            'data-totalcheque' => $cheque_sql['total']
        )
    );
}
