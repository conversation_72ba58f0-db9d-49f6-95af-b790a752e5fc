<?php
$sucursales = armar_sqls_sucursales();
$datos = array_sql(consulta_sql(
    "SELECT productos.idproducto, productos.estado, productos.estadoventa, productos.estadocompra, productos.estadocombo,
        productos.idrubro, productos.sku, productos.codigo, productos.codigoproveedor, productos.nombre, productos.url_amigable,
        productos.idunidad, productos.idiva, productos.idproveedor, productos.costo, productos.controlarstock, productos.trazabilidad,
        productos.combo, productos.stocknegativo, productos.mostrartienda, productos.observacion, productos.obstienda,
        productos.obsinterna, productos.ML_item_id, productos.ML_item_id2,
        ".$sucursales['precios']."
        ".$sucursales['stock']."
        categorias_rubros.nombre AS rubro,
        tablas_unidades.nombre AS unidad,
        proveedores.nombre AS proveedor,
        tablas_ivas.nombre AS iva,
        monedas.simbolo
    FROM productos
        LEFT JOIN categorias_rubros ON productos.idrubro = categorias_rubros.idrubro
        LEFT JOIN tablas_unidades ON productos.idunidad = tablas_unidades.idunidad
        LEFT JOIN proveedores ON productos.idproveedor = proveedores.idproveedor
        LEFT JOIN tablas_ivas ON productos.idiva = tablas_ivas.idiva
        ".$sucursales['joinlistas']."
        ".$sucursales['joinstock']."
        LEFT JOIN monedas ON productos.idmoneda = monedas.idmoneda
    WHERE productos.idproducto = '".$id."'
    LIMIT 1")
);

$tienda = array_sql(consulta_sql("SELECT ML_estado, MP_estado FROM tienda LIMIT 1"));
$estado_ml = ($_SESSION['modulo_ML'] && ($tienda['ML_estado'] || $tienda['MP_estado']));

exportar('
            <div id="prn_ventana_cuerpo">
                <div id="prn_ventana_cuerpo_inicio">
                    <p class="prn_p_titulo_ventana_cuerpo">'.$i18n[11].$datos['nombre'].'</p>
               </div> <!--Cierro el div id="ventana_cuerpo_inicio-->

                    <table class="prn_t_ventana_cuerpo" width="100%">
                    <tr>
                        <td class="prn_td_subtitulos">'.$i18n[18].'</td>
                        <td class="prn_td_subtitulos">'.$i18n[20].'</td>
                    </tr>
                    <tr>
                        <td class="prn_td_datos">
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[3].': </span>'.$datos['nombre'].'</p>
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[4].': </span>'.$datos['codigo'].'</p>
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[16].': </span>'.$datos['codigoproveedor'].'</p>
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[5].': </span>'.$datos['unidad'].'</p>
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[8].': </span>'.$datos['rubro'].'</p>
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[17].': </span>'.$datos['proveedor'].'</p>
                        </td>
                        <td class="prn_td_datos">
                            <p class="prn_p_texto_campo"><input type="checkbox" name="estado" value="1" disabled="disabled" '.($datos['estado'] ? 'checked="checked"' : '' ).'> '.$i18n[26].'</p>
                            <p class="prn_p_texto_campo"><input type="checkbox" name="estadoventa" value="1" disabled="disabled" '.($datos['estadoventa'] ? 'checked="checked"' : '' ).'> '.$i18n[145].'</p>
                            <p class="prn_p_texto_campo"><input type="checkbox" name="estadocompra" value="1" disabled="disabled" '.($datos['estadocompra'] ? 'checked="checked"' : '' ).'> '.$i18n[145].'</p>
                            <p class="prn_p_texto_campo"><input type="checkbox" name="estadocombo" value="1" disabled="disabled" '.($datos['estadocombo'] ? 'checked="checked"' : '' ).'> '.$i18n[145].'</p>
                            <p class="prn_p_texto_campo"><input type="checkbox" name="combo" value="1" disabled="disabled" '.($datos['combo'] ? 'checked="checked"' : '' ).'> '.$i18n[147].'</p>
                            <p class="prn_p_texto_campo"><input type="checkbox" name="controlarstock" value="1" disabled="disabled" '.($datos['controlarstock'] ? 'checked="checked"' : '' ).'> '.$i18n[28].'</p>
                            <p class="prn_p_texto_campo"><input type="checkbox" name="stocknegativo" value="1" disabled="disabled" '.($datos['stocknegativo'] ? 'checked="checked"' : '' ).'> '.$i18n[29].'</p>');
                            // <p class="prn_p_texto_campo"><input type="checkbox" name="mostrartienda" value="1" disabled="disabled" '.($datos['mostrartienda'] ? 'checked="checked"' : '' ).'> '.$i18n[87].'</p>
exportar('
                        </td>
                    </tr>');

$resultado_sql = consulta_sql("SELECT iua FROM archivos WHERE tipo='imagen' AND modulo='productos' AND publico='1' AND id='".$id."' LIMIT 6");
if (contar_sql($resultado_sql)) {
    exportar('
                    <tr>
                        <td class="prn_td_subtitulos">Imágenes del producto</td>
                    </tr>
                    <tr>
                        <td>');
    while ($imagen = array_sql($resultado_sql)) {
        exportar('<img class="prn_p_imagen" style="margin-left: 5px; margin-right: 5px;" src="'.URL_S3.$imagen['iua'].'/miniatura'.'" />');
    }
    exportar('
                        </td>
                    </tr>');
}

exportar('
                    <tr>
                        <td class="prn_td_subtitulos">'.$i18n[205].'</td>
                        <td class="prn_td_subtitulos">'.$i18n[83].'</td>
                    </tr>
                    <tr>
                        <td class="prn_td_datos">
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[6].': </span>'.convertir_numero($datos['costo']).'</p>
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[10].': </span>'.convertir_numero($datos['iva'], "porcentaje").'</p>');
                        puntero_sql($sucursales['listas']);
                        while($temp_array = array_sql($sucursales['listas'])){
                            exportar('
                                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[25].' - '.$temp_array['nombre'].': </span>'.convertir_numero($datos['utilidad_'.$temp_array['idlista']], "porcentaje").'</p>
                                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[7].' - '.$temp_array['nombre'].': </span>'.convertir_numero($datos['precio_'.$temp_array['idlista']]).'</p>
                                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[81].' - '.$temp_array['nombre'].': </span>'.convertir_numero($datos['preciofinal_'.$temp_array['idlista']]).'</p>');
                        }
exportar('
                        </td>
                        <td class="prn_td_datos">');

                        puntero_sql($sucursales['depositos']);
                        while($temp_array = array_sql($sucursales['depositos'])){
                            exportar('
                                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[21].' - '.$temp_array['nombre'].': </span>'.convertir_numero($datos['stockactual_'.$temp_array['iddeposito']], 'cantidad').'</p>
                                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[22].' - '.$temp_array['nombre'].': </span>'.convertir_numero($datos['stockideal_'.$temp_array['iddeposito']], 'cantidad').'</p>
                                <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[23].' - '.$temp_array['nombre'].': </span>'.convertir_numero($datos['stockminimo_'.$temp_array['iddeposito']], 'cantidad').'</p>');
                        }
exportar('
                        </td>
                    </tr>');
if($datos['combo']){
exportar('
                    <tr>
                        <td colspan="2" class="prn_td_subtitulos">'.$i18n[148].'</td>
                    </tr>
');
}
extras_exportar();

if ($datos['observacion']){
    exportar('
                    <tr>
                        <td class="prn_td_subtitulos">'.$i18n[84].'</td>
    ');
} elseif (!$datos['observacion'] && $estado_ml) {
    exportar('
                    <tr>
                        <td class="prn_td_subtitulos"></td>
    ');
}
if ($estado_ml){
    exportar('
                        <td class="prn_td_subtitulos">'.$i18n[206].'</td>
                    </tr>
    ');
}
if ($datos['observacion']){
    exportar('      <tr>
                        <td class="prn_td_datos">
                            <p class="prn_p_texto_campo_largo">'.$datos['observacion'].'</p>
                        </td>
    ');
} elseif (!$datos['observacion'] && $estado_ml) {
    exportar('      <tr>
                        <td class="prn_td_datos">
                            <p class="prn_p_texto_campo_largo"></p>
                        </td>
    ');
} if ($estado_ml){
    exportar('
                        <td class="prn_td_datos">
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[204].': </span>'.($datos['sku'] ? : '-').'</p>
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[42].': </span>'.($datos['ML_item_id'] ? $datos['ML_item_id'] : '-').'</p>
                            <p class="prn_p_texto_campo"><span class="prn_p_nombre_campo">'.$i18n[112].': </span>'.($datos['ML_item_id2'] ? $datos['ML_item_id2'] : '-').'</p>
                        </td>
                    </tr>');
}

if ($datos['obstienda'])
    exportar('
                    <tr>
                        <td colspan="2" class="prn_td_subtitulos">'.$i18n[36].'</td>
                    </tr>
                    <tr>
                        <td class="prn_td_datos" colspan="2">
                            <p class="prn_p_texto_campo_largo">'.$datos['obstienda'].'</p>
                    </tr>');

exportar('
                    </table>
            <div id="prn_ventana_cuerpo_fin"> </div>
        </div>  <!--Cierro el <div id="prn_ventana_cuerpo">-->');
