<?php
$_SESSION['control_agrandar_ventas'] = 30;
$anular = recibir_variable('texto', 'anulado_x', false);
$baja = recibir_variable('texto', 'baja_x', false);
//Podría ir true/false, pero quizás nos sirva contar en algún momento cuando esto escale
$cantidad_anular = 0;
$cantidad_baja = 0;

if ($anular || $baja) {
    $ids_array = recibir_marcas('venta_');

    if (empty($ids_array)) {
        ir_inicio($i18n[355]);
    }

    foreach ($ids_array as $idventa) {
        $venta = inicializar_venta($idventa);

        if (!$venta)
            continue;

        if ($anular) {
            $datos = anular_venta($venta, $venta);
            consulta_sql("UPDATE ventas SET estado = 'anulado' WHERE idventa = '$idventa'");

            if ($cantidad_anular <=0) {
                mensajes_alta($i18n[358], 'Confirmacion');
            }
            $cantidad_anular++;

        } elseif ($baja) {

            // Copiado de ventas_baja
            $buscar_monedas = [
                'clientes' => $venta['idcliente'],
                'ventas' => $idventa,
            ];
            $idmonedas = idmonedas($buscar_monedas);

            consulta_sql("DELETE FROM ventas WHERE idventa = '".$idventa."' LIMIT 1");
            consulta_sql("DELETE FROM movimientosxtrazabilidades WHERE tiporelacion = 'venta' AND idrelacion IN (SELECT idproductoxventa FROM productosxventas WHERE idventa = '$idventa')");
            consulta_sql("DELETE FROM productosxventas WHERE idventa = '".$idventa."'");
            consulta_sql("DELETE FROM tributosxventas WHERE idventa = '".$idventa."'");
            consulta_sql("DELETE FROM ventasxventas WHERE idventa = '".$idventa."'");
            consulta_sql("DELETE FROM ventasxventas WHERE idrelacion = '".$idventa."'");
            consulta_sql("DELETE FROM ventasxclientes WHERE idtipoventa = '".$venta['idtipoventa']."' AND id = '".$idventa."' LIMIT 1");
            consulta_sql("DELETE FROM ventas_ml WHERE idventa = '".$idventa."'");
            $resultado_sql = consulta_sql("SELECT idventapago FROM ventaspagos WHERE idventa = '".$idventa."'");
            while ($pago = array_sql($resultado_sql)) {
                consulta_sql("UPDATE ventaspagos SET idventa = '0' WHERE idventapago = '".$pago['idventapago']."' LIMIT 1");
                consulta_sql("UPDATE ventasxclientes SET numero = '' WHERE idtipoventa = '0' AND id = '".$pago['idventapago']."' LIMIT 1");
            }

            // Actualizar saldos
            borrar_saldo('ventas', $idventa);
            $diferencia = ($venta['operacioninversa'] ? 1 : -1) * $venta['total'];
            actualizar_saldo('clientes', $venta['idcliente'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia));
            if ($venta['tiporelacion'] == 'servicio')
                actualizar_saldo('ventasxservicios', $venta['idrelacion'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia));

            logs(true, '', json_encode($venta));
            extras_baja();

            if ($cantidad_baja <=0) {
                mensajes_alta($i18n[362], 'Confirmacion');
            }
            $cantidad_baja++;
        }
    }
}

if ($boton) {
    switch ($boton) {
        // Ordenar por Fecha
        case $i18n[52]: $ventas_ordenar = $_SESSION['control_ventas_ordenar'] == 'fecha ASC' ? 'fecha DESC' : 'fecha ASC'; break;
        // Ordenar por Vencimiento
        case $i18n[160]: $ventas_ordenar = $_SESSION['control_ventas_ordenar'] == 'vencimiento1 ASC' ? 'vencimiento1 DESC' : 'vencimiento1 ASC'; break;
        // Ordenar por Tipo de venta
        case $i18n[157]: $ventas_ordenar = $_SESSION['control_ventas_ordenar'] == 'idtipoventa ASC' ? 'idtipoventa DESC' : 'idtipoventa ASC'; break;
        // Ordenar por Total
        case $i18n[74]: $ventas_ordenar = $_SESSION['control_ventas_ordenar'] == 'total ASC' ? 'total DESC' : 'total ASC'; break;

        case $i18n[346]:// Mostrar las ventas con saldo
            consulta_sql("UPDATE controles SET ventas_ocultar = '1' WHERE idusuario = ".$_SESSION['usuario_idusuario']." LIMIT 1");
            $_SESSION['control_ventas_ocultar'] = 1;
            break;

        case $i18n[347]:// Mostrar las ventas sin saldo
            consulta_sql("UPDATE controles SET ventas_ocultar = '-1' WHERE idusuario = ".$_SESSION['usuario_idusuario']." LIMIT 1");
            $_SESSION['control_ventas_ocultar'] = -1;
            break;

        case $i18n[348]:// Mostrar todas las ventas
            consulta_sql("UPDATE controles SET ventas_ocultar = '0' WHERE idusuario = ".$_SESSION['usuario_idusuario']." LIMIT 1");
            $_SESSION['control_ventas_ocultar'] = 0;
            break;
    }

    if ($boton == $i18n[365]) {
        // Mostrar ventas de todos los usuarios
        $_SESSION['control_ventas_pendientes_usuario'] = 0;
        consulta_sql("UPDATE controles SET ventas_pendientes_usuario = '0' WHERE idusuario = ".$_SESSION['usuario_idusuario']." LIMIT 1");
    }
    if ($boton == $i18n[366]) {
        //Mi usuario y sin especificar
        $_SESSION['control_ventas_pendientes_usuario'] = 1;
        consulta_sql("UPDATE controles SET ventas_pendientes_usuario = '1' WHERE idusuario = ".$_SESSION['usuario_idusuario']." LIMIT 1");
    }

    if ($ventas_ordenar) {
        consulta_sql("UPDATE controles SET ventas_ordenar = '$ventas_ordenar' WHERE idusuario = '".$_SESSION['usuario_idusuario']."' LIMIT 1");
        $_SESSION['control_ventas_ordenar'] = $ventas_ordenar;
    }
}

// Primera consulta: solo IDs con LIMIT
$sql_ids = "SELECT v.idventa
    FROM ventas v
    INNER JOIN categorias_ventas cv ON v.idtipoventa = cv.idtipoventa
        AND cv.tienesituacion = '1'
    WHERE v.estado = 'cerrado'
        AND v.situacion = 'pendiente'
        ".($_SESSION['control_ventas_pendientes_usuario'] == 1 ? 'AND v.idusuario IN ('.$_SESSION['usuario_idusuario'].', 0) ' : '')."
        ".($_SESSION['control_ventas_ocultar'] == 1 ? 'AND EXISTS(SELECT 1 FROM saldos s WHERE s.idrelacion = v.idventa AND s.tiporelacion = "ventas" AND s.saldo > 0) AND v.muevesaldo = 1 ' :
          ($_SESSION['control_ventas_ocultar'] == -1 ? 'AND (NOT EXISTS(SELECT 1 FROM saldos s WHERE s.idrelacion = v.idventa AND s.tiporelacion = "ventas" AND s.saldo > 0) OR EXISTS(SELECT 1 FROM saldos s WHERE s.idrelacion = v.idventa AND s.tiporelacion = "ventas" AND s.saldo <= 0)) AND v.muevesaldo = 1' : ''))."
    ORDER BY v.".$_SESSION['control_ventas_ordenar']."
    LIMIT ".($_SESSION['control_agrandar_ventas'] + 1);

$resultado_ids = consulta_sql($sql_ids);
$ids_ventas = array();
while ($row = array_sql($resultado_ids)) {
    $ids_ventas[] = $row['idventa'];
}

if (!empty($ids_ventas)) {
    // Segunda consulta: datos completos solo de los IDs seleccionados
    $sql = "SELECT idventa, fecha, numero, total, vencimiento1, obsML, ventas.muevesaldo AS cuenta_corriente,
            categorias_ventas.nombre AS tipoventa, categorias_ventas.letra, categorias_ventas.puntodeventa, categorias_ventas.muevesaldo,
            clientes.idcliente, clientes.estado AS cliente_estado, clientes.nombre AS cliente, clientes.idtipoiva, saldos.saldo,
            monedas.simbolo
        FROM ventas
            LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
            LEFT JOIN clientes ON ventas.idcliente = clientes.idcliente
            LEFT JOIN saldos ON ventas.idventa = saldos.idrelacion AND saldos.tiporelacion = 'ventas'
            LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda
        WHERE ventas.idventa IN (".implode(',', $ids_ventas).")
        ORDER BY FIELD(ventas.idventa, ".implode(',', $ids_ventas).")";

    $resultado_sql = consulta_sql($sql);
} else {
    $resultado_sql = false;
}

$cantidad_ventas = count($ids_ventas);

ventana_inicio($i18n[156], '100', array(array('url' => 'ventas.php?a=alta', 'a' => 'alta', 'title' => $i18n[56], 'permiso' => 'ventas_alta')));
{
    contenido_inicio();
    {

        $temp_desplegable_cf = comprobantes_habilitados_condicion(0, 'interno');
        $temp_desplegable_ri = comprobantes_habilitados_condicion(1, 'interno');
        $temp_desplegable_rm = comprobantes_habilitados_condicion(2, 'interno'); // Responsable Monotributo y otros tipos de Monotributo

        $temp_desplegable_total = array_merge($temp_desplegable_cf, $temp_desplegable_ri, $temp_desplegable_rm);
        $desplegable_total = [];
        foreach($temp_desplegable_total as $tmp_array) {
            if (!in_array($tmp_array, $desplegable_total)) {
                array_push($desplegable_total, $tmp_array);
            }
        }

        $temp_desplegable_sesion = array();
        if ($_SESSION['control_ventas_ocultar'] != 1) $temp_desplegable_sesion[] = array('a' => $i18n[346]);
        if ($_SESSION['control_ventas_ocultar'] != -1) $temp_desplegable_sesion[] = array('a' => $i18n[347]);
        if ($_SESSION['control_ventas_ocultar'] != 0) $temp_desplegable_sesion[] = array('a' => $i18n[348]);
        if ($_SESSION['control_ventas_pendientes_usuario'] != 0) $temp_desplegable_sesion[] = array('a' => $i18n[365]);
        if ($_SESSION['control_ventas_pendientes_usuario'] != 1) $temp_desplegable_sesion[] = array('a' => $i18n[366]);

        if ($cantidad_ventas || ($_SESSION['control_ventas_ocultar']
            && contar_sql(consulta_sql(
                "SELECT idventa
                FROM ventas
                    LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
                WHERE categorias_ventas.tienesituacion = '1'
                    AND (ventas.idusuario = '".$_SESSION['usuario_idusuario']."' OR ventas.idusuario = '0')
                    AND ventas.estado = 'cerrado'
                    AND ventas.situacion = 'pendiente'
                    AND total > (SELECT SUM(total) FROM ventaspagos WHERE ventaspagos.idventa = ventas.idventa) LIMIT 1")))) {

            linea_inicio('titulo', 4);
            {
                switch ($_SESSION['control_ventas_ordenar']) {
                    case 'fecha ASC': $imagen[52] = ' ▲'; break;
                    case 'fecha DESC': $imagen[52] = ' ▼'; break;

                    case 'vencimiento1 ASC': $imagen[160] = ' ▲'; break;
                    case 'vencimiento1 DESC': $imagen[160] = ' ▼'; break;

                    case 'idtipoventa ASC': $imagen[157] = ' ▲'; break;
                    case 'idtipoventa DESC': $imagen[157] = ' ▼'; break;

                    case 'total ASC': $imagen[74].= ' ▲'; break;
                    case 'total DESC': $imagen[74].= ' ▼'; break;
                }
                marcas_todas('ventas_todos');
                celda('ordenar', $i18n[52], '12', $imagen[52]);
                celda('ordenar', $i18n[160], '10', $imagen[160]);
                celda('ordenar', $i18n[157], '15', $imagen[157]);
                celda('texto', $i18n[50], '13');
                celda('texto', $i18n[35], '22');
                celda('ordenar', $i18n[74], '13', $imagen[74]);
                celda('texto', $i18n[158]);
            }

            linea_fin(array(
                array(
                    'tipo' => 'desplegable',
                    'a' => 'filtrar',
                    'title' => $i18n_funciones[70],
                    'url' => 'ventas_listar',
                    'desplegable' => $temp_desplegable_sesion
                ))
            );

            $mostrando = $_SESSION['control_agrandar_ventas'];
            while (($venta = array_sql($resultado_sql)) && $mostrando) {
                $mostrando--;

                if (!$venta['muevesaldo'])
                    $venta['saldo'] = 0;
                elseif (!$venta['saldo'])
                    $venta['saldo'] = $venta['total'];

                if ($venta['idtipoiva'] == 1)
                    // El cliente es Responsable inscripto
                    $temp_desplegable = $temp_desplegable_ri;
                else if (in_array($venta['idtipoiva'],[2,4,5]))
                    // El cliente es algún tipo de Monotributista
                    $temp_desplegable = $temp_desplegable_rm;
                else
                    $temp_desplegable = $temp_desplegable_cf;

                if ($venta['muevesaldo'] && $venta['saldo'] > 0) {
                    $temp_boton_pago = array('tipo' => 'imagen', 'url' => 'ventas.php?a=altapago&idventa='.$venta['idventa'].'&idcliente='.$venta['idcliente'], 'a' => 'pago', 'title' => $i18n[360], 'permiso' => 'ventaspagos_alta');
                } else {
                    $temp_boton_pago = [];
                }
                linea_inicio('fila', 4, 'ventas.php?a=ver&id='.$venta['idventa']);
                {
                    marca('venta_'.$venta['idventa']);
                    celda('fechayhora', $venta['fecha'], '12');
                    celda('fecha', $venta['vencimiento1'], '10');
                    celda('texto', $venta['tipoventa'], '15');
                    celda('texto', $venta['letra'].completar_numero($venta['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8), '13', false, 'ventas.php?a=ver&id='.$venta['idventa']);
                    if ($venta['cliente_estado']) {
                        celda('largo', $venta['cliente'], '22', false, 'clientes.php?a=ver&id='.$venta['idcliente']);
                    } else {
                        celda('imagen', $i18n[374], 'imagen', 'deshabilitado');
                        celda('largo', $venta['cliente'], '19', false, 'clientes.php?a=ver&id='.$venta['idcliente']);
                    }
                    celda('moneda', $venta['total'], '10', false, false, false, false, $venta['simbolo'], true);
                    if ($venta['saldo'] == 0 && $venta['muevesaldo'])
                        celda('imagen', $i18n[359], 'imagen', 'pagado');
                    if ($venta['obsML'])
                        celda('imagen', $venta['obsML'], 'imagen', 'info');
                    celda('moneda', $venta['saldo'], false, false, false, false, false, $venta['simbolo'], true);
                }
                linea_fin(array(
                    $temp_boton_pago,
                    array('tipo' => 'desplegable', 'a' => 'alta_relacionada', 'title' => $i18n[127], 'url' => 'ventas_alta_auto.php?a=alta&idventa='.$venta['idventa'], 'desplegable' => $temp_desplegable, 'permiso' => 'ventas_alta'),
                    array('tipo' => 'imagen', 'url' => 'ventas.php?a=mod&id='.$venta['idventa'], 'a' => 'mod', 'title' => $i18n[60], 'permiso' => 'ventas_mod'),
                    array('tipo' => 'exportar', 'url' => 'ventas.php?a=exportar&id='.$venta['idventa'], 'a' => 'exportar', 'title' => $i18n[62])
                    ));
            }

             // Para cuando hay ventas pero están ocultos
            if (!$cantidad_ventas) {
                linea_inicio();
                {
                    celda('texto', $i18n[189]);
                }
                linea_fin();
            }

            if ($cantidad_ventas > $_SESSION['control_agrandar_ventas']) {
                agrandar(array(
                    array('valor' => $i18n_funciones[20], 'url' => 'ventas_agrandar', 'cantidad' => $_SESSION['control_agrandar_ventas']),
                    array('valor' => $i18n_funciones[18], 'url' => 'ventas_agrandar', 'cantidad' => $_SESSION['control_agrandar_ventas'], 'opciones' => 'todos')
                ));
            }
        } else {
            linea_inicio(false, 1);
            {
                celda('texto', $i18n[159]);
            }
            linea_fin(array(
                array(
                    'tipo' => 'desplegable',
                    'a' => 'filtrar',
                    'title' => $i18n_funciones[70],
                    'url' => 'ventas_listar',
                    'desplegable' => $temp_desplegable_sesion
                ))
            );
        }
    }
    contenido_fin();

    contenido_inicio();
    {
        texto('italica', false, ($_SESSION['control_ventas_ocultar'] != 0 ? ($_SESSION['control_ventas_ocultar'] == 1 ? $i18n[349] : $i18n[350]) : '') . ($_SESSION['control_ventas_pendientes_usuario'] == 1 ? $i18n[370] : $i18n[371]), 'auto', false, 'info');
    }
    contenido_fin();

    linea_unica(array(
        array(
            'tipo' => 'desplegable',
            'a' => 'alta_relacionada',
            'title' => $i18n[127],
            'url' => 'ventas_alta_auto.php?a=altamasiva',
            'desplegable' => $desplegable_total,
            'permiso' => 'ventas_alta',
            'confirma' => $i18n[356],
            'opciones' => ($_SESSION['servidor_version'] != 'ALFA' ? 'style="display:none;"' : ''),
        ),
        array(
            'tipo' => 'image',
            'a' => 'no',
            'title' => $i18n_funciones['anular'],
            'url' => 'ventas_listar',
            'nombre' => 'anulado',
            'permiso' => 'ventas_mod',
            'confirma' => $i18n[351],
            'elemento' => 'submit',
        ),
        array(
            'tipo' => 'image',
            'a' => 'baja',
            'title' => $i18n_funciones['borrar'],
            'url' => 'ventas_listar',
            'nombre' => 'baja',
            'permiso' => 'ventas_baja',
            'confirma' => $i18n[361],
            'elemento' => 'submit',
            'permiso' => 'ventas_baja',
        ),
        ), $i18n_funciones[274],
        ' id="linea_aplicar_seleccion" '
    );

}
ventana_fin();

?>
<script>
    $("#form_Ventas_pendientes .entrada_checkbox, #form_Ventas_pendientes .opciones_flotantes a.enlace").click(function() {
        if ($('#form_Ventas_pendientes input.entrada_checkbox').is(':checked')) {
            $("#form_Ventas_pendientes #linea_aplicar_seleccion").show();
        } else {
            $("#form_Ventas_pendientes #linea_aplicar_seleccion").hide();
        }

        if ($(this).attr('href') && $(this).attr('href') != "#desmarcar_todos") {
            $("#form_Ventas_pendientes #linea_aplicar_seleccion").show();
        } else if ($(this).attr('href') && $(this).attr('href') == "#desmarcar_todos") {
            $("#form_Ventas_pendientes #linea_aplicar_seleccion").hide();
        }
    });
</script>
