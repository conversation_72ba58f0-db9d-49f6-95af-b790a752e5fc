<?php

switch ($boton) {
    case $i18n_funciones['enviar']: //Enviar
    case $i18n_funciones['aceptar']: //Aceptar
        $datos = recibir_matriz(array(
        array('texto', 'asunto'),
        array('texto', 'adjunto'),
        array('mail', 'mail'),
        array('texto', 'clientes_saldo'),
        array('texto', 'clientes_todos'),
        array('texto', 'clientes_anual'),
        array('texto', 'clientes_mensual'),
        array('texto', 'clientes_testeo'),
        array('texto', 'instancias_api'),
        array('texto', 'instancias_demo'),
        array('texto', 'instancias_eliminadas'),
        array('texto', 'instancias_gratuitas'),
        array('texto', 'newsletters'),
        array('texto', 'instancias_especificas'),
        array('texto', 'usuarios_especificos'),
        array('texto', 'solo_admin'),
        array('texto', 'saldo'),
        array('texto', 'mediante'),
        array('texto', 'destacado')
    ));

        $datos['mail'] = filtro_link($datos['mail']);
        $datos['destinatarios'] = escape_sql($_POST['destinatarios']); // Me salto el filtro porque puede ser muy largo este campo

        if ($boton == $i18n_funciones['enviar']) {
            $destinatarios = json_decode(htmlspecialchars_decode($datos['destinatarios']), true);
            break;
        }

        if ($datos['saldo'] > 0 && !$datos['clientes_saldo']) {
            script_flotante('alerta', $i18n[211]);
            $boton = "";
            break;
        }

        if ($datos['instancias_especificas'] && !lista_ids($datos['instancias_especificas'])) {
            script_flotante('alerta', $i18n[213]);
            $boton = "";
            break;
        }

        if ($datos['usuarios_especificos'] && !lista_ids($datos['usuarios_especificos'])) {
            script_flotante('alerta', $i18n[229]);
            $boton = "";
            break;
        }

        $where = '';
        $join = '';

        //Todes
        if (!$datos['clientes_todos']) {
            //Saldo
            if ($datos['saldo'] > 0 && $datos['clientes_saldo']) {
                $where .= ' AND saldos.saldo >= '.$datos['saldo'];
            }

            //Mensual
            if ($datos['clientes_mensual']) {
                $where .= ' AND clientes.idtipocliente = 1 ';
            }

            //Anual
            if ($datos['clientes_anual']) {
                $where .= ' AND clientes.idtipocliente = 3 ';
            }

            if ($datos['clientes_mensual'] || $datos['clientes_anual']) {
                $join .= ' LEFT JOIN categorias_clientes ON clientes.idtipocliente = categorias_clientes.idtipocliente ';
            }

            //Testeo
            if ($datos['clientes_testeo']) {
                $where .= ' AND clientes.idcliente IN ("1140", "1546", "2086") ';
            }
        }

        //Query clientes
        if (($datos['saldo'] > 0 && $datos['clientes_saldo']) ||
                $datos['clientes_todos'] || $datos['clientes_anual'] || $datos['clientes_mensual'] || $datos['clientes_testeo']) {
            $resultado_clientes_sql = consulta_sql("SELECT clientes.idcliente, clientes.nombre AS cliente, clientes.mail, saldos.saldo
                FROM clientes
                LEFT JOIN saldos ON saldos.tiporelacion = 'clientes' AND clientes.idcliente = saldos.idrelacion
                ".$join."
                WHERE clientes.estado = '1' AND clientes.mail != '' ".$where."
                ");
        }

        //API
        $where_empresa = '';
        if ($datos['instancias_api']) {
            $resultado_instancias_api_sql = consulta_sql("SELECT idempresa, nombre AS cliente, mail, 0 AS saldo FROM empresas WHERE idsistema = '3' AND estado = 'activada'", 'saasargentina');
        }
        //Instancias eliminadas
        if ($datos['instancias_eliminadas']) {
            $resultado_instancias_eliminadas_sql = consulta_sql("SELECT idempresa, nombre AS cliente, mail, 0 AS saldo FROM empresas WHERE estado = 'eliminada' ", 'saasargentina');
        }
        //Instancias gratuitas
        if ($datos['instancias_gratuitas']) {
            $resultado_instancias_gratuitas_sql = consulta_sql("SELECT idempresa, nombre AS cliente, mail, 0 AS saldo FROM empresas WHERE idsistema = '4' AND estado = 'activada'", 'saasargentina');
        }
        //Instancias específicas
        if ($datos['instancias_especificas']) {
            $resultado_instancias_especificas_sql = consulta_sql("SELECT idempresa, nombre AS cliente, mail, 0 AS saldo FROM empresas WHERE idempresa IN (".$datos['instancias_especificas'].")", 'saasargentina');
        }
        //Usuarios específicos
        if ($datos['usuarios_especificos']) {
            $resultado_usuarios_especificos_sql = consulta_sql("SELECT idempresa, mail AS cliente, mail, 0 AS saldo FROM usuarios WHERE idusuario IN (".$datos['usuarios_especificos'].")", 'saasargentina');
        }
        //Instancias demo
        if ($datos['instancias_demo']) {
            $resultado_instancias_demo_sql = consulta_sql("SELECT demos.idempresa, empresas.nombre AS cliente, demos.mail, 0 AS saldo FROM demos LEFT JOIN empresas ON empresas.idempresa = demos.idempresa WHERE fechaacceso != '0000-00-00 00:00:00'", 'saasargentina');
        }
        //Newsletters
        if ($datos['newsletters']) {
            // Cómo hago la relación con empresas? por idempresa? puede haber newsletters sin idempresa?
            $resultado_newsletter_sql = consulta_sql("SELECT null AS idempresa, null AS cliente, mail, 0 AS saldo FROM newsletters WHERE estado = '1'", 'saasargentina');
        }
        //Empresas que quitamos
        //if ($datos['instancias_api'] || $datos['instancias_demo'] || $datos['instancias_eliminadas'] || $datos['instancias_gratuitas'] || $datos['instancias_especificas'] || $datos['newsletters']) {
        $quitar_empresas = array_sql(consulta_sql("SELECT mail FROM newsletters WHERE estado != '1'", 'saasargentina'));
        //}

        $total_registros =  array_merge(
                                array_all_sql($resultado_clientes_sql),
                                array_all_sql($resultado_instancias_api_sql),
                                array_all_sql($resultado_instancias_eliminadas_sql),
                                array_all_sql($resultado_instancias_gratuitas_sql),
                                array_all_sql($resultado_instancias_especificas_sql),
                                array_all_sql($resultado_usuarios_especificos_sql),
                                array_all_sql($resultado_instancias_demo_sql),
                                array_all_sql($resultado_newsletter_sql)
                            );

        break;

    case $i18n_funciones['cancelar']:
    case $i18n_funciones['cerrar']:
        ir_inicio();
        break;

    default:
        break;
}

ventana_inicio($i18n[131], '100');
{
    if (!$boton) {
        contenido_inicio();
        {
            entrada('texto', 'instancias_especificas', $i18n[210], false, '20', '200', $i18n[212]);
            entrada('texto', 'usuarios_especificos', $i18n[231], false, '20', '200', $i18n[212]);
            marcas($i18n[227], '12', array(array('nombre' => 'solo_admin', 'titulo' => '', 'valor' => $datos['solo_admin'])), $i18n[228]);

            $array_selector_mediante = array(
                array('id' => 'mail', 'valor' => $i18n_funciones[311]),
                array('id' => 'notificaciones', 'valor' => $i18n_funciones[304]),
                array('id' => 'mail_notificaciones', 'valor' => $i18n_funciones[166]),
            );
            selector_array('mediante', $i18n[230], false, '40', $array_selector_mediante);
            marcas($i18n[214], '10', array(array('nombre' => 'destacado', 'titulo' => '', 'valor' => $datos['destacado'])));

            $array_marcas = array(
                array('nombre' => 'clientes_saldo', 'titulo' => $i18n[132], 'custom_field' => '<input type="number" id="mailing_cliente_saldo" name="saldo"
                min="0" />'),
                array('nombre' => 'clientes_todos', 'titulo' => $i18n[133]),
                array('nombre' => 'clientes_mensual', 'titulo' => $i18n[134]),
                array('nombre' => 'clientes_anual', 'titulo' => $i18n[135]),
                array('nombre' => 'clientes_testeo', 'titulo' => $i18n[138]),
                array('nombre' => 'instancias_api', 'titulo' => $i18n[205]),
                array('nombre' => 'instancias_demo', 'titulo' => $i18n[206]),
                array('nombre' => 'instancias_eliminadas', 'titulo' => $i18n[207]),
                array('nombre' => 'instancias_gratuitas', 'titulo' => $i18n[208]),
                array('nombre' => 'newsletters', 'titulo' => $i18n[209])
            );
            marcas($i18n_funciones[47], '50', $array_marcas);

            selector_array('remitente', $i18n_funciones[48], $datos['remitente'], '50',
                [['id' => 'empresa', 'valor' => $_SESSION['empresa_nombre'] . ' &lt;' . $_SESSION['configuracion_mail'] . '&gt;']]);
            entrada('texto', 'asunto', $i18n_funciones[49], $datos['asunto'], '50', '200');

            $resultado_sql = consulta_sql("SELECT * FROM plantillas");

            if (contar_sql($resultado_sql)) {
                echo "<script>var plantillas = [];";
                    $array_selector_opciones = array(
                    array('id' => '', 'valor' => $i18n_funciones[91]),
                );

                while ($plantilla = array_sql($resultado_sql)) {
                    $array_selector_opciones[] = array(
                        'id' => $plantilla['idplantilla'],
                        'valor' => $plantilla['asunto'],
                        'datasets' => array(
                            'data-asunto-nombre' => $plantilla['asunto_nombre'],
                        )
                    );

                    $texto_final = $plantilla['texto'];

                    echo "plantillas[" . $plantilla['idplantilla'] . "] = '" . rawUrlEncode(str_replace(array("\r", "\n", "'"), "", $texto_final)) . "';";
                }
                echo "</script>";
                selector_array('idplantilla', $i18n_funciones[90], $datos['idplantilla'], '50', $array_selector_opciones);

            } else {
                texto('texto', false, $i18n_funciones[104], '50');
            }

            area('mail', $i18n_funciones[50], $datos['mail']);
        }
        contenido_fin();

        botones(array(array('valor' => $i18n_funciones['aceptar']), array('valor' => $i18n_funciones['cancelar'])));

    } elseif ($boton == $i18n_funciones['aceptar']) {
        contenido_inicio();
        {
            entrada('hidden', 'remitente', '', $datos['remitente']);
            entrada('hidden', 'asunto', '', $datos['asunto']);
            entrada('hidden', 'adjunto', '', $datos['adjunto']);
            entrada('hidden', 'mail', '', $datos['mail']);
            entrada('hidden', 'mediante', '', $datos['mediante']);
            entrada('hidden', 'destacado', '', $datos['destacado']);
            texto('texto', false, $i18n[136], '100');

            $evitar_repetidos = [];
            $destinatarios = [];
            foreach($total_registros as $cliente) {
                if (filter_var($cliente['mail'], FILTER_VALIDATE_EMAIL)
                        && !in_array($cliente['mail'], $quitar_empresas)
                        && !in_array($cliente['mail'], $evitar_repetidos)) {
                    texto('texto', false, (count($destinatarios) + 1) . ' - Cliente: ' . $cliente['cliente'] . ' | email: ' . $cliente['mail'] . ' | Saldo: ' .$cliente['saldo'], '100');
                    echo '<br>';
                    $evitar_repetidos[] = $cliente['mail'];
                    $cliente['cliente'] = str_replace(['"', "'"], "", $cliente['cliente']);
                    $destinatarios[] = $cliente;
                }
            }
            entrada('hidden', 'destinatarios', '', htmlspecialchars(json_encode($destinatarios, JSON_HEX_QUOT | JSON_HEX_APOX)));
        }
        contenido_fin();
        botones(array(array('valor' => $i18n_funciones['enviar']), array('valor' => $i18n_funciones['cancelar'])));

    } elseif($boton == $i18n_funciones['enviar']) {

        contenido_inicio();
        {
            texto('texto', false, $i18n[137], '100');

            foreach($destinatarios as $cliente) {
                $datos['mail_enviar'] = $datos['mail'];

                //Reemplazo de datos plantilla//
                $datos['mail_enviar'] = str_replace(
                    ['{{nombre}}', '{{saldo}}'],
                    [$cliente['cliente'], $cliente['saldo']],
                    $datos['mail_enviar']);
                //Botón MP del saldo del cliente
                if (strpos($datos['mail_enviar'], '{{mp-cliente}}') !== false) {
                    try {
                        $preference = crear_boton_mp(
                            MP_CLIENT_ID, //$mp_id
                            MP_CLIENT_SECRET, //$mp_secret
                            abs($cliente['saldo']), //$saldo
                            $i18n_funciones[168].$_SESSION['empresa_nombre'], //$titulo
                            $cliente['mail'], //$mail_cliente
                            SAAS_ID.'-'.$cliente['idcliente'], //$external_reference
                            $i18n_funciones[168].$_SESSION['empresa_nombre'] //$descripcion
                        );
                        $boton = '<a href="'.$preference["response"]["init_point"].'" name="MP-Checkout" class="blue-tr-l-rn-arall"> <img alt="Pagar con MercadoPago" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/btn-pago-mp.jpg"></a>';
                        sleep(1); //no sé qué onda generar tantos botones de pago todos juntos... ojo al piojo
                    } catch (Exception $ex) {
                        texto('texto', false, $i18n[140], 'auto');
                        mostrar_error($ex, true);
                    }
                    $datos['mail_enviar'] = str_replace("{{mp-cliente}}", $boton, $datos['mail_enviar']);
                }
                //if(strpos($datos['mail'], '{{mp-cliente}}') !== false) //Saldo de la venta (ver a futuro)
                //Fin reemplazo datos plantilla//

                $evitar_repetidos = [];
                if (filter_var($cliente['mail'], FILTER_VALIDATE_EMAIL)
                        && !in_array($cliente['mail'], $quitar_empresas)
                        && !in_array($cliente['mail'], $evitar_repetidos)) {

                    if ($datos['mediante'] == 'mail' || $datos['mediante'] == 'mail_notificaciones') {
                        $resultado = email_queue([$_SESSION['configuracion_mail'] => $_SESSION['empresa_nombre']],
                            $cliente['mail'], $datos['asunto'], $datos['mail_enviar']);
                    }

                    if ($datos['mediante'] == 'notificaciones' || $datos['mediante'] == 'mail_notificaciones') {
                        $resultado = enviar_notificacion($datos, $cliente['idempresa'] ?? buscar_idempresa($cliente['idcliente']));
                    }

                    texto('texto', false, $cliente['mail'] . ($resultado ? ' : OK' : ' : Error'), '100');
                    echo '<br>';
                    $evitar_repetidos[] = $cliente['mail'];
                }
            }
        }
        contenido_fin();
        botones(array(array('valor' => $i18n_funciones['cerrar'])));
    }

}
ventana_fin();

?>
<script>
    $("select[name=idplantilla]").change(function () {
        var asunto_original = '<?php echo $datos['asunto'];?>';
        var idplantilla = $("select[name=idplantilla] option:selected").val();
        var asunto = $("select[name=idplantilla] option:selected").html();
        var asunto_nombre = $("select[name='idplantilla'] option:selected").attr('data-asunto-nombre');
        if (idplantilla){
            $(".entrada_textarea").setCode(decodeURIComponent(plantillas[idplantilla]));
            if(asunto_nombre == 1){
                $("#asunto").val(asunto);
            } else {
                $("#asunto").val(asunto_original);
            }
        } else{
            $(".entrada_textarea").setCode("");
            $("#asunto").val(asunto_original);
        }
    });
</script>
