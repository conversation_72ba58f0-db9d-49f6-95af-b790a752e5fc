<?php

switch ($boton) {

    case $i18n_funciones[27]: // Agregar
        $datos = recibir_matriz(array(
        array('texto', 'asunto_nombre'),
        array('texto', 'asunto'),
        array('largo', 'texto')
    ));

        consulta_sql("INSERT INTO plantillas (asunto_nombre, asunto,texto) VALUES ('".$datos['asunto_nombre']."', '".$datos['asunto']."','".$datos['texto']."')");
        extras_alta();
        ir_ahora('configuraciones.php?a=plantillas');
        break;

    case $i18n_funciones[23]: // Cancelar
        ir_atras();
        break;

    default:
        $mp = array_sql(consulta_sql("SELECT MP_estado, MP_client_id, MP_client_secret FROM tienda WHERE idtienda = 1 LIMIT 1"));
        break;

}

ventana_inicio($i18n[450]);
{
    contenido_inicio();
    {
        entrada('hidden', 'mp_estado', '', $mp['MP_estado']);
        entrada('hidden', 'mp_client_id', '', ($mp['MP_client_id'] ? '1' : ''));
        entrada('hidden', 'mp_client_secret', '', ($mp['MP_client_secret'] ? '1' : ''));
        entrada('texto', 'asunto', $i18n[24], $datos['titulo'], '50', '200');
        texto('italica', false, $i18n[514], '50', false, 'info', false, 'id="info-tag"');
        area('texto', $i18n[444], $datos['texto'], 'auto', false, '', true, ($mp['MP_estado'] && $mp['MP_client_id'] && $mp['MP_client_secret'] ? true : false));
        marcas('', 'auto', array(array('nombre' => 'asunto_nombre', 'titulo' => $i18n[555], 'valor' => $datos['asunto_nombre'])));
    }
    contenido_fin();

    if (!$mp['estado'] || !$mp['MP_client_secret']) {
        contenido_inicio();
        {
            texto('italica', false, $i18n[617], 'auto', false, 'pago');
            enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_MP_autorizar', 'id' => $id, 'valor' => $i18n[618])));
        }
        contenido_fin();
    }

    botones(array(array('valor' => $i18n_funciones[27]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();

$resultado_sql = consulta_sql("SELECT * FROM tablas_tags ORDER BY FIELD(modulo, 'ventas', 'clientes', 'servicios', 'compras'), idtag ASC");
$opciones = "";
while ($tag = array_sql($resultado_sql)) {
    $opciones .= "<option value='{{".$tag['tag']."}}'>En ".$tag['modulo'].': '.$tag['nombre']."</option>";
}

?>
<script type="text/javascript">
function agregar_tags()
{
    $("#redactor_tag").html("<?=$opciones?>");
};
</script>
