<?php
$datos = obtener_debito_automatico();

if ($boton && $boton == $i18n[65]) {
    $datos['debito_automatico'] = recibir_variable('texto', 'debito_automatico', false);
    $datos['cbu'] = recibir_variable('texto', 'cbu', false);

    if (strlen($datos['cbu']) != 22) {
        $datos['debito_automatico'] = '';
        $datos['cbu'] = '';
        script_flotante('alerta', $i18n[671]);
    } else {
        // Débito lista
        consulta_sql("REPLACE INTO datosxextras SET
                idextraxmodulo = 1,
                idrelacion = '".$datos['idcliente']."',
                idlistaxextra = '".($datos['debito_automatico'] ? 2 : 1)."'
            ", 'admin');

        // CBU texto
        consulta_sql("REPLACE INTO datosxextras SET
            idextraxmodulo = 2,
            idrelacion = '".$datos['idcliente']."',
            texto = '".$datos['cbu']."'
        ", 'admin');

        // MENSUAL - 5% (idtipocliente = 12)
        if ($datos['debito_automatico']) {
            consulta_sql("UPDATE clientes SET
                idtipocliente = 12
                WHERE idcliente = '".$datos['idcliente']."'
                AND idtipocliente = 1
            ", 'admin');
            if (!contar_sql(consulta_sql(
                "SELECT 1 FROM clientes WHERE idcliente = '".$datos['idcliente']."' AND idtipocliente = 12", 'admin'))) {
                $mail = "La empresa N° {$_SESSION['empresa_idempresa']} de nombre {$_SESSION['empresa_nombre']} "
                        ."(<a href='".URL_SAAS."/saas.php?a=verempresa&id=".$_SESSION['empresa_idempresa']."'>".URL_SAAS."/saas.php?a=verempresa&id=".$_SESSION['empresa_idempresa']."</a>)"
                        ." habilitó el débito automático de su cuenta y no se actualizó a categoría Mensual -5%. Hay que revisar que esté todo en orden.";
                email_queue(MAIL_SERVIDOR, MAIL_INFO, "Revisar débito automático {$_SESSION['empresa_idempresa']}", $mail);
            }
        }
    }
}

ventana_inicio($i18n[469]);
{
    if ($_SESSION['sistema_gratis'] && $_SESSION['saldo'] == '0.00') {
        contenido_inicio(false, '100');
        {
            texto('italica', false, $i18n[479]);
        }
        contenido_fin();

    } else {

        solapas_inicio(array(
            array('nombre' => $i18n[484]),
            array('nombre' => $i18n[474]),
            array('nombre' => $i18n[483], 'url' => 'configuraciones_pagar_mp'),
            //array('nombre' => $i18n[481]),
        ));
        {
            solapa_inicio($i18n[484]);
            {
                if ($datos['debito_automatico'] && $datos['cbu']) {
                    contenido_inicio($i18n[663], '33');
                    {
                        texto('italica', false, $i18n[674], false, false, 'info');
                        bajo_linea();
                        entrada('numeros', 'cbu', $i18n[668], $datos['cbu'], 'auto', 22, false, 'autocomplete="off"');
                        enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_debito_automatico_autorizar', 'id' => $id, 'valor' => $i18n[669])), '50');
                    }
                    contenido_fin();
                } else {
                    contenido_inicio($i18n[663]);
                    {
                        observacion(false, $i18n[693].$i18n[664]);
                        marcas(false, 'auto', [['nombre' => 'debito_automatico', 'titulo' => $i18n[665], 'valor' => $datos['debito_automatico'], 'opciones' => 'onclick="checkearCondiciones()"']]);
                    }
                    contenido_fin();

                    contenido_inicio(false, '33');
                    {
                        entrada('numeros', 'cbu', $i18n[668], $datos['cbu'], 'auto', 22, false, 'autocomplete="off"');
                        bajo_linea();
                        texto('italica', false, $i18n[687], false, false, 'info');
                        botones(array(array('valor' => $i18n[65]), array('valor' => $i18n[66])));
                    }
                    contenido_fin();
                }
            }
            solapa_fin();

            solapa_inicio($i18n[474]);
            {
                contenido_inicio();
                {
                    texto('moneda', ((float)$_SESSION['saldo'] < 0 ? $i18n[478] : $i18n[477]), $_SESSION['saldo']);
                    observacion(false, $i18n[480]);
                    if ((float)$_SESSION['saldo'] > 0 || $_SESSION['empresa_estado'] == 'vencida') {
                        texto('texto', $i18n[647]);
                        botones(array(array('tipo' => 'modal-dialog', 'url' => 'archivos_alta', 'valor' => $i18n[648] , 'modulo' => 'configuraciones', 'orientacion' => 'boton-izquierda')));
                    } else {
                        observacion(false, $i18n[656]);
                    }
                }
                contenido_fin();
            }
            solapa_fin();

            solapa_inicio($i18n[483]);
            solapa_fin();

            solapa_inicio($i18n[481]);
            {
                contenido_inicio();
                {
                    observacion(false, $i18n[482]);
                    enlaces(false, array(array('tipo' => 'modal', 'url' => 'tickets_alta', 'valor' => $i18n[488], 'modulo' => 'tickets')));
                }
                contenido_fin();
            }
            solapa_fin();

        }
        solapas_fin();

    }
}
ventana_fin();

?>
<script>
    $(function() {
        checkearCondiciones();
    });
</script>