<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('entero', 'idusuario'),
        array('entero', 'idtiposervicio'),
        array('texto', 'prioridad'),
        array('texto', 'clientes_id'),
        array('texto', 'titulo'),
        array('fecha', 'fechasolicitado'),
        array('fecha', 'fechainicio'),
        array('fecha', 'fechafin'),
        array('fecha', 'fechalimite'),
        array('tiempo', 'tiempoestimado'),
        array('tiempo', 'tiempodedicado'),
        array('largo', 'obssolicitado'),
        array('largo', 'obsrealizado'),
        array('largo', 'obsinterna'),
        array('tiempo', 'contartiempo')
    ));
    $datos['idcliente'] = $datos['clientes_id'] ? $datos['clientes_id'] : 1;
} else {
    $datos = array('idservicios' => '', 'idusuario' => $_SESSION['usuario_idusuario'], 'idtiposervicio' => $_SESSION['control_ultimotiposervicio'], 'prioridad' => 3, 'idcliente' => 0, 'fechasolicitado' => 'ahora');
}

switch ($boton) {
    case $i18n_funciones[27]: //Agregar
        if ($datos['fechafin']) { // Terminado
            $estado =  '3';
            $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
            $fechafin = fecha_sql($datos['fechafin']);

        } elseif ($datos['fechainicio'] || $datos['obsrealizado'] || ($datos['tiempodedicado'] && $datos['tiempodedicado'] != '00:00:00')) { // Comenzado
            $estado =  '2';
            $fechainicio = ($datos['fechainicio']) ? fecha_sql($datos['fechainicio']) : fecha_sql('ahora');
            $fechafin = '';

        } else { // Sin comenzar
            $estado =  '1';
            $fechainicio = '';
            $fechafin = '';
        }
        consulta_sql("UPDATE controles SET ultimotiposervicio='".$datos['idtiposervicio']."' WHERE idusuario='".$_SESSION['usuario_idusuario']."' LIMIT 1");
        $_SESSION['control_ultimotiposervicio'] = $datos['idtiposervicio'];
        consulta_sql("INSERT INTO servicios SET
            idcliente='".$datos['idcliente']."',
            idusuario='".$datos['idusuario']."',
            idtiposervicio='".$datos['idtiposervicio']."',
            estado='".$estado."',
            prioridad='".$datos['prioridad']."',
            titulo='".$datos['titulo']."',
            fechasolicitado='".fecha_sql($datos['fechasolicitado'])."',
            fechainicio='".$fechainicio."',
            fechafin='".$fechafin."',
            fechalimite='".fecha_sql($datos['fechalimite'])."',
            tiempoestimado='".$datos['tiempoestimado']."',
            tiempodedicado='".$datos['tiempodedicado']."',
            contartiempo='".$datos['contartiempo']."',
            obssolicitado='".$datos['obssolicitado']."',
            obsrealizado='".$datos['obsrealizado']."',
            obsinterna='".$datos['obsinterna']."'
            ");

        $id = id_sql();
        fullsearch_insert('servicios', $id);
        insertar_saldo('comprasxservicios', $id);
        insertar_saldo('ventasxservicios', $id);
        extras_alta();
        ir_ahora('servicios.php?a=ver&id='.$id);
        break;

    case $i18n_funciones[23]: // Cancelar
        ir_atras();
        break;
}

ventana_inicio($i18n[34]);
{
    // Seleccion de clientes
    contenido_inicio($i18n[45], '100', false, false, $i18n[33]);
    {
        seleccionador('clientes');
    }
    contenido_fin();

    // Datos basicos
    contenido_inicio($i18n[35]);
    {
        selector_array('prioridad', $i18n[37], $datos['prioridad'], '33', array(array('id' => '1', 'valor' => $i18n['prioridad_1']), array('id' => '2', 'valor' => $i18n['prioridad_2']), array('id' => '3', 'valor' => $i18n['prioridad_3']), array('id' => '4', 'valor' => $i18n['prioridad_4']), array('id' => '5', 'valor' => $i18n['prioridad_5'])));
        if ($_SESSION['perfil_servicios_alta_todos']) {
            selector('idusuario', $i18n[36], $datos['idusuario'], '33', 'usuarios', 'nombre', false, true, true);
        } else {
            selector_array('idusuario', $i18n[36], false, '33', array(array('id' => $_SESSION['usuario_idusuario'], 'valor' => $_SESSION['usuario_nombre'])));
        }
        selector_familiar('idtiposervicio', $i18n[43], $datos['idtiposervicio'], '33', 'categorias_servicios', true, true, true);
    }
    contenido_fin();

    // Fechas y tiempos
    contenido_inicio($i18n[120]);
    {
        entrada('fechayhora', 'fechasolicitado', $i18n[104], $datos['fechasolicitado'], '14');
        entrada('fechayhora', 'fechainicio', $i18n[15], $datos['fechainicio'], '14');
        entrada('fechayhora', 'fechafin', $i18n[105], $datos['fechafin'], '14');
        entrada('fechayhora', 'fechalimite', $i18n[233], $datos['fechalimite'], '14');
        entrada('tiempo', 'tiempoestimado', $i18n[234], $datos['tiempoestimado'], '14');
        entrada('tiempo', 'tiempodedicado', $i18n[235], $datos['tiempodedicado'], '14');
        marcas(' ', '15', array(array('nombre' => 'contartiempo', 'titulo' => $i18n[248], 'valor' => $datos['contartiempo'])));
    }
    contenido_fin();

    extras();

    // Observaciones
    contenido_inicio($i18n[76]);
    {
        entrada('texto', 'titulo', $i18n[236], $datos['titulo'], 'auto', '200');
        area('obssolicitado', $i18n[115], $datos['obssolicitado']);
        area('obsrealizado', $i18n[116], $datos['obsrealizado']);
        area('obsinterna', $i18n[117], $datos['obsinterna']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n_funciones[27]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();
