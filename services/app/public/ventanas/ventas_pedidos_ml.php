<?php

$idtienda = recibir_variable('entero', 'idtienda', true);
$tiendas_sql = consulta_sql("SELECT * FROM tienda WHERE ML_access_token != '' ORDER BY idtienda");
$tienda = array_sql(consulta_sql(
    "SELECT * FROM tienda
    WHERE ML_access_token != ''
    ".($idtienda ? "AND idtienda = '$idtienda'" : "")
    ."ORDER BY idtienda LIMIT 1"));

if ($boton && $boton = $i18n_funciones['agregar']) {
    $marcas = recibir_marcas('pedido_');
    $marcas_en_cola = [];
    foreach ($marcas as $marca) {
        $marcas_en_cola[] = $marca;
        $datos = [
            "topic"             =>  "orders",
            "application_id"    =>  ML_APP_ID,
            "user_id"           =>  $tienda['ML_user_id'],
            "resource"          =>  '/orders/'.$marca,
            "attempts"          =>  1,
            "sent"              =>  gmDate("Y-m-d\TH:i:s.000\Z"),
            "received"          =>  gmDate("Y-m-d\TH:i:s.000\Z"),
        ];
        queue_ml('notificaciones/mercadolibre', json_encode($datos, JSON_UNESCAPED_SLASHES));
    }
    mensajes_alta($i18n[327].implode(', ', $marcas_en_cola), 'Confirmacion');
    ir_atras();
}

if ($tienda['ML_access_token']) {

    $ventas = array_all_sql(consulta_sql(
        "SELECT ventas_ml.idventa, ventas_ml.ML_order_id, ventas.idcliente, numero, fecha, puntodeventa, letra,
            clientes.nombre AS cliente,
            monedas.simbolo
        FROM ventas_ml
        LEFT JOIN ventas ON ventas.idventa = ventas_ml.idventa
        LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
        LEFT JOIN clientes ON ventas.idcliente = clientes.idcliente
        LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda
        WHERE fecha >= '".date("Y-m-d H:i:s")."' - INTERVAL ".(HORAS_PEDIDOS_ML*2)." HOUR
        "), 'ML_order_id');

    $tienda['ML_access_token'] = refrescar_ml($tienda);

    if (!$tienda['ML_access_token']) {
        mensajes_alta($i18n_funciones[215]);
        ir_atras();
    }

    desconectar_db();

    echo '<script>
        bloquear();
        </script>';
    $offset = 50;
    $url = 'https://api.mercadolibre.com/orders/search/recent'
        .'?seller='.$tienda['ML_user_id']
        .'&access_token='.$tienda['ML_access_token']
        .'&sort=date_asc&order.date_closed.from='.gmDate("Y-m-d\TH:i:s\.000-03:00", time() - (HORAS_PEDIDOS_ML * 3600));

    $pedidos = pedidos_curl_mercado($url);
    $total = $pedidos['paging']['total'];

    while ($total > $offset) {
        $temp_pedidos = pedidos_curl_mercado($url.'&offset='.$offset);
        $pedidos['results'] = array_merge($pedidos['results'], $temp_pedidos['results']);
        $offset += 50;
    }
    echo '<script>
        desbloquear();
        </script>';
}

ventana_inicio($i18n[326]);
{
    if (contar_sql($tiendas_sql) > 1) {
        contenido_inicio();
        {
            $tiendas = array();
            puntero_sql($tiendas_sql);
            while ($temp_array = array_sql($tiendas_sql)) {
                $tiendas[] = array('id' => $temp_array['idtienda'], 'valor' => $temp_array['tienda_nombre']);
            }
            selector_array('idtienda', $i18n[323], $idtienda, '25', $tiendas, false, 'onchange="recargar_ventana_mercado('."'pedidos_ml'".')"');

        }
        contenido_fin();
    }

	contenido_inicio();
    {
       	linea_inicio('titulo');
        {
            marcas_todas('ventas_todas');
            celda('imagen', '', 'imagen');
            celda('texto', $i18n[52], '15');    // Fecha
            celda('texto', $i18n[324], '15');    // Order id
            celda('texto', $i18n[35], '20');    // Cliente
            celda('texto', $i18n_funciones['ventas_singular'], '20');    // Venta
            celda('texto', $i18n[7], '20');     // Precio final
        }
        linea_fin();

        foreach ($pedidos['results'] as $pedido) {
            linea_inicio('fila', '', '', 'id="linea_'.$pedido['id'].'"');
            {
                if (!array_key_exists($pedido['id'], $ventas)) {
                    $temp_estado = 'no';
                    $idcliente = '';
                    $cliente = $pedido['buyer']['nickname'];
                    $fecha = new DateTime($pedido['date_created']);
                    $fecha = $fecha->format('d-m-Y H:i');
                    $idventa = '';
                    $numero_venta = '';

                } else {
                    $temp_estado = 'ok';
                    $idcliente = $ventas[$pedido['id']]['idcliente'];
                    $cliente = $ventas[$pedido['id']]['cliente'];
                    $fecha = $ventas[$pedido['id']]['fecha'];
                    $idventa = $ventas[$pedido['id']]['idventa'];
                    $numero_venta = numero_comprobante($ventas[$pedido['id']]['letra'], $ventas[$pedido['id']]['puntodeventa'], $ventas[$pedido['id']]['numero']);
                }

                $url_venta = 'https://www.mercadolibre.com.ar/ventas/'.$pedido['id'].'/detalle';

                marca('pedido_'.$pedido['id'], '', 'imagen', ($temp_estado == 'ok' ? 'disabled="disabled"' : ''));
                entrada('hidden', 'user_id_'.$pedido['id'], '', $pedido['buyer']['id']);
                entrada('hidden', 'fecha'.$pedido['id'], '', $pedido['date_created']);
                celda('imagen', $i18n[$temp_estado], 'imagen', $temp_estado, '', 'title="'.($temp_estado == 'ok' ?  $i18n[320] : $i18n[321]).'"');
                celda('fechayhora', $fecha, '15');
                celda('url', $pedido['id'], '15', false, $url_venta);
                celda('texto', $cliente, '20', false, ($idcliente ? 'clientes.php?a=ver&id='.$idcliente : 'https://www.mercadolibre.com.ar/perfil/'.$cliente));
                celda('texto', $numero_venta, '20', false, ($idventa ? 'ventas.php?a=ver&id='.$idventa : ''));
                celda('moneda', $pedido['payments'][0]['transaction_amount'], '20', false, false, false, false, $ventas[$pedido['id']]['simbolo']);

            }
            linea_fin();

        }

        if (!$tienda['ML_access_token']) {
            texto('italica', '', $i18n[267], 'auto', false, 'info', false);

        } else if (!$pedidos['paging']['total']) {
            texto('italica', '', $i18n[325], 'auto', false, 'info', false);

        } else {
            texto('italica', '', $i18n[322], 'auto', false, 'info', false);
        }

    if ($pedidos['paging']['total'])
        botones(array(array('valor' => $i18n_funciones['agregar'])));
    }
    contenido_fin();

}
ventana_fin();
