<?php

switch ($boton) {
    case $i18n[1]: //Aceptar
    	$datos = recibir_matriz(array(
        array('texto', 'titulo'),
        array('texto', 'tags'),
        array('largo', 'texto'),
        array('texto', 'url_amigable'),
        array('texto', 'tipo'),
        array('texto', 'curso'),
        array('texto', 'publico'),
        array('texto', 'orden'),
        array('texto', 'schema')
    ));
        consulta_sql("UPDATE faqs SET
					titulo = '".$datos['titulo']."',
					tags = '".$datos['tags']."',
					texto = '".filtro_link($datos['texto'])."',
					url_amigable = '".$datos['url_amigable']."',
					tipo = '".$datos['tipo']."',
					curso = '".$datos['curso']."',
					publico = '".$datos['publico']."',
					orden = '".$datos['orden']."',
					`schema` = '".strip_tags($datos['schema'])."'
				WHERE idfaq = '".$id."'", 'saasargentina');
        ir_atras();
        break;

    case $i18n[3]: //Cancelar
        ir_atras();
        break;

    default:
    	$datos = array_sql(consulta_sql("SELECT * FROM faqs WHERE idfaq = '".$id."'", 'saasargentina'));
    	break;
}

$tipos = [
	['id' => 'faq', 'valor' => $i18n[249]],
	['id' => 'consejos', 'valor' => $i18n[250]],
	['id' => 'lecciones', 'valor' => $i18n[251]],
	['id' => 'flotantes', 'valor' => $i18n[252]]
];

$cursos = [
	['id' => '', 'valor' => ''],
	['id' => 'Los primeros pasos', 'valor' => $i18n[253]],
	['id' => 'Funcionalidades básicas', 'valor' => $i18n[254]]
];

$publicos = [
	['id' => 'web', 'valor' => $i18n[255]],
	['id' => 'sistema', 'valor' => $i18n[256]],
	['id' => 'privado', 'valor' => $i18n[257]]
];

ventana_inicio($i18n[147]);
{
	contenido_inicio();
	{
		entrada('texto', 'titulo', $i18n[142], $datos['titulo'], '50', '200');
		entrada('texto', 'tags', $i18n[143], $datos['tags'], '50', '100');
		entrada('texto', 'url_amigable', $i18n[247], $datos['url_amigable'], '50', '100');
		selector_array('tipo', $i18n[244], $datos['tipo'], '50', $tipos);
		selector_array('curso', $i18n[245], $datos['curso'], '50', $cursos);
		selector_array('publico', $i18n[258], $datos['publico'], '50', $publicos);
		entrada('numeros', 'orden', $i18n[248], $datos['orden'], '10');
		area('texto', $i18n[144], $datos['texto']);
		area('schema', $i18n[256], $datos['schema']);
	}
	contenido_fin();
	botones(array(array('valor' => $i18n[1]), array('valor' => $i18n[3])));
}
ventana_fin();

?>
