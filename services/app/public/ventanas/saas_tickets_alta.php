<?php

switch ($boton) {
    case $i18n_funciones[27]:
        $datos = recibir_matriz(array(
        array('largo', 'texto'),
        array('texto', 'titulo'),
        array('entero', 'estado'),
        array('entero', 'idusuario'),
        array('texto', 'mensaje'),
        array('texto', 'destacado')
    ));
        $idusuario_final = recibir_variable('entero', 'idusuario_final', false);

        $datos_empresa = array_sql(consulta_sql("SELECT nombre, mail, idempresa FROM empresas WHERE idempresa =
            (SELECT idempresa FROM usuarios WHERE idusuario = '" . $idusuario_final . "' LIMIT 1)", 'saasargentina'));
        $datos_usuario = array_sql(consulta_sql("SELECT nombrereal, telefonos, mail,
            (SELECT telefonos FROM configuraciones LIMIT 1) AS telefono_empresa
            FROM usuarios WHERE idusuario = '" . $idusuario_final . "' LIMIT 1", $datos_empresa['idempresa']));

        consulta_sql(
            "INSERT INTO saas_tickets SET
                idempresa = '" . $datos_empresa['idempresa'] . "',
                idusuario = '" . $idusuario_final . "',
                idusuario_saas = '" . $datos['idusuario'] . "',
                estado = '" . $datos['estado'] . "',
                titulo = '" . $datos['titulo'] . "',
                empresa_nombre = '" . escape_sql($datos_empresa['nombre']) . "',
                empresa_telefonos = '" . escape_sql($datos_usuario['telefono_empresa']) . "',
                empresa_mail = '" . escape_sql($datos_empresa['mail']) . "',
                usuario_nombre = '" . escape_sql($datos_usuario['nombrereal']) . "',
                usuario_telefonos = '" . escape_sql($datos_usuario['telefonos']) . "',
                usuario_mail = '" . escape_sql($datos_usuario['mail']) . "'", 'admin');

        $id = campo_sql(consulta_sql(
            "SELECT idticket
            FROM saas_tickets
            WHERE idusuario = '" . $idusuario_final . "' AND titulo = '" . $datos['titulo'] . "'
            ORDER BY idticket DESC
            LIMIT 1", 'admin'), 0);
        $fecha = date("Y-m-d H:i:s");

        consulta_sql("INSERT INTO saas_textosxtickets (idticket, idusuario, fecha, texto) VALUES ('" . $id . "', '" . $datos['idusuario'] . "', '" . $fecha . "', '" . $datos['texto'] . "')", 'admin');
        consulta_sql("INSERT INTO tickets (idticket, idusuario, estado, titulo) VALUES ('" . $id . "', '" . $idusuario_final . "', '" . $datos['estado'] . "', '" . $datos['titulo'] . "')", $datos_empresa['idempresa']);
        consulta_sql("INSERT INTO textosxtickets (idticket, idusuario, fecha, texto) VALUES ('" . $id . "', '" . $datos['idusuario'] . "', '" . $fecha . "', '" . $datos['texto'] . "')", $datos_empresa['idempresa']);

        if ($datos['mensaje'])
            consulta_sql("INSERT INTO mensajes SET
                idusuario = '$idusuario_final',
                tipo = 'Consulta',
                texto = '".$datos['mensaje']."',
                idremitente = '$id',
                destacado = '".$datos['destacado']."',
                fecha = NOW()",
                $datos_empresa['idempresa']);

        // Aviso por mail si no es el mismo usuario que está creando el ticket
        if ($datos['idusuario'] != $_SESSION['usuario_idusuario']) {
            switch ($datos['idusuario']) {
                case 3: $mail_soporte = MAIL_DESARROLLO; break;
                case 196: $mail_soporte = MAIL_ADMIN; break;
                default: $mail_soporte = MAIL_INFO; break;
            }
            $mail = 'Consulta de ayuda asignada por soporte interno:<br>
                N° empresa: '.$datos_empresa['idempresa'].'<br>
                Empresa: '.$datos_empresa['nombre'].')<br>
                N° usuario: '.$idusuario_final.'<br>
                Usuario: '.$datos_usuario['nombrereal'].'<br>
                Fecha: '.$fecha.'<br>
                Asunto: '.$datos['titulo'].'<br>
                Texto:<br>
                '.$datos['texto'].'<br>
                <br>
                <a href="'.URL_SAAS.'/saas.php?a=modticket&id='.$id.'">Ver consulta</a><br>';
            email_queue(MAIL_SERVIDOR, $mail_soporte, 'Consulta de ayuda Nº '.$id.' asignada', $mail);
        }

        ir_ahora('saas.php?a=verticket&id='.$id);
        break;

    case $i18n_funciones[23]: // Cancelar
        ir_atras();
        break;

    default:
        $datos = array('idusuario' => $_SESSION['usuario_idusuario']);
        $idusuario_final = recibir_variable('entero', 'idusuario', true);
        $datos['mensaje'] = $i18n[25];
        break;
}

if (!$idusuario_final)
    ir_atras($i18n[105]);

ventana_inicio($i18n[101] . $idusuario_final);
{
    contenido_inicio();
    {
        entrada('hidden', 'idusuario_final', false, $idusuario_final);
        $temp_estado = array(
            array('id' => 'abierto', 'valor' => $i18n['abierto']),
            array('id' => 'cerrado', 'valor' => $i18n['cerrado']),
            array('id' => 'pausado', 'valor' => $i18n['pausado']),
            array('id' => 'pendiente', 'valor' => $i18n['pendiente']),
            array('id' => 'desarrollo', 'valor' => $i18n['desarrollo']),
            // array('id' => 'entendido', 'valor' => $i18n['entendido']),
            );
        entrada('texto', 'titulo', $i18n[10], '', '50', '200');
        selector_array('estado', $i18n[13], $ticket['estado'], '25', $temp_estado);
        selector('idusuario', $i18n[98], $datos['idusuario'], '25', 'usuarios', 'nombre', false, true, true);

    }
    contenido_fin();

    contenido_inicio($i18n[215]);
    {
        $resultado_sql = consulta_sql("SELECT * FROM plantillas");
        if (contar_sql($resultado_sql)) {
            echo "<script>var plantillas = [];";
            $array_selector_opciones = array(
                array('id' => '', 'valor' => 'Seleccione una plantilla para el texto de la respuesta'),
            );
            while ($plantilla = array_sql($resultado_sql)) {
                $array_selector_opciones[] = array('id' => $plantilla['idplantilla'], 'valor' => $plantilla['asunto']);
                echo "plantillas[" . $plantilla['idplantilla'] . "] = '" . str_replace(array("\r", "\n", "'"), "", $plantilla['texto']) . "';";
            }
            echo "</script>";
            selector_array('idplantilla', 'Plantillas', false, '50', $array_selector_opciones);
            entrada('texto', 'mensaje', $i18n[55], $datos['mensaje'], '40', '250');
            marcas($i18n[214], '10', array(array('nombre' => 'destacado', 'titulo' => '', 'valor' => $datos['destacado'])));
        }
        area('texto', '['.mostrar_fecha('fechayhora', date("Y-m-d H:i")).']&nbsp;&nbsp;'.$_SESSION['usuario_nombre'].':', $datos['texto']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n_funciones[27]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    function validacion_saas_tickets_alta(boton)
    {
        if (boton == '<?php echo $i18n_funciones[27]; ?>' && $("#marco_saas_tickets_alta input[name='titulo']").val() == '') {
            <?php script_validacion_flotante("alerta", $i18n[103], "input[name='titulo']"); ?>

            return false;
        } else if (boton == '<?php echo $i18n_funciones[27]; ?>' && $("#marco_saas_tickets_alta textarea[name='texto']").val() == '') {
            <?php script_validacion_flotante("alerta", $i18n[104], "textarea[name='texto']"); ?>

            return false;
        } else

            return true;
    };

$("select[name=idplantilla]").change(function () {
    var idplantilla = $("select[name=idplantilla] option:selected").val();
    console.log("idplantilla:"+idplantilla);
    console.log("texto:"+plantillas[idplantilla]);
    if (idplantilla)
        $("textarea[name='texto']").setCode(plantillas[idplantilla]);
    else
        $("textarea[name='texto']").setCode("");
});
</script>
