<?php
require __DIR__.'/../../../acc/acc.php';
require '../librerias/funciones_modelo.php';
require '../librerias/funciones_exportar.php';
recuperar_sesion();
$bd_link = conectar_db();
sesion();

$id = recibir_variable('texto', 'id', true);
$a = 'prn';
$cliente = array_sql(consulta_sql("SELECT clientes.nombre, cuit, dni, contacto, domicilio, telefonos, clientes.idlocalidad,
    categorias_localidades.idprovincia, categorias_localidades.nombre AS localidad, categorias_localidades.codigopostal,
    tablas_provincias.nombre AS provincia
    FROM clientes LEFT JOIN categorias_localidades ON clientes.idlocalidad=categorias_localidades.idlocalidad
    LEFT JOIN tablas_provincias ON categorias_localidades.idprovincia=tablas_provincias.idprovincia
    WHERE idcliente='".$id."' LIMIT 1")
);

exportar('<html>
<head>
    <title>Impresión del rótulo para el cliente '.$cliente['nombre'].'</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
</head>

<body onload="window.print();">
<div style="text-align: center;">
<p><font size="+4">
<b><u>DESTINATARIO</u></b><br>
'.$cliente['nombre'].'<br>
'.$cliente['contacto'].'<br>
');

if ($cliente['cuit'])
    exportar('CUIT: ' . convertir_numero($cliente['cuit'], 'cuit-dni').'<br>');
elseif ($cliente['dni'])
    exportar('DNI: ' . $cliente['dni'].'<br>');

exportar($cliente['domicilio'].'<br>
');

if ($cliente['idlocalidad'])
    exportar($cliente['localidad'].'<br>
');
if ($cliente['idprovincia'])
    exportar($cliente['provincia'].'<br>
');
if ($cliente['codigopostal'])
    exportar($cliente['codigopostal'].'<br>
');
if ($cliente['telefonos'])
    exportar($cliente['telefonos'].'<br>
');
exportar('
<hr>
</font><font size="+3">
<b><u>REMITENTE</u></b><br>
'.$_SESSION['usuario_nombrereal'].'<br>
'.$_SESSION['empresa_nombre'].'<br>
');
if ($_SESSION['configuracion_cuit'])
    exportar('CUIT: ' . convertir_numero($_SESSION['configuracion_cuit'], 'cuit-dni').'<br>
');
exportar($_SESSION['configuracion_domicilio'].'<br>
');
if ($_SESSION['configuracion_idlocalidad']){
    exportar($_SESSION['configuracion_localidad'].'<br>');
    $codigopostal = campo_sql(consulta_sql("SELECT codigopostal FROM categorias_localidades WHERE idlocalidad = '".$_SESSION['configuracion_idlocalidad']."'"));
    exportar($codigopostal.'<br>');
}
if ($_SESSION['configuracion_provincia'] && $_SESSION['configuracion_provincia'] != 'Sin especificar')
    exportar($_SESSION['configuracion_provincia'].'<br>
');
if ($_SESSION['configuracion_telefonos'])
    exportar($_SESSION['configuracion_telefonos'].'<br>
');
exportar('
</font></p>
</div>
</body>
</html>');
