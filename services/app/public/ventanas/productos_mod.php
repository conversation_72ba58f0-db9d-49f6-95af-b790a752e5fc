<?php
if ($boton) {
    $datos = recibir_matriz(array(
        array('entero', 'estado'),
        array('entero', 'estadoventa'),
        array('entero', 'estadocompra'),
        array('entero', 'estadocombo'),
        array('texto', 'combo'),
        array('entero', 'idrubro'),
        array('texto', 'sku'),
        array('texto', 'codigo'),
        array('texto', 'codigoproveedor'),
        array('texto', 'nombre'),
        array('cantidad', 'controlarstock'),
        array('cantidad', 'stocknegativo'),
        array('entero', 'idunidad'),
        array('entero', 'idiva'),
        array('entero', 'idproveedor'),
        array('moneda', 'prod_costo'),
        array('texto', 'mostrartienda'),
        array('texto', 'trazabilidad'),
        array('texto', 'tributo_5329'),
        array('largo', 'observacion'),
        array('largo', 'obstienda'),
        array('largo', 'obsinterna'),
        array('texto', 'ML_item_id'),
        array('texto', 'ML_item_id2'),
        array('entero', 'idcombo'),
        array('entero', 'idlista'),
        array('entero', 'iddeposito'),
        array('entero', 'idmoneda')
    ));
    $datos_listas = recibir_sucursales('listas');
    $datos_depositos = recibir_sucursales('depositos');
} else {
    $sucursales = armar_sqls_sucursales();
    $datos = array_sql(consulta_sql(
        "SELECT productos.idproducto, productos.estado, productos.estadoventa, productos.estadocompra, productos.estadocombo,
            productos.idrubro, productos.sku, productos.codigo, productos.codigoproveedor, productos.nombre, productos.url_amigable,
            productos.idunidad, productos.idiva, productos.idproveedor, productos.costo, productos.controlarstock, productos.trazabilidad, productos.tributo_5329,
            productos.combo, productos.stocknegativo, productos.mostrartienda, productos.observacion, productos.obstienda,
            productos.obsinterna, productos.ML_item_id, productos.ML_item_id2, productos.idmoneda,
            ".$sucursales['precios']."
            ".$sucursales['stock']."
            categorias_rubros.nombre AS rubro,
            tablas_unidades.nombre AS unidad,
            proveedores.nombre AS proveedor,
            tablas_ivas.nombre AS iva,
            monedas.simbolo
        FROM productos
            LEFT JOIN categorias_rubros ON productos.idrubro = categorias_rubros.idrubro
            LEFT JOIN tablas_unidades ON productos.idunidad = tablas_unidades.idunidad
            LEFT JOIN proveedores ON productos.idproveedor = proveedores.idproveedor
            LEFT JOIN tablas_ivas ON productos.idiva = tablas_ivas.idiva
            LEFT JOIN monedas ON productos.idmoneda = monedas.idmoneda
            ".$sucursales['joinlistas']."
            ".$sucursales['joinstock']."
        WHERE productos.idproducto = '$id'
        LIMIT 1"));
    if ($datos['combo']) {
        $idlista = campo_sql(consulta_sql("SELECT idlista FROM listas WHERE idmoneda = ".$datos['idmoneda']." ORDER BY idlista LIMIT 1"));
        $combo = consulta_sql("SELECT productosxcombos.*,
            ".$sucursales['precios']."
            ".$sucursales['stock']."
            productos.idproducto, productos.estado, productos.estadoventa, productos.estadocompra, productos.estadocombo,
            productos.idrubro, productos.codigo, productos.codigoproveedor, productos.nombre, productos.url_amigable,
            productos.idunidad, productos.idiva, productos.idproveedor, productos.costo, productos.controlarstock, productos.trazabilidad, productos.tributo_5329,
            productos.combo, productos.stocknegativo, productos.mostrartienda, productos.observacion, productos.obstienda,
            productos.obsinterna, productos.ML_item_id, productos.ML_item_id2, productos.idmoneda,
            tablas_ivas.nombre AS iva
        FROM productosxcombos
            LEFT JOIN productos ON productosxcombos.idproducto = productos.idproducto
            LEFT JOIN tablas_ivas ON productos.idiva = tablas_ivas.idiva
            ".$sucursales['joinlistas']."
            ".$sucursales['joinstock']."
        WHERE idcombo = '".$id."'
        ");

        $i = 0;
        $combocosto = $comboprecio = $combopreciofinal = 0;

        while ($datos_combo = array_sql($combo)) {
            $idcombo = $datos_combo['idcombo'];
            $combocosto += $datos_combo['cantidad'] * cotizacion($datos['idmoneda'], $datos_combo['idmoneda'], $datos_combo['costo']);
            $comboprecio += $datos_combo['cantidad'] * $datos_combo['precio_'.$idlista]; // cambio x idlista porque sino no coinciden con los ids de la tabla listas, es más, se usa $comboprecio y $combopreciofinal solamente una vez, la primera traida x PHP, luego lo recalcula js
            $combopreciofinal += $datos_combo['cantidad'] * $datos_combo['preciofinal_'.$idlista];
            $productosxcombo[$i] = $datos_combo;
            $i++;
        }
    }

    if ($datos['estadocombo']) {
        $cantidad_estadocombo = campo_sql(consulta_sql("SELECT COUNT(idproductoxcombo) FROM productosxcombos WHERE idproducto = '".$id."' "));
    }
}
$tienda = array_sql(consulta_sql("SELECT tienda_estado, ML_estado, MP_estado, ML_access_token, ML_user_id FROM tienda LIMIT 1"));
$cotizaciones = obtener_cotizaciones();

// Predeterminados para mostrar
if (!$datos['combo']) {
    $idlista = campo_sql(consulta_sql("SELECT idlista FROM listas ORDER BY idlista LIMIT 1"));
}
$iddeposito = campo_sql(consulta_sql("SELECT iddeposito FROM depositos ORDER BY iddeposito LIMIT 1"));

if ($boton == $i18n[12] && $datos['nombre'] == '') {
    mensajes_alta($i18n[14]);
    $boton = '';

} elseif ($boton == $i18n[12] && ($datos['codigo'] || $datos['ML_item_id'] || $datos['ML_item_id2'])) {

    $resultado_sql = consulta_sql(
    $sql =
        "SELECT idproducto, nombre, codigo, ML_item_id, ML_item_id2
        FROM productos
        WHERE idproducto != '$id'
            AND ((codigo != '' AND codigo = '".$datos['codigo']."')".
                ($datos['ML_item_id'] ? " OR ML_item_id = '".$datos['ML_item_id']."' OR ML_item_id2 = '".$datos['ML_item_id']."'" : '').
                ($datos['ML_item_id2'] ? " OR ML_item_id = '".$datos['ML_item_id2']."' OR ML_item_id2 = '".$datos['ML_item_id2']."'" : '').
        ") LIMIT 1");

    if (contar_sql($resultado_sql)) {
        $producto = array_sql($resultado_sql);

        if ($datos['codigo'] == $producto['codigo']) {
            mensajes_alta($i18n[71].'<a href="productos.php?a=ver&id='.$producto['idproducto'].'" target="_blank">'.$producto['nombre'].'</a>');
            $boton = '';
        }
        if ($datos['ML_item_id'] && ($datos['ML_item_id'] == $producto['ML_item_id'] || $datos['ML_item_id'] == $producto['ML_item_id2'])) {
            mensajes_alta($i18n[113].'<a href="productos.php?a=ver&id='.$producto['idproducto'].'" target="_blank">'.$producto['nombre'].'</a>');
            $boton = '';
        }
        if ($datos['ML_item_id2'] && ($datos['ML_item_id2'] == $producto['ML_item_id'] || $datos['ML_item_id2'] == $producto['ML_item_id2'])) {
            mensajes_alta($i18n[114].'<a href="productos.php?a=ver&id='.$producto['idproducto'].'" target="_blank">'.$producto['nombre'].'</a>');
            $boton = '';
        }
    }
}
if (!$boton) $sucursales = armar_sqls_sucursales();

switch ($boton) {
    case $i18n[12]: // Aceptar
        //cheque de no poder deshablitar cuando pertenece a un combo
        if(!$datos['estado']){
            $resultado_sql = consulta_sql("SELECT * FROM productosxcombos WHERE idproducto = '$id' LIMIT 1");
            if (contar_sql($resultado_sql))
                ir_atras($i18n[170]);
        }
        if ($datos['combo']
            && !contar_sql(consulta_sql("SELECT idproductoxcombo FROM productosxcombos WHERE idcombo = '$id' LIMIT 1"))) {
            mensajes_alta($i18n[186]);
        }

        if (!$datos['codigo']) {
            ir_atras($i18n[247]);
        }

        if (!$datos['estadocombo'] && contar_sql(consulta_sql("SELECT idproductoxcombo FROM productosxcombos WHERE idproducto = '".$id."' LIMIT 1"))) {
            ir_atras($i18n[254].$i18n[251].'<a href="'.URL_INFORMES.'/informes/mod/37?id=37&a=mod&compuesto=integra_compuesto&mostrar_productos_relacionados=1&mostrar_precio=1&idproducto='.$id.'" target="_blank">'.$i18n[252].'</a>');
        }

        consulta_sql("UPDATE productos SET
                estado = '".$datos['estado']."',
                estadoventa = '" . $datos['estadoventa'] . "',
                estadocompra = '" . $datos['estadocompra'] . "',
                estadocombo = '" . $datos['estadocombo'] . "',
                combo = '" . $datos['combo'] . "',
                idrubro = '".$datos['idrubro']."',
                idmoneda = '" . $datos['idmoneda'] . "',
                codigo = '".validar_codigo_producto($datos['codigo'], $id)."',
                sku = '".$datos['sku']."',
                codigoproveedor = '".$datos['codigoproveedor']."',
                nombre = '".$datos['nombre']."',
                url_amigable = '".url_amigable($datos['nombre'])."',
                controlarstock = '".$datos['controlarstock']."',
                idunidad = '".$datos['idunidad']."',
                idiva = '".$datos['idiva']."',
                idproveedor = '".$datos['idproveedor']."',
                costo = '".$datos['prod_costo']."',
                stocknegativo = '".$datos['stocknegativo']."',
                trazabilidad   = '".$datos['trazabilidad']."',
                tributo_5329   = '".$datos['tributo_5329']."',
                observacion = '".escape_sql($datos['observacion'])."',
                obsinterna = '".escape_sql($datos['obsinterna'])."'"
                .($_SESSION['modulo_tienda'] && $tienda['tienda_estado'] || $tienda['MP_estado']
                    ? ", mostrartienda = '".$datos['mostrartienda']."', obstienda = '".$datos['obstienda']."'"
                    : "")
                .($_SESSION['modulo_ML'] && $tienda['ML_estado'] || $tienda['MP_estado']
                    ? ", ML_item_id = '".$datos['ML_item_id']."', ML_item_id2 = '".$datos['ML_item_id2']."'"
                    : "")."
            WHERE idproducto = '".$id."'
            LIMIT 1");
        $afectado_sql = afectado_sql();

        $idlista_temp = 1;
        $alerta_costo = false;
        foreach ($datos_listas as $key => $value) {
            $idlista_temp = substr($key, strrpos($key, '_') + 1);
            $idmoneda_destino = obtener_moneda_desde_idlista($idlista_temp, 'idmoneda');

            switch ($key) {
                case strpos($key, 'idlista_') !== false:
                    $sql = "INSERT INTO precios SET
                    idproducto = '".$id."',
                    idlista = '".$value."', ";
                    break;
                case strpos($key, 'utilidad_') !== false:
                    $sql .= "utilidad = '".$value."', ";
                    $sql_duplicate = "utilidad = '".$value."', ";
                    break;
                case strpos($key, 'prod_precio_') !== false:
                    if ($value > 0 && $datos['prod_costo'] > cotizacion($datos['idmoneda'], $idmoneda_destino, $value) && !$alerta_costo) {
                        mensajes_alta($i18n[253].'<a href=productos.php?a=ver&id='.$id.'>'.$datos['nombre'].'</a>', 'Notificacion', 1);
                        $alerta_costo = true;
                    }
                    if ($_SESSION['configuracion_discrimina']) {
                        $sql .= "precio = '".$value."', ";
                        $sql_duplicate .= "precio = '".$value."', ";
                    } else {
                        $sql .= "precio = '".$value."'";
                        $sql_duplicate .= "precio = '".$value."'";
                        $sql .= " ON DUPLICATE KEY UPDATE ".$sql_duplicate;
                        consulta_sql($sql);
                        if(!$afectado_sql && afectado_sql())
                            $afectado_sql = afectado_sql();
                    }
                    break;
                case strpos($key, 'prod_preciofinal_') !== false:
                    if ($value > 0 && $datos['prod_costo'] > cotizacion($datos['idmoneda'], $idmoneda_destino, $value) && !$alerta_costo) {
                        mensajes_alta($i18n[253].$datos['nombre'], 'Notificacion', 1);
                        $alerta_costo = true;
                    }
                    $sql .= "preciofinal = '".$value."'";
                    $sql_duplicate .= "preciofinal = '".$value."'";
                    $sql .= " ON DUPLICATE KEY UPDATE ".$sql_duplicate;
                    consulta_sql($sql);
                    if(!$afectado_sql && afectado_sql())
                        $afectado_sql = afectado_sql();
                    break;
                default:
                    break;
            }
        }

        foreach ($datos_depositos as $key => $value) {
            switch ($key) {
                case strpos($key, 'iddeposito_') !== false:
                    $sql = "INSERT INTO stock SET
                    idproducto = '".$id."',
                    iddeposito = '".$value."', ";
                    break;
                case strpos($key, 'stockactual_') !== false:
                    $sql .= "stockactual = '".$value."', ";
                    $sql_duplicate = "stockactual = '".$value."', ";
                    break;
                case strpos($key, 'stockideal_') !== false:
                    $sql .= "stockideal = '".$value."', ";
                    $sql_duplicate .= "stockideal = '".$value."', ";
                    break;
                case strpos($key, 'stockminimo_') !== false:
                    $sql .= "stockminimo = '".$value."'";
                    $sql_duplicate .= "stockminimo = '".$value."'";
                    $sql .= " ON DUPLICATE KEY UPDATE ".$sql_duplicate;
                    consulta_sql($sql);
                    if(!$afectado_sql && afectado_sql())
                        $afectado_sql = afectado_sql();

                    break;
                default:
                    break;
            }
        }

        //si era combo
        if (!$datos['combo'] && contar_sql(consulta_sql("SELECT idproductoxcombo FROM productosxcombos WHERE idcombo = '".$id."'"))) {
            consulta_sql("DELETE FROM productosxcombos WHERE idcombo = '$id'");
        }

        if ($afectado_sql) {
            $motivo = 'mod';
            $idcombo = '0';
            consulta_sql("UPDATE productos SET updated_at = NOW() WHERE idproducto = '$id'");
            log_productos($id, $motivo, $idcombo);

            //actualizo el fullsearch_update
            fullsearch_update('productos', $id);
        }

        extras_mod();
        ir_ahora('productos.php?a=ver&id='.$id);
        break;

    case $i18n[13]: // Cancelar
        ir_atras();
        break;
}

mensajes_efimeros();

ventana_inicio($i18n[15].$datos['nombre'], '100', array(
    array('url' => 'productos.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[82]),
    array('url' => 'productos.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[64], 'permiso' => 'productos_baja', 'opciones' => 'onclick="return confirma('."'$i18n[69]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[73])));
{
    // Datos básicos
    contenido_inicio($i18n[18], '66');
    {
        entrada('texto', 'nombre', $i18n[3], $datos['nombre'], '50', '200');
        entrada('texto', 'codigo', $i18n[4], $datos['codigo'], '25', '60');
        entrada('texto', 'codigoproveedor', $i18n[16], $datos['codigoproveedor'], '25', '60');
        selector('idunidad', $i18n[5], $datos['idunidad'], '33', 'tablas_unidades');
        selector_familiar('idrubro', $i18n[8], $datos['idrubro'], '33', 'categorias_rubros', true, true, true);
        selector('idproveedor', $i18n[17], $datos['idproveedor'], '34', 'proveedores', 'nombre', false, true, true);
    }
    contenido_fin();

    // Datos de comportamiento
    contenido_inicio($i18n[20], '33');
    {
        marcas('', '100', array(
            array('nombre' => 'estado', 'titulo' => $i18n[26], 'valor' => $datos['estado'], 'opciones' => 'id="estado"'),
            array('nombre' => 'estadoventa', 'titulo' => $i18n[168], 'valor' => $datos['estadoventa'], 'opciones' => 'id="estadoventa"'),
            array('nombre' => 'estadocompra', 'titulo' => $i18n[145], 'valor' => $datos['estadocompra'], 'opciones' => 'id="estadocompra"'),
        ));
        marcas('', '100', array(
            array('nombre' => 'estadocombo', 'titulo' => $i18n[146], 'valor' => $datos['estadocombo'], 'opciones' => 'id="estadocombo"'),
            array('nombre' => 'combo', 'titulo' => $i18n[147], 'valor' => $datos['combo'], 'opciones' => 'id="combo"'),
            array('nombre' => 'controlarstock', 'titulo' => $i18n[28], 'valor' => $datos['controlarstock'], 'opciones' => 'id="controlarstock"'),
        ));

        marcas('', 'auto', array(array('nombre' => 'stocknegativo', 'titulo' => ($datos['combo'] ? $i18n[184] : $i18n[29]), 'valor' => $datos['stocknegativo'], 'opciones' => 'id="stocknegativo"')));

        if ($_SESSION['modulo_trazabilidad'])
            marcas('', 'auto', array(array('nombre' => 'trazabilidad', 'titulo' => $i18n[45], 'valor' => $datos['trazabilidad'])));
        if ($_SESSION['modulo_tienda'] && $tienda['tienda_estado'])
            marcas('', 'auto', array(array('nombre' => 'mostrartienda', 'titulo' => $i18n[87], 'valor' => $datos['mostrartienda'], 'ayuda_puntual' => $i18n[93])));
        if ($_SESSION['configuracion_emitir_impuestos'] && $_SESSION['configuracion_tributo_5329'])
            marcas('', 'auto', array(array('nombre' => 'tributo_5329', 'titulo' => $i18n[261], 'valor' => $datos['tributo_5329'], 'ayuda_puntual' => $i18n[262])));
    }
    contenido_fin();

    // Datos de costos y stock
    contenido_inicio($i18n[191], '100', false, false, false, false, 'mini-icono-listas-de-precios');
    {
        if ($_SESSION['modulo_multimoneda']) {
            selector_array('idmoneda', $i18n[265], $datos['idmoneda'], '15', $cotizaciones, false, 'id="idmoneda_costo"');
        } else {
            texto('titulo', $i18n[264], $i18n[266], '30');
            entrada('hidden', 'idmoneda', '', 1);
        }
        entrada('moneda', 'prod_costo', $i18n[6], $datos['costo'], '15', '10');
        if ($_SESSION['configuracion_discrimina'])
            selector('idiva', $i18n[10].' (%)', $datos['idiva'], '15', 'tablas_ivas', false, false, false);
    }
    contenido_fin();

    puntero_sql($sucursales['listas']);
    while ($temp_array = array_sql($sucursales['listas'])) {
        $solapas_listas[] = array('idsolapa' => $temp_array['idlista'], 'tipo' => 'idlista', 'nombre' => $temp_array['nombre']);
    }
    solapas_multiples_inicio($solapas_listas);
    {
        entrada('hidden', 'idlista', '', $idlista);
        puntero_sql($sucursales['listas']);
        while ($temp_array = array_sql($sucursales['listas'])) {
            solapa_inicio($temp_array['nombre']);
            {
                contenido_inicio('', '100', false, false, false, 'id="idlista" style="display: '.($temp_array[0] == 1 ? 'block':'none').'";');
                {
                    if ($_SESSION['modulo_multimoneda']) {
                        texto('titulo', $i18n[267], $temp_array['moneda'].' ('.$temp_array['simbolo'].')');
                        salto_linea();
                    }
                    entrada('hidden', 'idmoneda_precio_'.$temp_array['idlista'], '', $temp_array['idmoneda']);
                    entrada('hidden', 'idlista_'.$temp_array['idlista'], '', $temp_array['idlista']);
                    entrada('porcentaje', 'utilidad_'.$temp_array['idlista'], $i18n[25].' (%)', $datos['utilidad_'.$temp_array['idlista']], '15', '6', false, 'data="utilidad"');
                    entrada('moneda', 'prod_precio_'.$temp_array['idlista'], $i18n[7].' ('.$temp_array['simbolo'].')', $datos['precio_'.$temp_array['idlista']], '15', '10',  false, 'data="prod_precio"');
                    if ($_SESSION['configuracion_discrimina'])
                        entrada('moneda', 'prod_preciofinal_'.$temp_array['idlista'], $i18n[81].' ('.$temp_array['simbolo'].')', $datos['preciofinal_'.$temp_array['idlista']], '15', '10', false, 'data="prod_preciofinal"');
                }
                contenido_fin();
            }
            solapa_fin();
        }
    }
    solapas_fin();
    salto_linea();

    contenido_inicio($i18n[192], '100', false, false, false, false, 'mini-icono-stock-depositos');
    {

    }
    contenido_fin();

    //Depósitos
    puntero_sql($sucursales['depositos']);
    while ($temp_array = array_sql($sucursales['depositos'])) {
        $solapas_depositos[] = array('idsolapa' => $temp_array['iddeposito'], 'tipo' => 'iddeposito', 'nombre' => $temp_array['nombre']);
    }
    solapas_multiples_inicio($solapas_depositos);
    {
        entrada('hidden', 'iddeposito', '', $iddeposito);
        puntero_sql($sucursales['depositos']);
        while ($temp_array = array_sql($sucursales['depositos'])) {
            solapa_inicio($temp_array['nombre']);
            {
                contenido_inicio('', '100', false, false, false, 'id="iddeposito" style="display: '.($temp_array[0] == 1 ? 'block':'none').'";');
                {
                    entrada('hidden', 'iddeposito_'.$temp_array['iddeposito'], '', $temp_array['iddeposito']);
                    entrada('cantidadnegativa', 'stockactual_'.$temp_array['iddeposito'], $i18n[21], $datos['stockactual_'.$temp_array['iddeposito']], '15', '13');
                    entrada('cantidadnegativa', 'stockideal_'.$temp_array['iddeposito'], $i18n[22], $datos['stockideal_'.$temp_array['iddeposito']], '15', '13');
                    entrada('cantidadnegativa', 'stockminimo_'.$temp_array['iddeposito'], $i18n[23], $datos['stockminimo_'.$temp_array['iddeposito']], '15', '13');
                    if ($datos['combo']){
                        $datos['stockdinamico_'.$temp_array['iddeposito']] = stockdinamico($id, $temp_array['iddeposito']);
                        if($datos['stockdinamico_'.$temp_array['iddeposito']] || $datos['stockdinamico_'.$temp_array['iddeposito']] === 0)
                            texto('cantidad', $i18n[173], $datos['stockdinamico_'.$temp_array['iddeposito']], '20', false, false, false, 'id="solapa_stockdinamico_'.$temp_array['iddeposito'].'"');
                        else
                            texto('texto', $i18n[173], $i18n[183], '20');
                    }
                }
                contenido_fin();
            }
            solapa_fin();
        }
    }
    solapas_fin();

    // Combos
    // Seleccionador de productos
    contenido_inicio($i18n[148], '100', false, false, false, ($_SESSION['mobile'] ? 'style="overflow-x: scroll;"' : '') . 'id="ventana_combo"', 'mini-icono-composicion-de-productos');
    {
        entrada('hidden', 'idcombo', '', $idcombo);
        seleccionador_productos('productos', 'combo', $idlista, $iddeposito);

        //Totales
        linea_inicio('fila', 2);
        {
            linea_inicio('titulo', 2); {
                celda('texto', $i18n[171], ($_SESSION['configuracion_discrimina'] ? '55' : '48'));
                celda('cantidad', $datos['stockdinamico_1'] ? $datos['stockdinamico_1'] : '-', '14', false, false, 'id="stockdinamico"');
                celda('moneda', $combocosto, '7', false, false, 'id="combocosto"', false, $datos['simbolo']);

                if ($_SESSION['configuracion_discrimina']) {
                    celda('moneda', $combopreciofinal, '10', false, false, 'id="combopreciofinal"', false, $datos['simbolo']);
                } else {
                    celda('moneda', $comboprecio, '10', false, false, 'id="combopreciofinal"', false, $datos['simbolo']);
                }
                entrada('hidden', 'combocosto_actual', false, $combocosto, false, false, false, 'id="combocosto_actual"');
                entrada('hidden', 'comboprecio_actual', false, $comboprecio, false, false, false, 'id="comboprecio_actual"');
                entrada('hidden', 'combopreciofinal_actual', false, $combopreciofinal, false, false, false, 'id="combopreciofinal_actual"');
            }
            linea_fin(array(array(),array()));
        }
        linea_fin();

        enlaces('', array(
            array('tipo' => 'javascript', 'url' => '', 'valor' => $i18n[167], 'modulo' => 'productos',
                'opciones' => 'onclick="if (confirm(\''.$i18n[268].$i18n[172].'\')) productos_importes(); return false;"'),
        ));
    }
    contenido_fin();

    // Imagenes
    contenido_inicio($i18n[90], '66');
    {
        $resultado_sql = consulta_sql("SELECT iua FROM archivos WHERE tipo='imagen' AND modulo='productos' AND publico='1' AND id='".$id."' LIMIT 6");
        if (contar_sql($resultado_sql)) {
            while ($imagen = array_sql($resultado_sql)) {
                miniatura(URL_S3.$imagen['iua'].'/miniatura');
            }
        } else {
            texto('texto', '', $i18n[91], 'auto', false, false, false, 'id="notieneimagenes"');
        }
        salto_linea();
        enlaces('', array(array('tipo' => 'javascript', 'url' => '#', 'valor' => $i18n[92], 'opciones' => 'id="agregar_imagen"')));
    }
    contenido_fin();

    if ($_SESSION['modulo_ML'] && ($tienda['ML_estado'] || $tienda['MP_estado'])) {
        // Integración con MercadoLibre
        contenido_inicio($i18n[41], '33', false, false, false, false, 'mini-icono-ml');
        {
            entrada('texto', 'sku', $i18n[204], $datos['sku'], 'auto', '255', $i18n[242]);
            entrada('numeros', 'ML_item_id', $i18n[42], ($datos['ML_item_id'] ? $datos['ML_item_id'] : ''), 'auto', '10');
            entrada('numeros', 'ML_item_id2', $i18n[112], ($datos['ML_item_id2'] ? $datos['ML_item_id2'] : ''), 'auto', '10');
            if (!$tienda['ML_access_token'] || !$tienda['ML_user_id']) {
                texto('italica', '', $i18n[117]);
            }
        }
        contenido_fin();
    }

    extras();

    // Observaciones
    contenido_inicio($i18n[86], '100', true, false);
    {
        area('observacion', $i18n[84], $datos['observacion'], 'auto', $i18n[37]);
        if ($_SESSION['modulo_tienda'] && $tienda['tienda_estado'])
            area('obstienda', $i18n[36], $datos['obstienda']);
        area('obsinterna', $i18n[85], $datos['obsinterna']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[12]), array('valor' => $i18n[13])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
    var stocknegativo = "<?php echo $i18n[29]; ?>";
    var stocknegativo_combo = "<?php echo $i18n[184]; ?>";
    var idcombo = '<?php if($datos["combo"]) echo $id; ?>';
    var prod_costo = '<?php echo $datos["costo"]; ?>';
    var cantidad_estadocombo = parseInt('<?php echo $cantidad_estadocombo; ?>');
    var cotizaciones = JSON.parse('<?php echo json_encode($cotizaciones); ?>');

    function validacion_productos_mod(boton)
    {
        if (boton == '<?=$i18n[12]?>' && $("#marco_productos_mod input[name='nombre']").val() == '') {
<?php script_validacion_flotante("alerta", $i18n[14], "input[name='nombre']");?>

            return false;
        } else

            return true;
    };

    $(function() {

        $('#estado, #estadoventa, #estadocompra, #estadocombo, #combo, #controlarstock, #stocknegativo').change(estados_producto);
        estados_producto();

        $("#agregar_imagen").click(function(e) {
            e.preventDefault();
            $("#notieneimagenes").remove();
            $("#marco_archivos_listar img[src='<?=$_SESSION['servidor_url']?>estilos/estilo_<?=$_SESSION['usuario_idestilo']?>/images/alta.png']").parent().trigger("click");
        });

        $("#estadocombo, #combo").click(function() {
            if (cantidad_estadocombo > 0) {
                $("#flotante").append('<div class="alerta"><span class="campo_texto"><?php echo $i18n[259].'</br><a href="'.URL_INFORMES.'/informes/mod/37?id=37&a=mod&compuesto=integra_compuesto&mostrar_productos_relacionados=1&mostrar_precio=1&idproducto='.$id.'" target="_blank">'.$i18n[252].'</a>' ?>.</span></div>');
                $("#flotante").delay(5000).fadeOut(1000);

                return false;
            }
        });

    });
</script>
