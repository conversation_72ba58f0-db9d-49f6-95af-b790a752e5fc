<?php
$idcliente = recibir_variable('entero', 'idcliente', true);
if (!contar_sql(consulta_sql("SELECT estado FROM clientes WHERE idcliente='".$idcliente."' AND estado='1' LIMIT 1"))) {
    mensajes_alta($i18n[69]);
    ir_inicio();
} else {
    consulta_sql("INSERT INTO comunicaciones (idcliente, idusuario, fechainicio) VALUES ('".$idcliente."', '".$_SESSION['usuario_idusuario']."', '".fecha_sql('ahora', 'ahora')."')");
    ir_ahora('comunicaciones.php?a=mod&id='.id_sql());
}
