<?php
//Especifico el nombre del módulo en plural y singular para ser utilizado en varias partes de inclusiones
$modulo = 'ventas';
$modulo_singular = 'venta';

//Ejecuto el archivo que se encarga de revisar que todo esté ok e incluye todas las librerías.
require 'cargadores/iniciar.php';

//Agrego el idcomprapago que puede hacer falta como parche hasta que todo se maneje con id y los pagos con su módulo propio
$idventapago = recibir_variable('entero', 'idventapago', false);
if ($idventapago)
    $id = $idventapago;
else
    $idventapago = $id;
$_SESSION['history_0'] = $modulo.'|'.$a.'|'.$id;

//Ejecuto el switch principal con el comando enviado en $a
switch ($a) {
    default:
    case 'alta':
        if (!$_SESSION['perfil_ventas_alta'])
            ir_atras('No tiene permiso para agregar ventas', $modulo, $a, $id);
        else {
            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = 'ventas_alta';
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            marco_fin();
            html_cuerpo_fin();
            html_fin(['afip']);
            logs();
        }
        break;

    case 'alta_auto':
        if (!$_SESSION['perfil_ventas_alta'])
            ir_atras('No tiene permiso para agregar ventas', $modulo, $a, $id);
        else {
            $ventana = 'ventas_alta_auto';
            include('ventanas/'.$ventana.'.php');
        }
        break;

    case 'ver':
        if (!$id || !contar_sql(consulta_sql("SELECT idventa FROM ventas WHERE idventa = '$id'")))
            ir_inicio($i18n_funciones['url_incorrecto'], $modulo, $a, $id);
        else {
            $venta = array_sql(consulta_sql(
                "SELECT ventas.numero, ventas.muevesaldo,
                    categorias_ventas.nombre, categorias_ventas.letra, categorias_ventas.puntodeventa
                FROM ventas
                    LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
                WHERE idventa = '$id'
                LIMIT 1"));
            $nombre = $venta['nombre'].' '.$venta['letra'].completar_numero($venta['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8);
            $muevesaldo = $venta['muevesaldo'];
            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = 'ventas_ver';
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            marco_fin();
            if ($_SESSION['perfil_ventaspagos_ver'] && $muevesaldo) {
                $ventana = 'ventas_listar_pagos';
                marco_inicio();
                include('ventanas/'.$ventana.'.php');
                marco_fin();
            }
            if ($_SESSION['modulo_archivos'] && $_SESSION['perfil_archivos_ver']) {
                include('sistemas/sistema_'.$_SESSION['sistema_idsistema'].'_idioma_'.$_SESSION['usuario_ididioma'].'/idiomas/idiomas_archivos.php');
                $ventana = 'archivos_listar';
                marco_inicio();
                include('ventanas/'.$ventana.'.php');
                marco_fin();
            }
            html_cuerpo_fin();
            html_fin(['comprobantes']);
        }
        break;

    case 'mod':
        if (!$id || !$venta = array_sql(consulta_sql("SELECT ventas.idusuario, ventas.estado, estadocae, categorias_ventas.tipofacturacion AS tipofacturacion FROM ventas LEFT JOIN categorias_ventas ON ventas.idtipoventa=categorias_ventas.idtipoventa WHERE idventa = '$id' LIMIT 1")))
            ir_atras($i18n_funciones['url_incorrecto'], $modulo, $a, $id);
        elseif ($venta['estado'] == 'abierto' && !$_SESSION['perfil_ventas_alta'])
            ir_atras('No tiene permiso para agregar ventas', $modulo, $a, $id);
        elseif (($venta['estado'] == 'cerrado' || $venta['estado'] == 'anulado') && !$_SESSION['perfil_ventas_mod'])
            ir_atras('No tiene permiso para modificar ventas', $modulo, $a, $id);
        elseif ($venta['estado'] == 'cerrado' && $venta['estadocae'] == 'aprobado' && $venta['tipofacturacion'] == 'electronico')
            ir_atras('No se puede modificar facturas electrónicas autorizadas por ARCA', $modulo, $a, $id);
        elseif (($venta['estado'] == 'cerrado' || $venta['estado'] == 'anulado') && ($venta['idusuario'] != $_SESSION['usuario_idusuario'] || $venta['tipofacturacion'] == 'manual') && !$_SESSION['perfil_ventas_mod_todos'])
            ir_atras('No tiene permiso para modificar esta venta', $modulo, $a, $id);
        else {
            html_inicio(false, true);
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = 'ventas_mod';
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            marco_fin();
            html_cuerpo_fin();
            html_fin(['comprobantes', 'productos', 'afip']);
            logs();
        }
        break;

    case 'baja':
        if (!$id || !$venta = array_sql(consulta_sql("SELECT ventas.idusuario, ventas.estado AS estado, categorias_ventas.tipofacturacion AS tipofacturacion FROM ventas LEFT JOIN categorias_ventas ON ventas.idtipoventa=categorias_ventas.idtipoventa WHERE idventa = '$id' LIMIT 1")))
            ir_inicio($i18n_funciones['url_incorrecto'], $modulo, $a, $id);
        elseif (!$_SESSION['perfil_ventas_baja'])
            ir_atras('No tiene permiso para eliminar ventas', $modulo, $a, $id);
        elseif ($venta['estado'] == 'cerrado' && $venta['tipofacturacion'] == 'electronico')
            ir_atras('No se puede eliminar facturas electrónicas cerradas', $modulo, $a, $id);
        elseif (($venta['estado'] == 'cerrado' || $venta['estado'] == 'anulado') && $venta['idusuario'] != $_SESSION['usuario_idusuario'] && !$_SESSION['perfil_ventas_mod_todos'])
            ir_atras('No tiene permiso para eliminar esta venta', $modulo, $a, $id);
        else {
            $ventana = 'ventas_baja';
            include('ventanas/'.$ventana.'.php');
            logs();
        }
        break;

    case 'verpago':
    case 'verpagoindividual':
        if ($_SESSION['sistema_gratis'])
            ir_atras('No puede para accede a esta herramienta con el sistema gratuito', $modulo, $a, $id);
        elseif (!$id || !contar_sql(consulta_sql("SELECT idventapago FROM ventaspagos WHERE idventapago = '$id' LIMIT 1")))
            ir_inicio($i18n_funciones['url_incorrecto'], $modulo, $a, $id);
        else {
            if (contar_sql(consulta_sql("SELECT idtipoventa from ventasxclientes WHERE id = '$id' AND idtipoventa = '-1' LIMIT 1"))) {
                $temp_recibo_nombre = 'DP';
            } else {
                $temp_recibo_nombre = 'RP';
            }
            $nombre = $temp_recibo_nombre.completar_numero(campo_sql(consulta_sql("SELECT idnumeroventapago FROM ventaspagos WHERE idventapago = '$id' LIMIT 1")), 8);
            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = $a == 'verpago' ? 'ventaspagos_ver' : 'ventaspago_ver'; //Plurar - Singular: múltiples formas de pago
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            marco_fin();
            if ($_SESSION['modulo_archivos'] && $_SESSION['perfil_archivos_ver']) {
                $modulo = 'ventaspagos';
                include('sistemas/sistema_'.$_SESSION['sistema_idsistema'].'_idioma_'.$_SESSION['usuario_ididioma'].'/idiomas/idiomas_archivos.php');
                $ventana = 'archivos_listar';
                marco_inicio();
                include('ventanas/'.$ventana.'.php');
                marco_fin();
            }
            html_cuerpo_fin();
            html_fin();
            logs();
        }
        break;

    case 'altapago':
        if ($_SESSION['sistema_gratis'])
            ir_atras('No puede para accede a esta herramienta con el sistema gratuito', $modulo, $a, $id);
        elseif (!$_SESSION['perfil_ventaspagos_alta'])
            ir_atras('No tiene permiso para agregar pagos', $modulo, $a, $id);
        else {
            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = 'ventaspagos_altamod';
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            mensajes_efimeros();
            include('ventanas/'.$ventana.'_vista.php');
            marco_fin();
            html_cuerpo_fin();
            html_fin(
                array('comprobantes'), // Archivos JS para cargar
                array('seleccionar_tipo_caja') // Funciones JS para ejecutar en el onload
                );
            logs();
        }
        break;

    case 'modpago':
        if ($_SESSION['sistema_gratis'])
            ir_atras('No puede para accede a esta herramienta con el sistema gratuito', $modulo, $a, $id);
        elseif (!$id || !contar_sql(consulta_sql("SELECT idventapago FROM ventaspagos WHERE idventapago = '$id' LIMIT 1")))
            ir_inicio($i18n_funciones['url_incorrecto'], $modulo, $a, $id);
        elseif (!$_SESSION['perfil_ventaspagos_mod'])
            ir_atras('No tiene permiso para modificar pagos', $modulo, $a, $id);
        else {
            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = 'ventaspagos_altamod';
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            mensajes_efimeros();
            include('ventanas/'.$ventana.'_vista.php');
            marco_fin();
            html_cuerpo_fin();
            html_fin(
                array('comprobantes'), // Archivos JS para cargar
                array('seleccionar_tipo_caja') // Funciones JS para ejecutar en el onload
                );
            logs();
        }
        break;

    case 'bajapago':
        if ($_SESSION['sistema_gratis'])
            ir_atras('No puede para accede a esta herramienta con el sistema gratuito', $modulo, $a, $id);
        elseif (!$id || contar_sql(consulta_sql("SELECT idventapago FROM ventaspagos WHERE idventapago = '$id' LIMIT 1")) != 1)
            ir_inicio($i18n_funciones['url_incorrecto'], $modulo, $a, $id);
        elseif (!$_SESSION['perfil_ventaspagos_baja'])
            ir_atras('No tiene permiso para eliminar pagos', $modulo, $a, $id);
        else {
            $ventana = 'ventaspagos_baja';
            include('ventanas/'.$ventana.'.php');
            logs();
        }
        break;

    case 'importar';
        if (!$_SESSION['perfil_productos_herramientas'])
            ir_atras('No tiene permiso para utilizar las herramientas del módulo '.$modulo, $modulo, $a, $id);
        else {
            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $paso = recibir_variable('entero', 'paso', false);

            if ($boton == $i18n_funciones[80] && is_numeric($paso) && $paso >= 1 && $paso < 4) {
                $paso++;
            } elseif($boton == $i18n_funciones[26] && is_numeric($paso) && $paso > 1 && $paso <= 4) {
                $paso--;
            } else {
                $paso = 1;
            }
            $ventana = 'importar_'.$paso;
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            marco_fin();
            html_cuerpo_fin();
            html_fin();
            logs();
        }
        break;

    case 'exportar':
        if (!$_SESSION['perfil_ventas_herramientas'])
            ir_atras('No tiene permiso para utilizar las herramientas del módulo '.$modulo, $modulo, $a, $id);
        else
            $encoding = recibir_variable('texto', 'encoding', true);
            if($encoding){
                header('Location: '.URL_SCRIPTS.'/?script=exportar&modulo='.$modulo.'&encoding='.$encoding);
            } else {
                html_inicio();
                html_encabezado();
                html_menu();
                html_cuerpo();
                $ventana = 'exportar_csv';
                marco_inicio();
                include('ventanas/'.$ventana.'.php');
                marco_fin();
                html_cuerpo_fin();
                html_fin();
            }
            break;

    case 'txtafip':
        if ($_SESSION['sistema_gratis'])
            ir_atras('No puede para accede a esta herramienta con el sistema gratuito', $modulo, $a, $id);
        elseif (!$_SESSION['perfil_ventas_herramientas'])
            ir_atras('No tiene permiso para acceder a las herramientas', $modulo, $a, $id);
        else {
            $ventana = 'ventas_txtafip';
            // PARCHE: Dejo la descarga en la misma ventana, hasta hacer el sistema de procesos
            if (!isset($_GET['tipo'])) {
                html_inicio();
                html_encabezado();
                html_menu();
                html_cuerpo();
                marco_inicio();
            }
            include('ventanas/'.$ventana.'.php');
            if (!isset($_GET['tipo'])) {
                marco_fin();
                html_cuerpo_fin();
                html_fin();
                logs();
            }
        }
        break;

    case 'pedidos_ml':
        //ver validación
        if ($_SESSION['sistema_gratis'])
            ir_atras('No puede para accede a esta herramienta con el sistema gratuito', $modulo, $a, $id);
        elseif (!$_SESSION['perfil_ventas_herramientas'])
            ir_atras('No tiene permiso para acceder a las herramientas', $modulo, $a, $id);
        else {
            include('librerias/funciones_ml.php');
            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = 'ventas_pedidos_ml';
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            marco_fin();
            html_cuerpo_fin();
            html_fin(['comprobantes']);
            logs();
        }
        break;

    case 'pagos_mp':
        //ver validación
        if ($_SESSION['sistema_gratis'])
            ir_atras('No puede para accede a esta herramienta con el sistema gratuito', $modulo, $a, $id);
        elseif (!$_SESSION['perfil_ventas_herramientas'])
            ir_atras('No tiene permiso para acceder a las herramientas', $modulo, $a, $id);
        else {
            include('librerias/funciones_ml.php');
            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = 'ventas_pagos_mp';
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            marco_fin();
            html_cuerpo_fin();
            html_fin(['comprobantes']);
            logs();
        }
        break;
}
