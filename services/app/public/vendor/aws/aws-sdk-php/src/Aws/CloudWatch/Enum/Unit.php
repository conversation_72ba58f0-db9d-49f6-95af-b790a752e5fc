<?php
/**
 * Copyright 2010-2013 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 * http://aws.amazon.com/apache2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */

namespace Aws\CloudWatch\Enum;

use Aws\Common\Enum;

/**
 * Contains enumerable Unit values
 */
class Unit extends Enum
{
    const SECONDS = 'Seconds';
    const MICROSECONDS = 'Microseconds';
    const MILLISECONDS = 'Milliseconds';
    const BYTES = 'Bytes';
    const KILOBYTES = 'Kilobytes';
    const MEGABYTES = 'Megabytes';
    const GIGABYTES = 'Gigabytes';
    const TERABYTES = 'Terabytes';
    const BITS = 'Bits';
    const KILOBITS = 'Kilobits';
    const MEGABITS = 'Megabits';
    const GIGABITS = 'Gigabits';
    const TERABITS = 'Terabits';
    const PERCENT = 'Percent';
    const COUNT = 'Count';
    const BYTES_PER_SECOND = 'Bytes/Second';
    const KILOBYTES_PER_SECOND = 'Kilobytes/Second';
    const MEGABYTES_PER_SECOND = 'Megabytes/Second';
    const GIGABYTES_PER_SECOND = 'Gigabytes/Second';
    const TERABYTES_PER_SECOND = 'Terabytes/Second';
    const BITS_PER_SECOND = 'Bits/Second';
    const KILOBITS_PER_SECOND = 'Kilobits/Second';
    const MEGABITS_PER_SECOND = 'Megabits/Second';
    const GIGABITS_PER_SECOND = 'Gigabits/Second';
    const TERABITS_PER_SECOND = 'Terabits/Second';
    const COUNT_PER_SECOND = 'Count/Second';
    const NONE = 'None';
}
