{"name": "tecnickcom/tc-lib-barcode", "description": "PHP library to generate linear and bidimensional barcodes", "type": "library", "homepage": "http://www.tecnick.com", "license": "LGPL-3.0-or-later", "keywords": ["3 of 9", "ANSI MH10.8M-1983", "AZTEC", "barcode", "CBC", "CODABAR", "CODE 11", "CODE 128 A B C", "CODE 39", "CODE 93", "Datamatrix", "EAN 13", "EAN 8", "ECC200", "Intelligent Mail Barcode", "Interleaved 2 of 5", "ISO IEC 15438 2006", "ISO IEC 16022", "ISO IEC 24778 2008", "KIX", "<PERSON><PERSON>", "MSI", "Onecode", "PDF417", "PHARMACODE TWO-TRACKS", "PHARMACODE", "PLANET", "POSTNET", "QR-Code", "RMS4CC", "Royal Mail", "Standard 2 of 5", "tc-lib-barcode", "UPC-A", "UPC-E", "UPC", "USD-3", "USPS-B-3200", "USS-93"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "require": {"php": ">=5.6", "ext-bcmath": "*", "ext-date": "*", "ext-gd": "*", "ext-pcre": "*", "tecnickcom/tc-lib-color": "^1.14"}, "require-dev": {"pdepend/pdepend": "2.13.0", "phpmd/phpmd": "2.13.0", "phpunit/phpunit": "10.1.2 || 9.6.7 || 8.5.31 || 7.5.20 || 6.5.14 || 5.7.27 || 4.8.36", "squizlabs/php_codesniffer": "3.7.2 || 2.9.2"}, "autoload": {"psr-4": {"Com\\Tecnick\\Barcode\\": "src"}}, "autoload-dev": {"psr-4": {"Test\\": "test"}}}