name: check

env:
  XDEBUG_MODE: coverage

permissions:
  contents: read

on:
  push:
    branches:
      - 'main'
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - main

jobs:
    test-php:
        name: Test on php ${{ matrix.php-version }} and ${{ matrix.os }}
        runs-on: ${{ matrix.os }}
        continue-on-error: ${{ matrix.experimental }}
        strategy:
            fail-fast: false
            matrix:
                php-version: ["7.2", "7.3", "7.4", "8.0", "8.1", "8.2"]
                experimental: [false]
                os: [ubuntu-latest]
                coverage-extension: [pcov]
                include:
                    - { php-version: '5.6', experimental: false, os: ubuntu-latest, coverage-extension: 'xdebug' }
                    - { php-version: '7.1', experimental: false, os: ubuntu-latest, coverage-extension: 'xdebug' }
        steps:
            - uses: actions/checkout@v4
            - name: Use php ${{ matrix.php-version }}
              uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ matrix.php-version }}
                  coverage: ${{ matrix.coverage-extension }}
                  extensions: bcmath, curl, date, gd, hash, imagick, json, mbstring, openssl, pcre, zlib
                  ini-values: display_errors=on, error_reporting=-1, zend.assertions=1
            - name: List php modules
              run: php -m
            - name: List php modules using "no php ini" mode
              run: php -m -n
            - name: Cache module
              uses: actions/cache@v3
              with:
                  path: ~/.composer/cache/
                  key: composer-cache
            - name: Install dependencies
              run: make deps
            - name: Run all tests
              run: make qa
            - name: Send coverage
              uses: codecov/codecov-action@v3
              with:
                flags: php-${{ matrix.php-version }}-${{ matrix.os }}
                name: php-${{ matrix.php-version }}-${{ matrix.os }}
